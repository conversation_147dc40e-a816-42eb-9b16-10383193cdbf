#!/usr/bin/env node

/**
 * Test script to validate the performance monitoring implementation
 */

const fs = require('fs');
const path = require('path');

console.log('🎯 Testing Performance Monitoring Implementation...\n');

// Test 1: Verify PerformanceAnalytics exists and has required methods
function testPerformanceAnalyticsImplementation() {
    console.log('1. Testing PerformanceAnalytics Implementation...');
    
    const analyticsPath = path.join(__dirname, 'src/utils/performance/PerformanceAnalytics.ts');
    
    if (!fs.existsSync(analyticsPath)) {
        console.log('   ❌ PerformanceAnalytics.ts file not found');
        return false;
    }
    
    const content = fs.readFileSync(analyticsPath, 'utf8');
    
    const requiredMethods = [
        'startCycle',
        'endCycle',
        'startProvider',
        'endProvider',
        'recordProviderFetch',
        'recordProviderProcess',
        'recordProviderStore',
        'recordNetworkRequest',
        'recordProviderError',
        'generatePerformanceReport'
    ];
    
    const missingMethods = requiredMethods.filter(method => !content.includes(method));
    
    if (missingMethods.length === 0) {
        console.log('   ✅ PerformanceAnalytics has all required methods');
        return true;
    } else {
        console.log(`   ❌ Missing methods: ${missingMethods.join(', ')}`);
        return false;
    }
}

// Test 2: Verify NetworkTiming utilities exist
function testNetworkTimingImplementation() {
    console.log('2. Testing NetworkTiming Implementation...');
    
    const networkTimingPath = path.join(__dirname, 'src/utils/performance/NetworkTiming.ts');
    
    if (!fs.existsSync(networkTimingPath)) {
        console.log('   ❌ NetworkTiming.ts file not found');
        return false;
    }
    
    const content = fs.readFileSync(networkTimingPath, 'utf8');
    
    const requiredFeatures = [
        'NetworkTimer',
        'wrapUndiciRequest',
        'wrapCycleTLSRequest',
        'measureExecutionTime',
        'captureMemorySnapshot',
        'trackProxyPerformance'
    ];
    
    const missingFeatures = requiredFeatures.filter(feature => !content.includes(feature));
    
    if (missingFeatures.length === 0) {
        console.log('   ✅ NetworkTiming has all required utilities');
        return true;
    } else {
        console.log(`   ❌ Missing features: ${missingFeatures.join(', ')}`);
        return false;
    }
}

// Test 3: Verify main.ts integration
function testMainIntegration() {
    console.log('3. Testing main.ts Integration...');
    
    const mainPath = path.join(__dirname, 'src/main.ts');
    const content = fs.readFileSync(mainPath, 'utf8');
    
    const requiredIntegrations = [
        'PerformanceAnalytics',
        'performanceAnalytics.startCycle',
        'performanceAnalytics.endCycle',
        'performanceAnalytics.startProvider',
        'performanceAnalytics.endProvider',
        'measureExecutionTime',
        'captureMemorySnapshot'
    ];
    
    const missingIntegrations = requiredIntegrations.filter(integration => !content.includes(integration));
    
    if (missingIntegrations.length === 0) {
        console.log('   ✅ main.ts properly integrated with performance monitoring');
        return true;
    } else {
        console.log(`   ❌ Missing integrations: ${missingIntegrations.join(', ')}`);
        return false;
    }
}

// Test 4: Verify provider integration (BetMGM as example)
function testProviderIntegration() {
    console.log('4. Testing Provider Integration...');
    
    const betmgmPath = path.join(__dirname, 'src/providers/BetMGMProvider.ts');
    
    if (!fs.existsSync(betmgmPath)) {
        console.log('   ❌ BetMGMProvider.ts file not found');
        return false;
    }
    
    const content = fs.readFileSync(betmgmPath, 'utf8');
    
    const hasNetworkTiming = content.includes('wrapCycleTLSRequest') || content.includes('measureExecutionTime');
    const hasPerformanceImports = content.includes('performance/NetworkTiming');
    
    if (hasNetworkTiming && hasPerformanceImports) {
        console.log('   ✅ Provider integration implemented');
        return true;
    } else {
        console.log('   ❌ Provider integration missing or incomplete');
        return false;
    }
}

// Test 5: Verify configuration system
function testConfigurationSystem() {
    console.log('5. Testing Configuration System...');
    
    const configPath = path.join(__dirname, 'src/utils/performance/config.ts');
    
    if (!fs.existsSync(configPath)) {
        console.log('   ❌ config.ts file not found');
        return false;
    }
    
    const content = fs.readFileSync(configPath, 'utf8');
    
    const requiredFeatures = [
        'PerformanceConfig',
        'MonitoringLevel',
        'getPerformanceConfig',
        'defaultConfig',
        'developmentConfig',
        'productionConfig'
    ];
    
    const missingFeatures = requiredFeatures.filter(feature => !content.includes(feature));
    
    if (missingFeatures.length === 0) {
        console.log('   ✅ Configuration system implemented');
        return true;
    } else {
        console.log(`   ❌ Missing configuration features: ${missingFeatures.join(', ')}`);
        return false;
    }
}

// Test 6: Verify dashboard implementation
function testDashboardImplementation() {
    console.log('6. Testing Dashboard Implementation...');
    
    const dashboardPath = path.join(__dirname, 'src/utils/performance/Dashboard.ts');
    
    if (!fs.existsSync(dashboardPath)) {
        console.log('   ❌ Dashboard.ts file not found');
        return false;
    }
    
    const content = fs.readFileSync(dashboardPath, 'utf8');
    
    const requiredFeatures = [
        'PerformanceDashboard',
        'displayProgressBar',
        'displayProviderStatus',
        'displayCycleSummary',
        'displayTrendAnalysis',
        'displayAlerts'
    ];
    
    const missingFeatures = requiredFeatures.filter(feature => !content.includes(feature));
    
    if (missingFeatures.length === 0) {
        console.log('   ✅ Dashboard implementation complete');
        return true;
    } else {
        console.log(`   ❌ Missing dashboard features: ${missingFeatures.join(', ')}`);
        return false;
    }
}

// Test 7: Verify log directory structure
function testLogDirectoryStructure() {
    console.log('7. Testing Log Directory Structure...');
    
    const logDir = path.join(__dirname, 'logs');
    const performanceLogDir = path.join(logDir, 'performance');
    
    try {
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }
        
        if (!fs.existsSync(performanceLogDir)) {
            fs.mkdirSync(performanceLogDir, { recursive: true });
        }
        
        // Test write permissions
        const testFile = path.join(performanceLogDir, 'test.json');
        fs.writeFileSync(testFile, '{"test": true}');
        fs.unlinkSync(testFile);
        
        console.log('   ✅ Log directory structure created and writable');
        return true;
    } catch (error) {
        console.log(`   ❌ Log directory setup failed: ${error.message}`);
        return false;
    }
}

// Test 8: Verify TypeScript compilation
function testTypeScriptCompilation() {
    console.log('8. Testing TypeScript Compilation...');
    
    const { execSync } = require('child_process');
    
    try {
        // Check if TypeScript files compile without errors
        execSync('npx tsc --noEmit --skipLibCheck', { 
            stdio: 'pipe',
            cwd: __dirname 
        });
        
        console.log('   ✅ TypeScript compilation successful');
        return true;
    } catch (error) {
        console.log('   ❌ TypeScript compilation failed');
        console.log(`   Error: ${error.message}`);
        return false;
    }
}

// Test 9: Verify environment variable support
function testEnvironmentVariableSupport() {
    console.log('9. Testing Environment Variable Support...');
    
    const configPath = path.join(__dirname, 'src/utils/performance/config.ts');
    const content = fs.readFileSync(configPath, 'utf8');
    
    const envVars = [
        'PERFORMANCE_MONITORING_LEVEL',
        'PERFORMANCE_TARGET',
        'PERFORMANCE_LOG_DIRECTORY',
        'PERFORMANCE_ENABLE_REAL_TIME_DISPLAY'
    ];
    
    const missingEnvVars = envVars.filter(envVar => !content.includes(envVar));
    
    if (missingEnvVars.length === 0) {
        console.log('   ✅ Environment variable support implemented');
        return true;
    } else {
        console.log(`   ❌ Missing environment variables: ${missingEnvVars.join(', ')}`);
        return false;
    }
}

// Test 10: Verify performance data persistence
function testPerformanceDataPersistence() {
    console.log('10. Testing Performance Data Persistence...');
    
    const analyticsPath = path.join(__dirname, 'src/utils/performance/PerformanceAnalytics.ts');
    const content = fs.readFileSync(analyticsPath, 'utf8');
    
    const persistenceFeatures = [
        'logPerformanceReport',
        'historicalData',
        'writeFileSync',
        'JSON.stringify'
    ];
    
    const missingFeatures = persistenceFeatures.filter(feature => !content.includes(feature));
    
    if (missingFeatures.length === 0) {
        console.log('   ✅ Performance data persistence implemented');
        return true;
    } else {
        console.log(`   ❌ Missing persistence features: ${missingFeatures.join(', ')}`);
        return false;
    }
}

// Run all tests
async function runAllTests() {
    const results = [];
    
    results.push(testPerformanceAnalyticsImplementation());
    results.push(testNetworkTimingImplementation());
    results.push(testMainIntegration());
    results.push(testProviderIntegration());
    results.push(testConfigurationSystem());
    results.push(testDashboardImplementation());
    results.push(testLogDirectoryStructure());
    results.push(testTypeScriptCompilation());
    results.push(testEnvironmentVariableSupport());
    results.push(testPerformanceDataPersistence());
    
    const passedTests = results.filter(result => result).length;
    const totalTests = results.length;
    
    console.log('\n📊 Performance Monitoring Test Results:');
    console.log(`   Passed: ${passedTests}/${totalTests} tests`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 All performance monitoring features successfully implemented!');
        console.log('   ✅ Comprehensive performance analytics system ready');
        console.log('   ✅ Real-time monitoring and reporting enabled');
        console.log('   ✅ Network timing and resource tracking active');
        console.log('   ✅ Historical data persistence configured');
        console.log('   ✅ Configurable monitoring levels available');
        console.log('   ✅ Dashboard and alerting system operational');
        console.log('\n🚀 Ready to measure optimization effectiveness!');
    } else {
        console.log('\n⚠️  Some performance monitoring features need attention');
        console.log('   Review failed tests above');
    }
    
    return passedTests === totalTests;
}

// Execute tests
if (require.main === module) {
    runAllTests().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { runAllTests };
