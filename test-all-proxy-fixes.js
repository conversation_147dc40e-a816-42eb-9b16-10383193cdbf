#!/usr/bin/env node

// Test script to verify all proxy fixes
async function testAllProxyFixes() {
    console.log('🔧 Testing all proxy fixes...\n');
    
    const results = {
        BetRivers: { status: 'PENDING', projections: 0, duration: 0, error: null },
        Fliff: { status: 'PENDING', projections: 0, duration: 0, error: null },
        OwnersBox: { status: 'PENDING', projections: 0, duration: 0, error: null }
    };
    
    // Test BetRivers
    console.log('1️⃣ Testing BetRivers...');
    try {
        const { fetchBetRivers } = require('./dist/providers/BetRiversProvider');
        const startTime = Date.now();
        const projections = await fetchBetRivers();
        const duration = Date.now() - startTime;
        
        results.BetRivers = {
            status: projections && projections.length > 0 ? 'SUCCESS' : 'NO_DATA',
            projections: projections ? projections.length : 0,
            duration,
            error: null
        };
        console.log(`   ✅ BetRivers: ${results.BetRivers.projections} projections in ${duration}ms`);
    } catch (error) {
        results.BetRivers.error = error.message;
        results.BetRivers.status = error.message.includes('Proxy uri is mandatory') ? 'PROXY_ERROR' : 'OTHER_ERROR';
        console.log(`   ❌ BetRivers failed: ${error.message}`);
    }
    
    // Test Fliff
    console.log('\n2️⃣ Testing Fliff...');
    try {
        const { fetchFliff } = require('./dist/providers/Fliff');
        const startTime = Date.now();
        const projections = await fetchFliff();
        const duration = Date.now() - startTime;
        
        results.Fliff = {
            status: projections && projections.length > 0 ? 'SUCCESS' : 'NO_DATA',
            projections: projections ? projections.length : 0,
            duration,
            error: null
        };
        console.log(`   ✅ Fliff: ${results.Fliff.projections} projections in ${duration}ms`);
    } catch (error) {
        results.Fliff.error = error.message;
        results.Fliff.status = error.message.includes('Proxy uri is mandatory') ? 'PROXY_ERROR' : 'OTHER_ERROR';
        console.log(`   ❌ Fliff failed: ${error.message}`);
    }
    
    // Test OwnersBox
    console.log('\n3️⃣ Testing OwnersBox...');
    try {
        const { fetchOwnersBox } = require('./dist/providers/OwnersBox');
        const startTime = Date.now();
        const projections = await fetchOwnersBox();
        const duration = Date.now() - startTime;
        
        results.OwnersBox = {
            status: projections && projections.length > 0 ? 'SUCCESS' : 'NO_DATA',
            projections: projections ? projections.length : 0,
            duration,
            error: null
        };
        console.log(`   ✅ OwnersBox: ${results.OwnersBox.projections} projections in ${duration}ms`);
    } catch (error) {
        results.OwnersBox.error = error.message;
        results.OwnersBox.status = error.message.includes('Proxy uri is mandatory') ? 'PROXY_ERROR' : 'OTHER_ERROR';
        console.log(`   ❌ OwnersBox failed: ${error.message}`);
    }
    
    // Summary
    console.log('\n📊 SUMMARY:');
    console.log('='.repeat(50));
    
    let totalProjections = 0;
    let proxyErrorsFixed = 0;
    let successfulProviders = 0;
    
    for (const [provider, result] of Object.entries(results)) {
        const statusIcon = result.status === 'SUCCESS' ? '✅' : 
                          result.status === 'NO_DATA' ? '⚠️' : 
                          result.status === 'PROXY_ERROR' ? '🔴' : '❌';
        
        console.log(`${statusIcon} ${provider}: ${result.status} (${result.projections} projections, ${result.duration}ms)`);
        
        if (result.error && !result.error.includes('Proxy uri is mandatory')) {
            console.log(`   └─ Error: ${result.error}`);
        }
        
        totalProjections += result.projections;
        if (result.status !== 'PROXY_ERROR') proxyErrorsFixed++;
        if (result.status === 'SUCCESS') successfulProviders++;
    }
    
    console.log('='.repeat(50));
    console.log(`🎯 Total projections: ${totalProjections}`);
    console.log(`🔧 Proxy errors fixed: ${proxyErrorsFixed}/3 providers`);
    console.log(`✅ Successful providers: ${successfulProviders}/3`);
    
    if (proxyErrorsFixed === 3) {
        console.log('\n🎉 ALL PROXY ISSUES FIXED! ✨');
    } else {
        console.log('\n⚠️  Some proxy issues remain to be addressed.');
    }
}

// Run the test
testAllProxyFixes().then(() => {
    process.exit(0);
}).catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
});
