// Constants for league-specific stat type mappings
const leagueStatTypeMapping = {
    'NBA': {
        'Points': {
            mapping: {
                'Fanduel': 'Points'
            }
        },
        'Rebounds': {
            mapping: {
                'Fanduel': 'Rebounds'
            }
        },
        'Assists': {
            mapping: {
                'Fanduel': 'Assists'
            }
        },
        'Threes': {
            mapping: {
                'Fanduel': 'Three Point FG'
            }
        },
        'Blocks': {
            mapping: {
                'Fanduel': 'Blocks'
            }
        },
        'Steals': {
            mapping: {
                'Fanduel': 'Steals'
            }
        },
        'Turnovers': {
            mapping: {
                'Fanduel': 'Turnovers'
            }
        }
    },
    'WNBA': {
        'Points': {
            mapping: {
                'Fanduel': 'Points'
            }
        },
        'Rebounds': {
            mapping: {
                'Fanduel': 'Rebounds'
            }
        },
        'Assists': {
            mapping: {
                'Fanduel': 'Assists'
            }
        }
    },
    'NBA1Q': {
        'Points': {
            mapping: {
                'Fanduel': '1st Qtr Points'
            }
        },
        'Rebounds': {
            mapping: {
                'Fanduel': '1st Qtr Rebounds'
            }
        },
        'Assists': {
            mapping: {
                'Fanduel': '1st Qtr Assists'
            }
        }
    }
};

module.exports = {
    leagueStatTypeMapping
};
