const utils = {
    sleep(ms) {
      return new Promise(function (resolve) {
        return setTimeout(resolve, ms);
      });
    },
    nFormatter(num) {
      var numStr = num.toString(),
          decimalRegEx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  
      [
        { value: 1, letter: "" },
        { value: 1e3, letter: "k" },
        { value: 1e6, letter: "M" }
      ].forEach(function (item) {
        if (num >= item.value) {
          numStr = (num / item.value).toFixed(1).replace(decimalRegEx, "$1") + item.letter;
        }
      });
  
      if (numStr.includes(".") && !numStr.includes("k") && !numStr.includes("M")) {
        var parts = numStr.split(".");
        if (parts[parts.length - 1].length === 2) {
          numStr += "0";
        }
      }
  
      return numStr;
    },
    dump(obj) {
      try {
        return JSON.stringify(obj);
      } catch (err) {
        console.log("x in dump", err);
        console.trace();
        return "dump_error: " + obj;
      }
    },
    randomString(length) {
      var result = "",
          characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  
      for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
      }
  
      return result;
    },
    strTime() {
      return new Date(Date.now())
        .toISOString()
        .replace("T", " ")
        .replace("Z", "");
    },
    randomInt(min, max) {
      if (isNaN(min) || isNaN(max)) return 0;
      var rand = min - 0.5 + Math.random() * (max - min + 1);
      return Math.round(rand);
    },
    moneyToString(amount) {
      return Number(Math.round(amount * 100) / 100).toFixed(2);
    },
    nullToEmpty(str) {
      return str == null ? "" : str;
    },
    isPlatformAndroid() {
      return false;
    },
    isPlatformIos() {
      return false;
    },
    anythingToPositiveNumber(num) {
      var result = +num;
      if (isNaN(result)) result = 0;
      if (Math.floor(result) !== result) result = 0;
      if (result <= 0) result = 0;
      return result;
    },
    defnumber(val, defaultVal) {
      return val != null ? val : defaultVal;
    },
    getDeviceWidth() {
      return window.innerWidth;
    },
    getDeviceHeight() {
      return window.innerHeight;
    },
    convertStringArrayToSelectArray(arr) {
      return arr.map(function (item) {
        return {
          value: item,
          label: `${item}`
        };
      });
    },
    get_gold_coins_amount_update_step(currentAmount, previousAmount) {
      if (previousAmount == null) return currentAmount;
      var difference = currentAmount - previousAmount;
      return Math.round(difference / 100);
    },
    getReferralCustomerRestAmountToSpendInPercents(totalAmount, spentAmount) {
      var percent = Math.round((spentAmount / totalAmount) * 100) / 100;
      return isNaN(percent) || percent < 0 ? 0 : percent;
    },
    getNumberSuffix(number) {
      var j = number % 10,
          k = number % 100;
      if (j === 1 && k !== 11) {
        return "st";
      }
      if (j === 2 && k !== 12) {
        return "nd";
      }
      if (j === 3 && k !== 13) {
        return "rd";
      }
      return "th";
    }
  };
  
  module.exports = utils;
  