const { fetch, ProxyAgent } = require('undici');
const crypto = require('crypto');
const { getProxy } = require('../../utils/proxy');

function convertAmericanToDecimal(americanOdds) {
  if (americanOdds > 0) {
    return (americanOdds / 100) + 1;
  } else {
    return (100 / Math.abs(americanOdds)) + 1;
  }
}

function generateRandomString(length) {
  // Generate a random string of specified length using letters and digits
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

function generateDeviceId() {
  // Generate a random UUID and format it as ios.UUID
  const uuid = crypto.randomUUID().toUpperCase();
  return `ios.${uuid}`;
}

class FliffAPI {
  constructor() {
    this.deviceId = generateDeviceId();
    this.userProfile = 'fobj__sb_user_profile__685503';
    this.appInstallToken = generateRandomString(10);
    this.appXVersion = '5.3.5.229';
    this.authToken = 'fobj__sb_user_profile__685503';
    this.baseUrl = 'https://herald-2.app.getfliff.com/fc_mobile_api_public';
    this.headers = {
      'accept': 'application/json, text/plain, */*',
      'content-type': 'application/json',
      // 'baggage': 'sentry-environment=production,sentry-public_key=44cf74b044a14251a75b1194fb12c336,sentry-release=5.3.5,sentry-trace_id=0e36337cc81248fcbaa0a617e1f1f2e1',
      'user-agent': 'Fliff/1 CFNetwork/3826.400.120 Darwin/24.3.0',
      'accept-language': 'en-US,en;q=0.9',
      'accept-encoding': 'gzip, deflate, br',
      // 'sentry-trace': '0e36337cc81248fcbaa0a617e1f1f2e1-1f85400ebad949c3-0'
    };
    this.leagueMap = {
      MLB: 441,
      // NFL: 451,
      // NCAA: 452,
      NHL: 481,
      NBA: 461,
      NCAAB: 462,
      // CBA: 4752,
      // KBL: 4751,
    };
    this.timeout = 20000;
    this.maxConcurrency = 50; // Reduced from 50 to prevent overwhelming system
    this.proxyKey = 'Fliff';
    this.retryDelay = 500;
    this.maxRetries = 1;
    this.batchSize = 50; // Process events in batches
  }

  async makeRequest(url, data, attempt = 1) {
    const proxy = getProxy(this.proxyKey);
    const proxyAgent = new ProxyAgent(proxy);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          ...this.headers,
          'content-type': 'application/json'
        },
        body: JSON.stringify(data),
        dispatcher: proxyAgent,
        signal: AbortSignal.timeout(this.timeout)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const responseData = await response.json();
      return { data: responseData }; // Maintain compatibility with axios response structure
    } catch (error) {
      if ((error.name === 'TimeoutError' || error.message.includes('429')) && attempt < this.maxRetries) {
        const backoffDelay = this.retryDelay * Math.pow(2, attempt - 1);
        console.warn(`[FLIFF] Request attempt ${attempt} failed, retrying in ${backoffDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, backoffDelay));
        return this.makeRequest(url, data, attempt + 1);
      }

      console.error(`[FLIFF] Request failed with proxy ${proxy}:`, error.message);
      throw error;
    }
  }

  async getEventIds(leagueId) {
    const url = `${this.baseUrl}?device_x_id=${this.deviceId}&app_x_version=${this.appXVersion}&app_install_token=${this.appInstallToken}&auth_token=${this.userProfile}&conn_id=5&platform=prod`;
    
    const data = {
      header: {
        device_x_id: this.deviceId,
        app_x_version: this.appXVersion,
        app_install_token: this.appInstallToken,
        auth_token: this.userProfile,
        conn_id: 5,
        platform: 'prod',
      },
      invocation: {
        request: {
          __object_class_name: 'FCM__Public_Feed_Sync__Request',
          code: 3054,
          subfeed_meta: {
            revisions: [],
            packed_subfeed_revisions: [],
            focused_channel_id: leagueId,
            focused_conflict_fkey: '',
            focused_ticket_conflict_fkeys: [],
            focused_ticket_proposal_fkeys: [],
          },
        },
      },
      x_invocations: null,
      x_sb_meta: {
        sb_config_version: 1,
        sb_user_profile_version: -1,
        sb_user_profile_meta: null,
      },
    };

    try {
      const response = await this.makeRequest(url, data);
      const pre = response.data.x_slots.active_prematch_conflicts;
      const eventIds = pre.filter(p => p.channel_id === leagueId).map(p => ({
        conflict_fkey: p.conflict_fkey,
        away_team_name: p.away_team_name,
        home_team_name: p.home_team_name,
        event_start_time: new Date(p.event_start_timestamp_utc),
      }));

      return eventIds;
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        console.error('[FLIFF] Request timed out getting event IDs');
      } else {
        console.error('[FLIFF] Failed to get event IDs:', error);
      }
      return [];
    }
  }

  async fetchGameData(event, leagueName, retryCount = 2) {
    let delay = 500;

    for (let attempt = 0; attempt < retryCount; attempt++) {
      const url = `${this.baseUrl}?device_x_id=${this.deviceId}&app_x_version=${this.appXVersion}&app_install_token=${this.appInstallToken}&auth_token=${this.userProfile}&conn_id=5&platform=prod`;
      
      const data = {
        header: {
          device_x_id: this.deviceId,
          app_x_version: this.appXVersion,
          app_install_token: this.appInstallToken,
          auth_token: this.userProfile,
          conn_id: 5,
          platform: 'prod',
        },
        invocation: {
          request: {
            __object_class_name: 'FCM__Public_Feed_Sync__Request',
            subfeed_meta: {
              revisions: [],
              packed_subfeed_revisions: [],
              focused_channel_id: this.leagueMap[leagueName],
              focused_conflict_fkey: event.conflict_fkey,
              focused_ticket_conflict_fkeys: [],
              focused_ticket_proposal_fkeys: [],
            },
            code: 3061,
          },
        },
        x_invocations: null,
        x_sb_meta: {
          sb_config_version: 1,
          sb_user_profile_version: 3200,
          sb_user_profile_meta: {
            id_51202: 0,
            id_51203: 0,
            id_51204: 0,
            id_51207: 398634996,
            id_51206: 0,
            id_51221: 543799404,
            id_51231: 0,
            id_51232: 0,
            id_51241: 0,
            id_51250: 0,
            id_51251: 0,
            id_51252: 0,
            id_51253: 0,
            id_51254: 0,
            id_51255: 0,
          },
        },
      };

      try {
        const response = await this.makeRequest(url, data);
        const gameData = response.data;

        if (gameData.result && gameData.result.error) {
          const { error_code, error_message } = gameData.result.error;
          if (error_code === 40791 && error_message.toLowerCase().includes('cpu usage too high')) {
            console.warn(`[FLIFF] CPU usage too high for ${event.conflict_fkey}, attempt ${attempt + 1}`);
            await this.delay(delay);
            delay *= 0.5;
            continue;
          }
        }

        return this.extractProjections(gameData.x_slots.prematch_subfeeds_updates, leagueName, event);

      } catch (error) {
        const errorMessage = error.response?.data?.message || error.message;
        console.error(`[FLIFF] Error fetching ${event.conflict_fkey} (${attempt + 1}/${retryCount}): ${errorMessage}`);
        
        if (attempt < retryCount - 1) {
          await this.delay(delay);
          delay *= 0.5;
        }
      }
    }
    return [];
  }

  extractProjections(subfeeds, leagueName, event) {
    const projections = [];
    const uniqueStatTypes = new Set();

    subfeeds.forEach(subfeed => {
      subfeed.market_updates.forEach(marketUpdate => {
        if (!marketUpdate.is_active) return;

        // Get market status and name
        const marketStatus = marketUpdate.status;
        const marketName = marketUpdate.visual_name;
        const filtersIds = marketUpdate.filters_ids || [];
        
        // Skip markets with status 748
        if (marketStatus === 748) return;

        // Only process player props (filter 1616)
        if (!filtersIds.includes(1616)) return;

        marketUpdate.groups.forEach(group => {
          const proposals = group.proposals;
          if (proposals.length !== 2) return;

          // Skip if either proposal has status 748
          if (proposals.some(p => p.status === 748)) return;

          const overProposal = proposals.find(p => p.t_151_cell_text_1?.startsWith('Over'));
          const underProposal = proposals.find(p => p.t_151_cell_text_1?.startsWith('Under'));

          if (!overProposal || !underProposal) return;

          const t_131_market_name = proposals[0].t_131_market_name;
          const validTerms = [
            "pitcher", "batter", "player", "passing", "receiving",
            "rushing", "solo tackles", "assisted tackles",
            "field goal", "extra point", "kicking points",
            "longest", "sacks", "defensive"
          ];

          // Convert market name to lowercase for case-insensitive comparison
          const marketNameLower = t_131_market_name.toLowerCase();
          
          // Check if it matches any of our valid terms using includes (case-insensitive)
          if (!validTerms.some(term => marketNameLower.includes(term)) && !marketNameLower.includes('player')) {
            return;
          }
          
          const playerName = proposals[0].t_141_selection_name.split(/ Under| Over/)[0];

          // Convert American odds to decimal odds
          const overOddsAmerican = overProposal.coeff;
          const underOddsAmerican = underProposal.coeff;

          // Modify the league name if it's a first quarter bet
          let modifiedLeagueName = leagueName;
          if (marketNameLower.startsWith('player first quarter')) {
            modifiedLeagueName += '1Q';
          }
          
          // Create a normalized version of the stat type (lowercase with underscores)
          const normalizedStatType = t_131_market_name
            .toLowerCase()
            .replace(/\s+/g, '_')
            .replace(/[^a-z0-9_]/g, '');

          const projection = {
            proj_id: `${group.group_tag}_${normalizedStatType}`,
            league: modifiedLeagueName,
            player_name: playerName,
            stat_type: t_131_market_name,
            stat_type_normalized: normalizedStatType, // Add normalized version for consistent comparison
            line: parseFloat(proposals[0].t_151_cell_text_1.split(' ')[1]),
            over_odds_american: overOddsAmerican > 0 ? `+${overOddsAmerican}` : `${overOddsAmerican}`,
            under_odds_american: underOddsAmerican > 0 ? `+${underOddsAmerican}` : `${underOddsAmerican}`,
            over_odds_decimal: convertAmericanToDecimal(overOddsAmerican),
            under_odds_decimal: convertAmericanToDecimal(underOddsAmerican),
            matchup: proposals[0].t_121_event_info,
            start_time: event.event_start_time,
            source: 'Fliff'
          };

          projections.push(projection);
        });
      });
    });

    return projections;
  }

  async fetchFliff() {
    const startTime = Date.now();
    let totalEvents = 0;
    let processedEvents = 0;
    
    console.log('[FLIFF] Starting fetch...');

    try {
      // First, get all event IDs in parallel
      const leaguePromises = Object.entries(this.leagueMap).map(async ([leagueName, leagueId]) => {
        const eventIds = await this.getEventIds(leagueId);
        return { leagueName, eventIds };
      });

      const leagueResults = await Promise.all(leaguePromises);
      
      // Calculate total events
      totalEvents = leagueResults.reduce((sum, { eventIds }) => sum + eventIds.length, 0);
      console.log(`[FLIFF] Found ${totalEvents} total events`);

      // Process all events with progress tracking
      const allProjections = [];
      for (const { leagueName, eventIds } of leagueResults) {
        if (eventIds.length === 0) continue;

        // console.log(`[FLIFF] Processing ${eventIds.length} events for ${leagueName}`);
        const projections = await this.processEventsWithConcurrency(
          eventIds, 
          leagueName, 
          this.maxConcurrency,
          (processed) => {
            processedEvents += processed;
            const progress = ((processedEvents / totalEvents) * 100).toFixed(1);
            // console.log(`[FLIFF] Progress: ${progress}% (${processedEvents}/${totalEvents})`);
          }
        );
        allProjections.push(...projections);
      }

      const endTime = Date.now();
      const duration = (endTime - startTime) / 1000;
      console.log(`[FLIFF] Fetch completed in ${duration.toFixed(1)}s`);
      console.log(`[FLIFF] Total projections: ${allProjections.length}`);

      return allProjections;
    } catch (error) {
      console.error('[FLIFF] Error in fetchFliff:', error);
      return [];
    }
  }

  async processEventsWithConcurrency(events, leagueName, concurrency = this.maxConcurrency, onProgress) {
    // Split events into batches for better memory management
    const batches = [];
    for (let i = 0; i < events.length; i += this.batchSize) {
      batches.push(events.slice(i, i + this.batchSize));
    }

    let results = [];
    let failedEvents = [];

    // Process each batch
    for (const batch of batches) {
      const chunks = [];
      for (let i = 0; i < batch.length; i += concurrency) {
        chunks.push(batch.slice(i, i + concurrency));
      }

      for (const chunk of chunks) {
        try {
          const chunkResults = await Promise.allSettled(
            chunk.map(event => this.fetchGameData(event, leagueName))
          );

          // Process successful results
          results = results.concat(
            chunkResults
              .filter(result => result.status === 'fulfilled' && result.value)
              .map(result => result.value)
          );

          // Track failed events for retry
          failedEvents = failedEvents.concat(
            chunk.filter((_, index) => chunkResults[index].status === 'rejected')
          );

          if (onProgress) {
            onProgress(chunk.length);
          }

          // Adaptive delay based on failure rate
          const failureRate = chunkResults.filter(r => r.status === 'rejected').length / chunk.length;
          const delay = Math.min(5000, Math.max(1000, failureRate * 10000));
          await this.delay(delay);
        } catch (error) {
          console.error(`[FLIFF] Chunk processing failed:`, error.message);
          failedEvents = failedEvents.concat(chunk);
          await this.delay(2000); // Delay on error
        }
      }

      // Small delay between batches to prevent overwhelming the system
      await this.delay(2000);
    }

    // Retry failed events with increased delay and reduced concurrency
    if (failedEvents.length > 0) {
      console.warn(`[FLIFF] Retrying ${failedEvents.length} failed events...`);
      await this.delay(5000); // Longer delay before retries
      
      // Process retries with reduced concurrency
      const retryChunks = [];
      for (let i = 0; i < failedEvents.length; i += Math.max(2, concurrency / 2)) {
        retryChunks.push(failedEvents.slice(i, i + Math.max(2, concurrency / 2)));
      }

      for (const chunk of retryChunks) {
        const retryResults = await Promise.allSettled(
          chunk.map(event => this.fetchGameData(event, leagueName, 3))
        );

        results = results.concat(
          retryResults
            .filter(result => result.status === 'fulfilled' && result.value)
            .map(result => result.value)
        );

        await this.delay(2000); // Delay between retry chunks
      }
    }

    return results.flat();
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = FliffAPI;