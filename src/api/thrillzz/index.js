const WebSocket = require('ws');
const { ProxyAgent } = require('undici');
const fs = require('fs');
const path = require('path');
const pLimit = require('p-limit');

function convertToAmericanOdds(decimalOdds) {
    if (decimalOdds >= 2) {
        return `+${Math.round((decimalOdds - 1) * 100)}`;
    } else {
        return `${Math.round(-100 / (decimalOdds - 1))}`;
    }
}

class ThrillzzApi {
    constructor() {
        this.url = 'wss://sc.thrillzsystem.com/ws';
        this.headers = {
            'Pragma': 'no-cache',
            'Origin': 'https://app.thrillzz.com',
            'Accept-Language': 'en-US,en;q=0.9,la;q=0.8,en-GB;q=0.7',
            'Sec-WebSocket-Key': 'jUp/tNEv5shFxD1hvDQRKw==',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'Upgrade': 'websocket',
            'Cache-Control': 'no-cache',
            'Connection': 'Upgrade',
            'Sec-WebSocket-Version': '13',
            'Sec-WebSocket-Extensions': 'permessage-deflate; client_max_window_bits'
        };

        this.leagues = {
            MLB: 183,
            // Add more leagues as needed
        };

        this.ws = null;
        this.eventQueue = [];
        this.projections = [];
        this.resolve = null;
        this.completedEvents = 0;
        this.totalEvents = 0;

        // Load proxies from file
        this.proxies = this.loadProxies();
    }

    loadProxies() {
        const proxiesPath = path.resolve(__dirname, '../../proxies/proxies.txt');
        const proxies = fs.readFileSync(proxiesPath, 'utf-8')
            .trim()
            .split('\n')
            .map(proxy => proxy.trim());
        return proxies;
    }

    getRandomProxy() {
        const randomIndex = Math.floor(Math.random() * this.proxies.length);
        const [host, port, username, password] = this.proxies[randomIndex].split(':');
        return { host, port, username, password };
    }

    connect() {
        return new Promise((resolve) => {
            this.resolve = resolve;

            // First, get the list of events for each league
            const leaguePromises = [];

            for (let leagueName in this.leagues) {
                const leagueId = this.leagues[leagueName];
                leaguePromises.push(this.getLeagueEvents(leagueId, leagueName));
            }

            Promise.all(leaguePromises).then(() => {
                // Once we have all events, process them in parallel
                this.processEventsInParallel();
            }).catch(error => {
                console.error('[THRILLZZ] Error getting league events:', error);
                resolve([]);
            });
        });
    }

    getLeagueEvents(leagueId, leagueName) {
        return new Promise((resolve, reject) => {
            const proxy = this.getRandomProxy();
            const proxyUrl = `http://${proxy.username}:${proxy.password}@${proxy.host}:${proxy.port}`;
            const agent = new ProxyAgent(proxyUrl);

            const ws = new WebSocket(this.url, { headers: this.headers, dispatcher: agent });

            ws.on('open', () => {
                const initialMessage = {
                    type: "subscribeLive",
                    data: {
                        subscribe: true,
                        type: 4,
                        isLive: false,
                        id: leagueId
                    }
                };
                ws.send(JSON.stringify(initialMessage));
            });

            ws.on('message', (data) => {
                try {
                    if (Buffer.isBuffer(data)) {
                        const message = data.toString('utf-8');
                        const jsonData = JSON.parse(message);

                        const events = jsonData.data.payload.map(event => ({
                            eventName: event.name,
                            eventId: event.id,
                            startTime: new Date(event.date),
                            leagueName: leagueName
                        }));

                        this.eventQueue.push(...events);
                        ws.close();
                        resolve();
                    }
                } catch (err) {
                    console.error(`[THRILLZZ] Error getting events for ${leagueName}:`, err);
                    ws.close();
                    reject(err);
                }
            });

            ws.on('error', (error) => {
                console.error(`[THRILLZZ] WebSocket error for ${leagueName}:`, error);
                reject(error);
            });
        });
    }

    processEventsInParallel() {
        if (this.eventQueue.length === 0) {
            this.resolve([]);
            return;
        }

        this.totalEvents = this.eventQueue.length;
        console.log(`[THRILLZZ] Processing ${this.totalEvents} events in parallel`);

        // Create a concurrency limit for WebSocket connections
        const limit = pLimit(10); // Process up to 10 events simultaneously

        const eventPromises = this.eventQueue.map(event =>
            limit(() => this.processEvent(event))
        );

        Promise.allSettled(eventPromises).then(() => {
            console.log(`[THRILLZZ] Completed processing all events. Total projections: ${this.projections.length}`);
            this.resolve(this.projections);
        });
    }

    processEvent(event) {
        return new Promise((resolve, reject) => {
            const proxy = this.getRandomProxy();
            const proxyUrl = `http://${proxy.username}:${proxy.password}@${proxy.host}:${proxy.port}`;
            const agent = new ProxyAgent(proxyUrl);

            const ws = new WebSocket(this.url, { headers: this.headers, dispatcher: agent });

            ws.on('open', () => {
                const eventSubscriptionMessage = {
                    type: "subscribeLive",
                    data: {
                        subscribe: true,
                        type: 1,
                        isLive: false,
                        id: event.eventId
                    }
                };
                ws.send(JSON.stringify(eventSubscriptionMessage));
            });

            ws.on('message', (data) => {
                try {
                    if (Buffer.isBuffer(data)) {
                        const message = data.toString('utf-8');
                        const jsonData = JSON.parse(message);

                        const eventProjections = this.extractProjections(
                            jsonData.data.payload,
                            event.leagueName,
                            event.eventName,
                            event.startTime
                        );

                        this.projections.push(...eventProjections);
                        ws.close();
                        resolve();
                    }
                } catch (err) {
                    console.error(`[THRILLZZ] Error processing event ${event.eventId}:`, err);
                    ws.close();
                    resolve(); // Resolve anyway to not block other events
                }
            });

            ws.on('error', (error) => {
                console.error(`[THRILLZZ] WebSocket error for event ${event.eventId}:`, error);
                resolve(); // Resolve anyway to not block other events
            });

            // Add timeout to prevent hanging
            setTimeout(() => {
                if (ws.readyState === WebSocket.OPEN) {
                    ws.close();
                    resolve();
                }
            }, 30000); // 30 second timeout
        });
    }



    extractProjections(eventData, leagueName, eventName, startTime) {
        const projections = [];
    
        for (const marketId in eventData.markets) {
            const market = eventData.markets[marketId];
    
            // Filter the markets that start with "Under/Over Player" or "Under/Over Pitcher"
            if (market.name.startsWith('Under/Over Player') || market.name.startsWith('Under/Over Pitcher')) {
                const playerSelections = {};
    
                for (const selectionId in market.selections) {
                    const selection = market.selections[selectionId];
    
                    // Only include selections that meet the criteria
                    if (!selection.isSuspended && selection.isVisible && !market.isLiveMarket) {
                        const playerName = selection.playerName;
                        const line = selection.line;
    
                        if (!playerSelections[playerName]) {
                            playerSelections[playerName] = [];
                        }
    
                        playerSelections[playerName].push(selection);
                    }
                }
    
                // Check if we have both over and under for each player
                for (const playerName in playerSelections) {
                    const selections = playerSelections[playerName];
    
                    if (selections.length === 2) {
                        const overSelection = selections.find(s => s.n === "Over");
                        const underSelection = selections.find(s => s.n === "Under");
    
                        // Ensure both selections have the same baseline and player name
                        if (overSelection && underSelection && overSelection.line === underSelection.line && overSelection.playerName === underSelection.playerName) {
                            const projection = {
                                proj_id: `${overSelection.id}_${underSelection.id}`,
                                league: leagueName,
                                player_name: playerName,
                                stat_type: market.name,
                                line: parseFloat(overSelection.line),
                                over_odds_decimal: overSelection.price,
                                under_odds_decimal: underSelection.price,
                                over_odds_american: convertToAmericanOdds(overSelection.price),
                                under_odds_american: convertToAmericanOdds(underSelection.price),
                                matchup: eventName,
                                start_time: startTime,
                                source: 'Thrillzz'
                            };
    
                            projections.push(projection);
                        }
                    }
                }
            }
        }
    
        return projections;
    }    


}

module.exports = ThrillzzApi;
