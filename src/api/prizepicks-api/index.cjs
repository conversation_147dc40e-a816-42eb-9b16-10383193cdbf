const { join } = require('path');
const fs = require('fs');
const path = require('path');
const { getProxy } = require('../../utils/proxy');

// Sleep function
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Import the createTlsClient function from the tlsclient-wrapper.js
async function createClient() {
    try {
        const { createTlsClient } = await import('./tlsclient-wrapper.js');
        return createTlsClient();
    } catch (error) {
        console.error('Error loading tlsclient-wrapper.js:', error);
        throw error;
    }
}

// Headers
const headers = {
    "sec-ch-ua": "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"",
    "sec-ch-ua-mobile": "?0",
    //"sec-ch-ua-platform": "\"Windows\"",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Mobile/15E148 Safari/604.1",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "sec-fetch-site": "none",
    "sec-fetch-mode": "navigate",
    "sec-fetch-user": "?1",
    "sec-fetch-dest": "document",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "de-DE,de;q=0.9",
    "priority": "u=0, i"
};

class PrizePicksAPI {
    constructor(proxyKey = 'PrizePicks') {
        this.proxyKey = proxyKey;
    }

    async fetchPrizePicks() {
        let client;
        try {
            client = await createClient();
            let proxyUrl = getProxy(this.proxyKey);

            while (true) {
                const response = await this.makeRequest(client, proxyUrl, headers);

                if (response && response.body.substring(0, 30) === '{"data":[{"type":"projection",') {
                    //console.log("Got Valid Response: ", response.body.substring(0, 30));
                    //await sleep(500);
                    return transformProjections(JSON.parse(response.body));
                } else {
                    /*
                    console.log('Reinitializing client and proxy... : ', response?.body?.substring(0, 30));
                    
                    client = await createClient();
                    proxyUrl = getProxy(this.proxyKey);
                    */
                    await client.terminate();
                    return [];
                }
            }
        } catch (error) {
            console.error('Error fetching PrizePicks projections:', error);
            throw error;
        } finally {
            if (client) {
                await client.terminate();
            }
        }
    }

    async makeRequest(client, proxyUrl, headers) {
        try {
            const proxy = getProxy(this.proxyKey);
            const response = await client.get("https://api.prizepicks.com/projections?per_page=9999", {
                headers,
                proxyUrl: this.proxy || proxy
            });
            return response;
        } catch (error) {
            console.error(`[PRIZEPICKS] Request error: ${error}`);
            return null;
        }
    }
}

// Utility functions
function transformProjections(responseBody) {
    if (!responseBody || !Array.isArray(responseBody.data) || !Array.isArray(responseBody.included)) {
        console.log('Invalid response body structure');
        return [];
    }

    //console.log(`Total projections before filtering: ${responseBody.data.length}`);

    const allowedLeagues = ['PGA', 'CRICKET', 'NHL', 'DARTS', 'SOCCER', 'EURO', 'MLB', 'WNBA', 'LAX', 'TENNIS', 'MMA', 'OBBALL', 'OGOLF', 'NFL', 'CFB', 'NBA', 'NBA1Q', 'CBB'];

    const filteredProjections = responseBody.data
        .filter(proj => {
            if (proj.attributes.odds_type !== 'standard') {
                //console.log(`Filtered out projection ${proj.id}: non-standard odds_type - ${proj.attributes.odds_type}`);
                return false;
            }

            const playerInfo = getPlayerInfo(responseBody.included, proj.relationships.new_player.data.id);

            if (proj.attributes.is_promo !== false) {
                //console.log(`Filtered out projection ${proj.id}: is_promo - ${proj.attributes.is_promo}`);
                return false;
            }

            if (!allowedLeagues.includes(playerInfo.league)) {
                //console.log(`Filtered out projection ${proj.id}: league not allowed - ${playerInfo.league}`);
                return false;
            }

            if (playerInfo.combo !== false) {
                //console.log(`Filtered out projection ${proj.id}: combo not false - ${playerInfo.combo}`);
                return false;
            }

            return true;
        });

    //console.log(`Projections after filtering: ${filteredProjections.length}`);

    return filteredProjections.map(proj => {
        const playerInfo = getPlayerInfo(responseBody.included, proj.relationships.new_player.data.id);
        const startTime = new Date(proj.attributes.start_time);

        let stat_type = proj.attributes.stat_type;
        let league = playerInfo.league;

        if (league === 'DARTS' && stat_type === "180's Thrown") {
            stat_type = 'Player 180s';
        }

        if (['PGA', 'LIVGOLF', 'LPGA', 'EUROGOLF', 'OGOLF'].includes(league)) {
            league = 'GOLF';
        }

        if (league === 'HRDERBY' && ['Round 1', 'Round 2', 'Round 3'].includes(proj.attributes.description)) {
            stat_type = `${stat_type} ${proj.attributes.description}`;
        }

        // Update for NFL1Q league
        if (league === 'NFL1Q') {
            league = 'NFL';
            stat_type = `1Q ${stat_type}`;
        }
        // Update for NFL1Q league
        if (league === 'CBB') {
            league = 'NCAAB';
        }

        return {
            proj_id: proj.id,
            league: league,
            player_name: playerInfo.name.trim(),
            team: playerInfo.team,
            stat_type: stat_type,
            line: proj.attributes.line_score,
            over_odds_american: '-119',
            under_odds_american: '-119',
            over_odds_decimal: 1.84,
            under_odds_decimal: 1.84,
            matchup: `${playerInfo.team} vs. ${proj.attributes.description}`,
            start_time: startTime,
            source: 'PrizePicks'
        };
    });
}

function getPlayerInfo(included, playerId) {
    const player = included.find(item => item.type === 'new_player' && item.id === playerId);
    if (!player) {
        console.log(`Player not found for id: ${playerId}`);
        return { league: '', name: '', team: '', combo: true };
    }
    return {
        league: player.attributes.league,
        name: player.attributes.display_name || player.attributes.name,
        team: player.attributes.team,
        combo: player.attributes.combo
    };
}

// Export both the PrizePicksAPI class and the example usage function
module.exports = {
    PrizePicksAPI
};