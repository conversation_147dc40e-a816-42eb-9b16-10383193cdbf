const fs = require('fs');

function loadProxies(filePath) {
    const data = fs.readFileSync(filePath, 'utf8');

    return data.split('\n').map(line => line.trim()).filter(line => {
        const parts = line.split(':');
        return parts.length === 4;
    }).map(proxy => {
        const [host, port, username, password] = proxy.split(':');
        return `http://${username}:${password}@${host}:${port}`;
    });
}

module.exports = loadProxies;