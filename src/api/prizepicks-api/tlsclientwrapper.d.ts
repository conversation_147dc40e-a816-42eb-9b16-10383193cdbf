declare module 'tlsclientwrapper' {
    interface TlsClientOptions {
        tlsClientIdentifier: string;
        timeoutSeconds?: number;
        streamOutputBlockSize?: number | null;
        streamOutputEOFSymbol?: string | null;
        streamOutputPath?: string | null;
    }

    class TlsClient {
        constructor(options: TlsClientOptions);
        get(url: string, options?: { headers?: Record<string, string>; proxyUrl?: string }): Promise<{ body: string }>;
        terminate(): Promise<void>;
    }

    export = TlsClient;
}