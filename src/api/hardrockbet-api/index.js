//#region CycleTLS Fetch
const initCycleTLS = require("cycletls");

/**
 * Fetch function that wraps around CycleTLS, some fields & functions might be missing
 * @param {*} url 
 * @param {*} opts 
 */
async function fetch(url, opts) {
  const cycleTLS = await initCycleTLS();

  const maxRetries = 3;
  const retryDelay = 1000; // Initial retry delay in milliseconds

  for (let retry = 0; retry < maxRetries; retry++) {
    try {
      const res = await cycleTLS(url, {
        headers: opts?.headers ?? {},
        cookies: opts?.cookies ?? {},
        body: opts?.body ?? "",
        ja3: opts?.ja3 ?? '771,4865-4867-4866-49195-49199-52393-52392-49196-49200-49162-49161-49171-49172-156-157-47-53-10,0-23-65281-10-11-35-16-5-51-43-13-45-28-21,29-23-24-25-256-257,0',
        userAgent: opts?.userAgent ?? 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:92.0) Gecko/20100101 Firefox/92.0',
        proxy: opts?.proxy ?? undefined,
      }, opts.method ?? "GET");

      // Add fields and functions we use!
      res.ok = (res.status >= 200 && res.status < 400);
      res.text = async () => {
        return res.body;
      };
      res.json = async () => {
        return res.body;
      };

      return res;
    } catch (error) {
      if (error.response && error.response.status >= 500) {
        console.error(`Server error (${error.response.status}) during fetch request. Retry attempt: ${retry + 1}`, error);

        if (retry < maxRetries - 1) {
          // Exponential backoff: Wait before retrying
          const backoffDelay = retryDelay * Math.pow(2, retry);
          await new Promise(resolve => setTimeout(resolve, backoffDelay));
        } else {
          // Max retries reached, handle the error or fallback
          console.error("Max retries reached. Unable to fetch data.");
          // Return a default value or an error object
          return null;
        }
      } else {
        console.error(`Error during fetch request:`, error);
        // Return a default value or an error object
        return null;
      }
    }
  }

  cycleTLS.exit();
}

const WebSocket = require("ws");
let { HttpsProxyAgent } = require('https-proxy-agent');

/**
 * Hard Rock Bet API 
 */
class HardRockBetAPI {
  //#region Variables
  proxy;
  //#endregion

  /**
   * Create HardRock Bet API instance
   * @param {string} proxy Proxy connection string, ex: `http://user:<EMAIL>:1234`
   */
  constructor(proxy) {
    // Set proxy
    this.proxy = proxy;
  }

  //#region Hard Rock Bet
  /**
   * Get all sports competition data
   * This needs to be called to get the competition id's that will give us the details per sports event
   */
  async getSportsCompetitions(includeCompetitionEvents = true) {
    /**
     * GraphQL payload
     */
    const payload = {
      "operationName": "betSync", "query": `query betSync(
  $locale: String
  $region: String
  $segment: String
  $language: String
  $nonTradingFilters: [NodeFilterType]
  $channel: String
) { 
  betSync(
    locale: $locale
    channel: $channel
    cmsSegment: $segment
    region: $region
    language: $language
  ) {  
    sports(filters: $nonTradingFilters) {
      id
      code
      numEvents
      numEventsToday
      numEventsInplay
      numEventsDisplayed
      numOutrightEvents
      weighting
      groups {
        name
        displayOrder
        types
      }
      keyMarkets {
        type
      }
      categories(filters: $nonTradingFilters) {
        id
        name
        numEvents
        numEventsInplay
        numEventsDisplayed
        weighting
        competitions(filters: $nonTradingFilters) {
          id
          name
          highlight
          weighting
          numEvents
          numOutrightEvents
          numEventsDisplayed
          numEventsInplay
          numEventsToday
          streaming
          streamingAvailable
          propParlay
          events {
            count
          }
        }
      }
      events {${(includeCompetitionEvents ? `
      data {
  id
  compId
  compName
  betradarId
  simplebetTrackerId
  globalAtsId
  eventTime
  sport
  inplay
  outright
  numMarkets
  name
  displayed
  displayedGroups(channel: $channel) {
    name
  }
  path
  sgpEnabled
  sgpSelectionLimit
  streamingGeos
  streamingAvailable
  streaming
  trackerStatus
  trackerId
  propParlay
  
}` : "")}
        count
      }
    }
  }
}
`, "variables": {
        "channel": "NEW_JERSEY_ONLINE",
        "segment": "nj",
        "region": "us",
        "language": "enus",
        "nonTradingFilters": [
          "DISPLAYED"
        ]
      }
    };

    /**
     * Send request
     */
    try {
      const res = await fetch("https://api.hardrocksportsbook.com/java-graphql/graphql?type=event_tree", {
        "credentials": "omit",
        "headers": {
          "Accept": "application/json",
          "Accept-Language": "en-US,en;q=0.5",
          "content-type": "application/json",
          "Sec-Fetch-Dest": "empty",
          "Sec-Fetch-Mode": "cors",
          "Sec-Fetch-Site": "cross-site",
          "Pragma": "no-cache",
          "Cache-Control": "no-cache"
        },
        "referrer": "https://app.hardrock.bet/",
        "body": JSON.stringify(payload),
        "method": "POST",
        "mode": "cors",
        proxy: this.proxy
      });

      /**
       * Get result
       */
      const data = await res.json();

      /**
       * Return data
       */
      return data;
    } catch (error) {
      console.error(`[HardRockBet] Error fetching sports competitions: ${error.message}`);
      return null;
    }
  }

  /**
   * Get all sport events data for a competition
   * This 
   * @param {*} competitionId 
   */
  async getCompetitionEvents(competitionId) {
    /**
   * GraphQL payload
   */
    const payload = {
      "operationName": "betSync",
      "query": `query betSync(
  $filters: [Filter]
  $segment: String
  $region: String
  $language: String
  $search: String
  $slice: Interval
  $timeInterval: Interval
  $channel: String
  $random: Int
  $marketTypes: [String]
  $sports: [String]
  $sort: Sort
) {
  betSync(cmsSegment: $segment, region: $region, language: $language, channel: $channel, eventParams: {
    random: $random
    marketTypes: $marketTypes
    sportList: $sports
  }) {
    events(filters: $filters, slice: $slice, timeInterval: $timeInterval, search: $search, sports: $sports, sort: $sort) {
      data {
  id
  compId
  compName
  betradarId
  simplebetTrackerId
  globalAtsId
  eventTime
  sport
  inplay
  outright
  numMarkets
  name
  displayed
  displayedGroups(channel: $channel) {
    name
  }
  path
  sgpEnabled
  sgpSelectionLimit
  streamingGeos
  streamingAvailable
  streaming
  trackerStatus
  trackerId
  propParlay
  
}
      count
    }
  }
}
`,
      "variables": {
        "channel": "NEW_JERSEY_ONLINE",
        "segment": "nj",
        "region": "us",
        "language": "enus",
        "filters": [{
          "field": "compId",
          "values": `${competitionId}`
        }, {
          "field": "outright", "value": "false"
        }]
      }
    };

    /**
     * Send request
     */
    try {
      const res = await fetch("https://api.hardrocksportsbook.com/java-graphql/graphql", {
        "credentials": "omit",
        "headers": {
          "Accept": "application/json",
          "Accept-Language": "en-US,en;q=0.5",
          "content-type": "application/json",
          "Sec-Fetch-Dest": "empty",
          "Sec-Fetch-Mode": "cors",
          "Sec-Fetch-Site": "cross-site",
          "Pragma": "no-cache",
          "Cache-Control": "no-cache"
        },
        "referrer": "https://app.hardrock.bet/",
        "body": JSON.stringify(payload),
        "method": "POST",
        "mode": "cors",
        proxy: this.proxy
      });

      /**
       * Get result
       */
      const data = await res.text();

      /**
       * Return data
       */
      return data;
    } catch (error) {
      console.error(`[HardRockBet] Error fetching competition events for competitionId ${competitionId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get the Root Ladder, this is used to look up the odds for the player props etc.
   * You should cache this object to save on requests!
   */
  async getRootLadder() {
    try {
      const res = await fetch("https://api-va.hardrocksportsbook.com/sportsbook/v1/api/getRootLadder", {
        "credentials": "omit",
        "headers": {
          "Accept": "application/json",
          "Accept-Language": "en-US,en;q=0.5",
          "content-type": "application/json",
          "Sec-Fetch-Dest": "empty",
          "Sec-Fetch-Mode": "cors",
          "Sec-Fetch-Site": "cross-site",
          "Pragma": "no-cache",
          "Cache-Control": "no-cache"
        },
        "referrer": "https://app.hardrock.bet/",
        "method": "GET",
        "mode": "cors",
        proxy: this.proxy
      });

      /**
       * Get result
       */
      const data = await res.text();

      /**
       * Return data
       */
      return data;
    } catch (error) {
      console.error(`[HardRockBet] Error fetching root ladder: ${error.message}`);
      return null;
    }
  }

  /**
   * Get player props for event
   */
  async getPlayerProps(eventIds) {
    try {
      // Ensure eventId is an array
      if (!Array.isArray(eventIds)) eventIds = [eventIds];

      // Connect via websocket
      const proxyAgent = this.proxy ? new HttpsProxyAgent(this.proxy) : undefined;

      // Initiate the WebSocket connection
      let socket = new WebSocket("wss://api.hardrocksportsbook.com/graphql-ws", { agent: proxyAgent });

      /**
       * Map containing the data responses for each event id
       */
      let eventIdData = {};

      /// Create a promise for getting our event prop data!
      const eventDataPromise = new Promise((resolve, reject) => {
        // Set a 30-second timeout
        const timeout = setTimeout(() => {
          resolve("[HardRockBet1] Timed out while waiting for all eventId data responses.");
        }, 1000 * 60);

        /**
         * On connection open
         */
        socket.once('open', function () {
          /**
           * Send our initial login request message (required)
           */
          socket.send(JSON.stringify({
            "SportsbookLoginRequest": {
              "application": "sportsbook",
              "channel": "NEW_JERSEY_ONLINE",
              "locale": "enus-us-x-nj"
            }
          }));

          // Subscribe to incoming message, required to handle the rest of the logic
          socket.once('message', function (message) {
            // We should've gotten the "status: ok, reqid: 0" response here for our SportsbookLoginRequest
            const data = JSON.parse(message);

            // If not ok status, throw error?
            if (!data.Response?.status === "ok") {
              console.error(`[HardRockBet2] Failed establishing graphql-ws connection. Raw message: ${message}`);
              return;
            }

            // Request subscription data
            // Send our subscription request to get data for the event id
            socket.send(JSON.stringify({
              "SubscriptionRequest": {
                "subscribe": {
                  "ids": [...eventIds], // Support for multi eventId subscriptions
                  "mostBalancedMarkets": false
                }
              }
            }));

            // Assume we got ok status, subscribe to incoming messages!
            socket.on('message', function (message) {
              // Check if we got our SubscriptionResponse
              const response = JSON.parse(message);

              // If no data field, ignore
              if (!response.SubscriptionResponse?.data) return;

              // We got the data we wanted, this should include player props stuff
              // extract the eventId we received a response for
              const responseEventId = response.SubscriptionResponse.data.id;

              // Store the response data in the received event data object
              eventIdData[responseEventId] = response.SubscriptionResponse.data;

              // We can close the connection once we got data for every eventId
              if (eventIds.every((id) => eventIdData[id])) {
                clearTimeout(timeout); // Clear the timeout upon successful data retrieval
                socket.close(); // Close the socket connection
                resolve(eventIdData); // Resolve now that we have all the data
              }
            });
          });
        });

        socket.on('error', (err) => {
          console.error(`[HardRockBet] WebSocket error: ${err.message}`);
          clearTimeout(timeout);
          reject(err);
        });

        socket.on('unexpected-response', (req, res) => {
          console.error(`[HardRockBet] Unexpected server response: ${res.statusCode}`);
          clearTimeout(timeout);
          reject(new Error(`Unexpected server response: ${res.statusCode}`));
        });
    
        socket.on('close', (code, reason) => {
          if (code === 1005) {
            console.warn(`[HardRockBet] WebSocket closed with no close code (code: ${code}, reason: ${reason})`);
          } else {
            console.log(`[HardRockBet] WebSocket closed (code: ${code}, reason: ${reason})`);
          }
          clearTimeout(timeout);
          resolve(eventIdData);
        });
      });

      // Await all data to arrive or timeout
      try {
        const data = await eventDataPromise;
        return data; // Return the received event data
      } catch (error) {
        console.error(`[HardRockBet5] Error fetching player props for eventIds [${eventIds.join(', ')}]: ${error}`);
        return null;
      }
    } catch (err) {
      console.error(`[HardRockBet] An error occurred while fetching player props: ${err.message}`);
      return null; // or return a default value
    }
  }

  close() {
    fetch.close();
  }
  //#endregion
}

module.exports = HardRockBetAPI;
