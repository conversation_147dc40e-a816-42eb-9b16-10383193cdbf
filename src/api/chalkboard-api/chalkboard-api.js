// Import
const crypto = require("crypto");

//#region Hashing / Signature util
/**
 * Chalkboard.io's HMAC key
 */
const HMAC_KEY = "3aeb578ed6fb0bea4e634c47916ae279cc9ff4ae";
/**
 * Calculate SHA1-HMAC hash from text 
 * @param {string} text 
 * @returns {string} hash
 */
function hash(text) {
  const hash = crypto.createHmac("sha1", HMAC_KEY)
    .update(text)
    .digest("hex");
  return hash;
}
//#endregion

//#region Constants / utils
const utils = {
  firebase: {
    getUsernameEmail(username) {
      return `${username}<EMAIL>`;
    }
  }
};

const constants = {
  firebase: {
    endpoints: {
      verifyPassword: "https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyPassword?key=AIzaSyDLd_hdFO5a-aTsc3k4eLjkTqdQE-beDks",
      getAccountInfo: "https://www.googleapis.com/identitytoolkit/v3/relyingparty/getAccountInfo?key=AIzaSyDLd_hdFO5a-aTsc3k4eLjkTqdQE-beDks",

      tokenAuthentication: "https://securetoken.googleapis.com/v1/token?key=AIzaSyDLd_hdFO5a-aTsc3k4eLjkTqdQE-beDks"
    },
    headers: {
      "X-Client-Version": "iOS/FirebaseSDK/10.25.0/FirebaseCore-iOS",
      "X-Ios-Bundle-Identifier": "com.taild",
      "User-Agent": "FirebaseAuth.iOS/10.25.0 com.taild/1.0.47 iPhone/17.0 hw/iPhone14_3",
      "x-firebase-appcheck": "eyJraWQiOiJqQTNhLVEiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_1930LCE3mvmOJ76u2xbIJYnQRdAszK8zmkBD1sl0sHCjDuQdyGdjJso5yHiA6o2FNwkIwUQnCmyxn9DJBZgIKZtaV9RshNNYaN5inx8SegoNnDXAfCZVBAaHIg1ut4v_kapqhv3vUsqp4Xr4E-kuK7bLHr_Ok3Lxzqz3pZms2F1zbNnw36guubskoBGOtg30E8OZkxXd-GLB6AQehKP_4poVLDGU9DOsjflqpUc3q0SbZvk1Gnoi2ObtCz9PAvkOC9FU2X7b_kuntFsDhZpFylo0-hjtItzRODTQqRjt_tvwDooQKFC3clbUGyRiJGk2dbMUb5TqA5Ez5MG5V7JWcHiC6ElxPzeRPX-dILrk_1WAHdTV-73TqOj3RuYJsEp5ZF1eOgdHmNN6aa_bsqJx8mWpQuxxAG-FTsakrpXv2TTayZPV1gWznTE400hAkbk4LzTChnuPfQblt8mkq9QLuAbc1hCsLlx8RwUL9dfPVLhPExeoOwTvKKTlKmo7yn"
    }
  },
  chalkboard: {
    endpoints: {
      api: {
        lobby: {
          legs: "https://kube-prod.chalkboard.io/dfs/api/lobby/legs"
        }
      },
      v2: {
        search: "https://kube-prod.chalkboard.io/v2/search/search" // Add the search endpoint
      }
    },
    headers: {
      "host": "kube-prod.chalkboard.io",
      "x-firebase-appcheck": "eyJraWQiOiJqQTNhLVEiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.H_1930LCE3mvmOJ76u2xbIJYnQRdAszK8zmkBD1sl0sHCjDuQdyGdjJso5yHiA6o2FNwkIwUQnCmyxn9DJBZgIKZtaV9RshNNYaN5inx8SegoNnDXAfCZVBAaHIg1ut4v_kapqhv3vUsqp4Xr4E-kuK7bLHr_Ok3Lxzqz3pZms2F1zbNnw36guubskoBGOtg30E8OZkxXd-GLB6AQehKP_4poVLDGU9DOsjflqpUc3q0SbZvk1Gnoi2ObtCz9PAvkOC9FU2X7b_kuntFsDhZpFylo0-hjtItzRODTQqRjt_tvwDooQKFC3clbUGyRiJGk2dbMUb5TqA5Ez5MG5V7JWcHiC6ElxPzeRPX-dILrk_1WAHdTV-73TqOj3RuYJsEp5ZF1eOgdHmNN6aa_bsqJx8mWpQuxxAG-FTsakrpXv2TTayZPV1gWznTE400hAkbk4LzTChnuPfQblt8mkq9QLuAbc1hCsLlx8RwUL9dfPVLhPExeoOwTvKKTlKmo7yn",
      "clientos": "ios",
      "currentversion": "1.0.47",
      "key": "beta",
      "Accept-Language": "en-GB,en;q=0.9",
      "User-Agent": "Chalkboard/4571 CFNetwork/1474 Darwin/23.0.0",
      "buildnumber": "4571",
      "devicemodel": "iPhone 13 Pro Max"
    }
  }
};
//#endregion

class ChalkboardAPI {
  /**
   * Undici ProxyAgent
   */
  proxyAgent = null;

  /**
   * Chalkboard API
   * @param {any?} proxyAgent 
   */
  constructor(proxyAgent = undefined) {
    this.proxyAgent = proxyAgent;
  }

  //#region Firebase authentication
  //#region Tokens
  /**
   * @type {{
   * idToken: string,
   * accessToken: string,
   * refreshToken: string,
   * expiresIn: number,
   * tokenType: string
   * }}
   */
  _authenticationResult = null;
  cacheAuthenticationResult(data) {
    // Extract relevant
    const result = {
      idToken: data.idToken || data.id_token || data.accessToken || data.access_token,
      accessToken: data.accessToken || data.access_token || data.idToken || data.id_token,
      refreshToken: data.refreshToken || data.refresh_token,
      expiresIn: parseInt(data.expiresIn || data.expires_in),
      tokenType: data.tokenType || data.token_type || "Bearer"
    };

    this._authenticationResult = result;
  }

  /**
   * Get cached authentication data
   */
  getAuthData() {
    return this._authenticationResult;
  }
  //#endregion

  /**
   * Firebase authentication using username/password 
   * @param {string} username 
   * @param {string} password 
   * @returns {{
   * kind: string,
   * localId: string,
   * email: string,
   * displayName: string,
   * registered: string,
   * 
   * idToken: string,
   * refreshToken: string
   * expiresIn: string
   * }} result
   */
  async authenticate(username, password) {
    try {
      /**
       * Request body payload
       * @type {{
       * clientType: "CLIENT_TYPE_IOS",
       * email: string,
       * password: string,
       * returnSecureToken: boolean
       * }}
       */
      const payload = {
        clientType: "CLIENT_TYPE_IOS",
        email: utils.firebase.getUsernameEmail(username),
        password: password,
        returnSecureToken: true
      };
      const encodedPayload = JSON.stringify(payload);

      // Make request
      const res = await fetch(constants.firebase.endpoints.verifyPassword, {
        method: "post",
        headers: {
          ...constants.firebase.headers,
          "content-type": "application/json"
        },
        body: encodedPayload,
        dispatcher: this.proxyAgent
      });

      // Check status
      if (!res.ok) {
        // Get error response data 
        const errorBody = await res.text();
        throw new Error(`Non-ok status, Response body: ${errorBody}`);
      }

      // Get JSON result
      /**
       * @type  {{
       * kind: string,
       * localId: string,
       * email: string,
       * displayName: string,
       * idToken: string,
       * registered: string,
       * refreshToken: string
       * expiresIn: string
       * }}
       */
      const data = await res.json();

      // Store authentication result
      this.cacheAuthenticationResult(data);

      // Return data
      return data;
    }
    catch (err) {
      throw err;
    }
  }
  /**
   * Firebase authentication using refresh token
   * @param {string} refreshToken 
   * @returns {{
   * id_token: string,
   * access_token: string,
   * refresh_token: string,
   * token_type: string,
   * expires_in: string,
   * user_id: string,
   * project_id: string
   * }} result
   */
  async authenticateRefreshToken(refreshToken) {
    try {
      /**
       * Request body payload
       * @type {{
       * grantType: "refresh_token"
       * refreshToken: string,
       * }}
       */
      const payload = {
        grantType: "refresh_token",
        refreshToken: refreshToken
      };
      const encodedPayload = JSON.stringify(payload);

      // Make request
      const res = await fetch(constants.firebase.endpoints.tokenAuthentication, {
        method: "post",
        headers: {
          "content-type": "application/json",
          ...constants.firebase.headers
        },
        body: encodedPayload,
        dispatcher: this.proxyAgent
      });

      // Check status
      if (!res.ok) {
        // Get error response data 
        throw new Error(`Non-ok status, Response body: ${res.text()}`);
      }

      // Get JSON result
      /**
       * @type {{
       * access_token: string,
       * expires_in: string,
       * token_type: string,
       * refresh_token: string,
       * id_token: string,
       * user_id: string,
       * project_id: string
       * }}
       */
      const data = await res.json();

      // Store authentication result
      this.cacheAuthenticationResult(data);

      // Return data
      return data;
    }
    catch (err) {
      throw err;
    }
  }

  /**
   * Firebase get account info
   * @param {string} idToken 
   * @returns {{
   * kind: string,
   * users: [{
   * localId: string,
   * email: string,
   * passwordHash: string,
   * emailVerified: boolean,
   * passwordUpdatedAt: number,
   * providerUserInfo: [{
   * providerId: string,
   * federatedId: string,
   * email: string,
   * rawId: string
   * }],
   * validSince: string,
   * lastLoginAt: string,
   * createdAt: string,
   * lastRefreshAt: string
   * }]
   * }} result
   */
  async getAccountInfo(idToken) {
    try {
      /**
       * Request body payload
       * @type {{
       * idToken: string
       * }}
       */
      const payload = {
        idToken: idToken
      };
      const encodedPayload = JSON.stringify(payload);

      // Make request
      const res = await fetch(constants.firebase.endpoints.getAccountInfo, {
        method: "post",
        headers: {
          "content-type": "application/json",
          ...constants.firebase.headers
        },
        body: encodedPayload,
        dispatcher: this.proxyAgent
      });

      // Check status
      if (!res.ok) {
        // Get error response data 
        throw new Error(`Non-ok status, Response body: ${res.text()}`);
      }

      // Get JSON result
      /**
       * @type {{
       * kind: string,
       * users: [{
       * localId: string,
       * email: string,
       * passwordHash: string,
       * emailVerified: boolean,
       * passwordUpdatedAt: number,
       * providerUserInfo: [{
       * providerId: string,
       * federatedId: string,
       * email: string,
       * rawId: string
       * }],
       * validSince: string,
       * lastLoginAt: string,
       * createdAt: string,
       * lastRefreshAt: string
       * }]
       * }}
       */
      const data = await res.json();

      // Return data
      return data;
    }
    catch (err) {
      throw err;
    }
  }

  /**
   * Is authenticated?
   * @returns {boolean}
   */
  isAuthenticated() {
    // Extract
    const { accessToken } = this._authenticationResult || {};

    // Check for accessToken
    if (!accessToken)
      return false;

    // Extract encoded jwt-token payload
    const [, encodedPayload,] = accessToken.split(".");

    // Decode payload
    /**
     * JWT Payload
     * @type {{
     * auth_time: number,
     * iat: number,
     * exp: number
     * }}
     */
    const payload = JSON.parse(Buffer.from(encodedPayload, "base64"));

    // Get current time
    const timestamp = parseInt(Date.now().toString().slice(0, -3));

    // Check if we're past expiration timestamp
    if (timestamp > payload.exp)
      return false; // Expired token

    // If our guard checks are implemented correctly, we should be authenticated now.
    return true;
  }
  //#endregion
  /**
    * Search games based on type
    * @param {string} type The type of games to search for (e.g., "nba")
    * @returns {Promise<any>} The search results
    */
  async searchGames(params) {
    // Ensure the instance is authenticated
    if (!this.isAuthenticated()) {
      throw new Error("Chalkboard instance not authenticated.");
    }
  
    try {
      // Encode the search parameters
      const encodedParams = new URLSearchParams(params).toString();
  
      // Generate request "generated" header value
      const generated = new Date().toISOString().split(".")[0];
      // Generate request "signature" header value
      const signature = hash(generated);
  
      // Construct the endpoint URL
      const endpointUrl = `${constants.chalkboard.endpoints.v2.search}?${encodedParams}`;
  
      // Log the endpoint URL
      console.log('Searching endpoint:', endpointUrl);
  
      // Make the request
      const res = await fetch(endpointUrl, {
        method: "get",
        headers: {
          ...constants.chalkboard.headers,
          "accept": "application/json, text/plain, */*",
          "Authorization": `${this._authenticationResult.tokenType} ${this._authenticationResult.accessToken}`,
          "generated": generated,
          "signature": signature,
        },
        dispatcher: this.proxyAgent
      });
  
      // Check if the request was successful
      if (!res.ok) {
        throw new Error(`Non-ok status, Response body: ${await res.text()}`);
      }
  
      // Parse the JSON response
      const data = await res.json();
  
      // Return the parsed data
      return data;
    } catch (err) {
      // Handle any errors that occur during the fetch
      console.error('Failed to fetch games:', err);
      throw err; // Rethrow the error after logging
    }
  }
  //#region Chalkboard

  //#endregion
}

module.exports = ChalkboardAPI;