// Import chalkboard
const ChalkboardAPI = require("./chalkboard-api");

function decimalToAmerican(decimalOdds) {
  if (decimalOdds >= 2) {
    return `+${Math.round((decimalOdds - 1) * 100)}`;
  } else {
    return `-${Math.round(100 / (decimalOdds - 1))}`;
  }
}

async function fetchPagesConcurrently(chalkboard, letter, startPage, endPage) {
  const pagePromises = [];
  for (let page = startPage; page <= endPage; page++) {
    pagePromises.push(chalkboard.getLobbyGames({ value: letter, page: page }));
  }
  return await Promise.all(pagePromises);
}

async function fetchGamesForLetter(chalkboard, letter) {
  const leagueNames = new Set(["nba"]);
  const projections = new Map();
  let currentPage = 1;
  const maxPagesPerBatch = 4; // Adjust based on API limits and performance

  let keepGoing = true;
  while (keepGoing) {
    const responses = await fetchPagesConcurrently(chalkboard, letter, currentPage, currentPage + maxPagesPerBatch - 1);

    let emptyResponse = true;
    for (const response of responses) {
      if (response && response.list && response.list.length > 0) {
        emptyResponse = false;
        for (const game of response.list) {
          if (leagueNames.has(game.leagueName)) {
            const teamType = game.player.teamId === game.home.id ? 'home' : 'away';
            const opponentType = teamType === 'home' ? 'away' : 'home';

            // formatted start time
            const startTime = new Date(game.scheduled);
            const options = {
              weekday: 'short',
              month: 'short',
              day: 'numeric',
              hour: 'numeric',
              minute: '2-digit',
              timeZone: 'America/New_York',
              timeZoneName: 'short',
            };
            const formattedStartTime = startTime.toLocaleString('en-US', options).replace('EDT', 'EST');

            // formatted league
            const formattedLeague = game.leagueName.toUpperCase();

            // Extracting over_odds and under_odds
            const overOdds = (game.markets.over.odds * 0.89).toFixed(2);
            const underOdds = (game.markets.under.odds * 0.89).toFixed(2);

            const overMulti = (game.markets.over.odds * 0.89).toFixed(2) + 'x';
            const underMulti = (game.markets.under.odds * 0.89).toFixed(2) + 'x';

            // Convert decimal odds to American odds
            const overOddsAmerican = decimalToAmerican(overOdds);
            const underOddsAmerican = decimalToAmerican(underOdds);

            const projection = {
              proj_id: game.key,
              league: formattedLeague,
              team: game[teamType].team_alias,
              opponent: game[opponentType].team_alias,
              matchup: game[teamType].team_alias + " vs. " + game[opponentType].team_alias, // Added matchup field
              player_id: game.player.player_id,
              player_name: game.player.full_name,
              position: game.player.position,
              stat_type: game.statisticName,
              line: game.line,
              start_time: formattedStartTime,
              image_url: game.player.img,
              over_odds: overOdds,
              under_odds: underOdds,
              over_multi: overMulti,
              under_multi: underMulti,
              over_odds_american: overOddsAmerican,
              under_odds_american: underOddsAmerican
            };

            projections.set(game.key, projection);
          }
        }
      }
    }

    if (emptyResponse) {
      keepGoing = false;
    } else {
      currentPage += maxPagesPerBatch;
    }
  }

  return Array.from(projections.values());
}

async function fetchChalkboard() {
  console.log("Logging into Chalkboard...");
  console.log("Authenticating user...");
  const chalkboard = new ChalkboardAPI();

  const authResult = await chalkboard.authenticate("bigmoney2004", "Testing@123");
  const accountInfo = await chalkboard.getAccountInfo(authResult.idToken);
  if (!accountInfo) {
    console.error("Failed to retrieve account information. Exiting application.");
    process.exit(1);
  }

  console.log("Fetching projections...");
  const alphabet = 'abcdefghijklmnopqrstuvwxyz'.split('');

  const startTime = Date.now(); // Start time

  const promises = alphabet.map(letter => fetchGamesForLetter(chalkboard, letter));
  const results = await Promise.all(promises);
  const combinedProjectionsMap = new Map();

  for (const projectionList of results) {
    for (const projection of projectionList) {
      combinedProjectionsMap.set(projection.proj_id, projection); // Combine with deduplication
    }
  }

  const endTime = Date.now(); // End time
  const timeTaken = endTime - startTime; // Calculate time taken

  console.log(`Fetch speed: ${timeTaken} ms.`);

  return Array.from(combinedProjectionsMap.values());
}

module.exports = { fetchChalkboard };