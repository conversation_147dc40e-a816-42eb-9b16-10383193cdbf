const { MongoClient } = require('mongodb');

async function fetchCaesars() {
  const client = new MongoClient('mongodb+srv://livehypeplus:<EMAIL>/?retryWrites=true&w=majority');
  
  try {
    // Connect to MongoDB
    await client.connect();
    
    // Get reference to the database and collection
    const db = client.db('Caesars');
    const collection = db.collection('player_projections');
    
    // Fetch all projections
    const projections = await collection.find({}).toArray();
    
    return projections;
  } catch (error) {
    console.error('Error fetching projections:', error);
    throw error;
  } finally {
    // Ensure the connection is closed even if there's an error
    await client.close();
  }
}

module.exports = {
  fetchCaesars
};