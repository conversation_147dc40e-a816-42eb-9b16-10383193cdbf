import { Projection } from "../utils/types";
const fetch = require("node-fetch");

// Type guards for Kambi API responses
interface KambiBetOffer {
    id: number;
    criterion: {
        label: string;
        englishLabel: string;
    };
    outcomes: KambiOutcome[];
    eventId: number;
}

interface KambiOutcome {
    id: number;
    label: string;
    odds: number;
    line?: number;
    participant?: string;
    status: string;
    type: string;
    oddsAmerican: string;
}

interface KambiEvent {
    event: {
        id: number;
        name: string;
        start: string;
    };
    betOffers: KambiBetOffer[];
}

// Add interface for event data
interface KambiEventData {
    id: number;
    name: string;
    start: string;
    // add other fields if needed
}

// Base URL for Kambi API
const baseUrl = 'https://eu1.offering-api.kambicdn.com/offering/v2018/parxuspa/listView/football';

// Default query parameters
const defaultParams = {
    lang: 'en_US',
    market: 'US-PA',
    category: '17479'  // Soccer category
};

// Type guard for KambiBetOffer
function isKambiBetOffer(offer: any): offer is KambiBetOffer {
    return offer 
        && typeof offer.id === 'number'
        && offer.criterion
        && typeof offer.criterion.label === 'string'
        && Array.isArray(offer.outcomes);
}

// Type guard for KambiOutcome
function isKambiOutcome(outcome: any): outcome is KambiOutcome {
    return outcome
        && typeof outcome.id === 'number'
        && typeof outcome.label === 'string'
        && typeof outcome.odds === 'number'
        && typeof outcome.status === 'string'
        && typeof outcome.type === 'string'
        && typeof outcome.oddsAmerican === 'string';
}

// Add this function after the type guards
function formatPlayerName(name: string): string {
    const [last, first] = name.split(',').map(n => 
        n.trim().normalize('NFD').replace(/[\u0300-\u036f]/g, '')
    );
    return `${first} ${last}`.toLowerCase();
}

// Function to determine stat type from criterion label
function getStatType(criterionLabel: string): string {
    if (criterionLabel.includes("Shots on Target") || 
        criterionLabel.includes("Player's shots on target")) {
        return "Shots on Target";
    }
    if (criterionLabel.includes("Player's shots")) {
        return "Shots";
    }
    return criterionLabel;
}

// Update function signature to accept event data
async function fetchProjectionData(eventId: string, eventData: KambiEventData): Promise<Projection[]> {
    const url = `https://eu1.offering-api.kambicdn.com/offering/v2018/parxuspa/betoffer/event/${eventId}?lang=en_US&market=US-PA`;

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Network response was not ok for event ID ${eventId}`);
        }
        const data = await response.json();

        if (!data.betOffers || !Array.isArray(data.betOffers)) {
            throw new Error(`Invalid response format for event ID ${eventId}`);
        }
        
        // Use a Map to track unique projections by player, stat type, and line
        const projectionsMap = new Map<string, Projection>();
        
        // Process each bet offer
        data.betOffers.forEach(offer => {
            if (!isKambiBetOffer(offer)) {
                return;
            }

            // Check if this is a player shots related bet
            if (offer.criterion.label.includes("Player's shots") || 
                offer.criterion.label.includes("Shots on Target")) {
                offer.outcomes.forEach(outcome => {
                    if (!isKambiOutcome(outcome) || !outcome.participant || outcome.status !== 'OPEN') {
                        return;
                    }

                    // Find the matching over/under outcome
                    const matchingOutcome = offer.outcomes.find(o => 
                        isKambiOutcome(o) &&
                        o.type === (outcome.type === 'OT_OVER' ? 'OT_UNDER' : 'OT_OVER') &&
                        o.participant === outcome.participant
                    );

                    if (!matchingOutcome) {
                        return;
                    }

                    const overOutcome = outcome.type === 'OT_OVER' ? outcome : matchingOutcome;
                    const underOutcome = outcome.type === 'OT_UNDER' ? outcome : matchingOutcome;
                    
                    const playerName = formatPlayerName(outcome.participant);
                    const statType = getStatType(offer.criterion.label);
                    const line = outcome.line ? Math.round(outcome.line / 1000 * 2)/2 : 0;
                    
                    // Create a unique key for this projection based on player, stat type, and line
                    const projectionKey = `${playerName}_${statType}_${line}`;

                    const projection: Projection = {
                        proj_id: `${offer.id}_${outcome.id}`,
                        league: "SOCCER",
                        player_name: playerName,
                        stat_type: statType,
                        line: line,
                        over_odds_american: overOutcome.oddsAmerican,
                        over_odds_decimal: overOutcome.odds / 1000,
                        under_odds_american: underOutcome.oddsAmerican,
                        under_odds_decimal: underOutcome.odds / 1000,
                        matchup: eventData.name,
                        start_time: new Date(eventData.start),
                        source: "BetRivers"
                    };

                    // Only add this projection if we don't already have one with the same key
                    if (!projectionsMap.has(projectionKey)) {
                        projectionsMap.set(projectionKey, projection);
                    }
                });
            }
        });

        return Array.from(projectionsMap.values());
    } catch (error: any) {
        console.error(`Failed to fetch or process data for event ID ${eventId}:`, error.message);
        return [];
    }
}

async function fetchAllEvents(): Promise<Projection[]> {
    try {
        const queryParams = new URLSearchParams({
            ...defaultParams
        });
        const url = `${baseUrl}?${queryParams.toString()}`;

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error('Failed to fetch events list');
        }

        const data = await response.json();
        
        // Map events to include their data
        const eventPromises = data.events.map(event => {
            const eventData = event.event; // Contains id, name, start, etc.
            return fetchProjectionData(eventData.id.toString(), eventData);
        });

        // Fetch projections for all events concurrently
        const results = await Promise.allSettled(eventPromises);

        // Filter out successful results and flatten the array
        const successfulProjections = results
            .filter((result): result is PromiseFulfilledResult<Projection[]> => result.status === 'fulfilled')
            .flatMap(result => result.value);

        return successfulProjections;
    } catch (error) {
        console.error('Error fetching events:', error);
        return [];
    }
}

async function fetchBetRivers(): Promise<Projection[]> {
    try {
        console.log('[BetRivers] Starting fetch...');
        const projections = await fetchAllEvents();
        const shotsProjections = projections.filter(p => p.stat_type === "Shots");
        // console.log(`[BetRivers] Completed fetch with ${shotsProjections.length} shots projections`);
        return projections;
    } catch (error) {
        console.error('Error during fetching BetRivers data:', error);
        return [];
    }
}

export { fetchBetRivers }; 