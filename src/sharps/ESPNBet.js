const { ESPNBetAPI } = require('../api/espnbet');

async function fetchESPNBet() {
    try {
        const api = new ESPNBetAPI();
        const allProjections = await api.fetchESPNBet();
        
        // Filter to keep only SOCCER projections
        const soccerProjections = allProjections.filter(projection => projection.league === "SOCCER");
        
        //console.log(`Fetched ${soccerProjections.length} SOCCER projections.`);
        return soccerProjections;
    } catch (error) {
        console.error('[ESPNBET] Error fetching ESPNBet projections:', error);
        throw error; // Re-throw the error to be handled by the caller
    }
}

module.exports = { fetchESPNBet };