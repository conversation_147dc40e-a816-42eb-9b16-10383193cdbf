// Pinnacle.js

function decimalToAmerican(decimalOdds) {
  if (decimalOdds >= 2) {
    // Ensure the output is always a string starting with '+'
    return '+' + Math.round((decimalOdds - 1) * 100);
  }
  // Ensure the output is always a string
  return Math.round(-100 / (decimalOdds - 1)).toString();
}

const RAPIDAPI_KEY = '**************************************************'; // Replace with your actual key if different
const RAPIDAPI_HOST = 'pinnacle-odds.p.rapidapi.com';

const SPORTS = {
  'Football': 7,
  'Hockey': 4,
  'Basketball': 3,
  'Baseball': 9
};

const LEAGUE_MAPPINGS = {
  3: 'NBA',  // Basketball
  4: 'NHL',  // Hockey
  7: 'NFL',  // Football
  9: 'MLB'   // Baseball - Already correctly mapped
};

const headers = {
  'X-RapidAPI-Key': RAPIDAPI_KEY,
  'X-RapidAPI-Host': RAPIDAPI_HOST
};

// --- Updated parseSpecialName Function ---
function parseSpecialName(name) {
  // Regex Explanation:
  // ^                   - Start of the string
  // (.*?)               - Capture group 1: Player Name (non-greedy)
  // \s*                 - Optional whitespace
  // \(                  - Literal opening parenthesis for the stat type
  // ([^)]+)             - Capture group 2: Stat Type (one or more characters that are NOT closing parenthesis)
  // \)                  - Literal closing parenthesis
  // (?:                 - Start of a non-capturing group for the suffix
  //   \(must start\)    - Match the literal "(must start)" suffix
  // )?                  - Make the suffix group optional
  // $                   - End of the string
  const match = name.match(/^(.*?)\s*\(([^)]+)\)(?:\(must start\))?$/);

  if (!match) {
    console.warn(`[PINNACLE] Could not parse special name format: ${name}`);
    // Fallback attempt if the suffix isn't exactly "(must start)" or missing
    const fallbackMatch = name.match(/^(.*?)\s*\(([^)]+)\)/);
     if (fallbackMatch) {
        console.warn(`[PINNACLE] Using fallback parsing for: ${name}`);
        return { player_name: fallbackMatch[1].trim(), stat_type: fallbackMatch[2].trim() };
     }
    return { player_name: name.trim(), stat_type: null }; // Final fallback
  }

  let player_name = match[1].trim();
  let stat_type = match[2].trim();

  // Handle potential Pitcher vs Batter stats differentiation if needed based on name/context
  // For now, direct mapping based on the extracted stat_type seems sufficient for the examples.
  // e.g., "Total Strikeouts" likely implies Pitcher Strikeouts in MLB context.

  return { player_name, stat_type };
}
// --- End of Updated parseSpecialName Function ---


// No changes needed in createProjection logic itself, but ensure it uses the updated parseSpecialName
// The existing logic for finding pairs and calculating odds seems correct for O/U props.
// Renaming this function slightly for clarity as it creates the object AFTER finding pairs
function createProjectionObject(special, overLine, underLine, league) {
  const { player_name, stat_type } = parseSpecialName(special.name);

  // Skip if parsing failed
  if (!player_name || !stat_type) return null;

  // Skip if essential event data is missing for matchup string
  if (!special.event || !special.event.home || !special.event.away) {
      console.warn(`[PINNACLE] Skipping special_id=${special.special_id} due to missing event data.`);
      return null;
  }

  const matchup = `${special.event.away} @ ${special.event.home}`; // Standard format Away @ Home
  const lineValue = parseFloat(overLine.handicap); // O/U line value is the same

  // Ensure odds are valid numbers > 1.0
  const overDecimalOdds = parseFloat(overLine.price);
  const underDecimalOdds = parseFloat(underLine.price);

  if (isNaN(lineValue) || !isFinite(lineValue) ||
      isNaN(overDecimalOdds) || !isFinite(overDecimalOdds) || overDecimalOdds <= 1 ||
      isNaN(underDecimalOdds) || !isFinite(underDecimalOdds) || underDecimalOdds <= 1) {
    console.warn(`[PINNACLE] Invalid line value or odds for special_id=${special.special_id}, handicap=${overLine.handicap}`);
    return null; // Skip if odds or line value are invalid
  }

  // Convert start time to a JavaScript Date object
  const startTime = new Date(special.starts + 'Z'); // Append 'Z' to ensure UTC parsing

  return {
    proj_id: `PINNY-${special.special_id}-${lineValue}`, // Unique ID per market/line value
    league,
    player_name,
    stat_type, // Use the raw stat_type here, mapping happens in constants.ts
    line: lineValue,
    over_odds_american: decimalToAmerican(overDecimalOdds),
    under_odds_american: decimalToAmerican(underDecimalOdds),
    over_odds_decimal: overDecimalOdds,
    under_odds_decimal: underDecimalOdds,
    matchup,
    start_time: startTime,
    source: 'Pinnacle'
    // is_alt: false, // Assuming these are main lines
  };
}


async function fetchPinnyProps() {
  const projections = [];
  const processedSpecialIds = new Set(); // Keep track of processed specials per handicap
  const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

  console.log('[PINNACLE] Fetching props...');
  try {
    for (const [sportName, sportId] of Object.entries(SPORTS)) {
      console.log(`[PINNACLE] Fetching for ${sportName} (ID: ${sportId})`);
      let retries = 3;
      let success = false;

      while (retries > 0 && !success) {
          try {
              const response = await fetch(`https://pinnacle-odds.p.rapidapi.com/kit/v1/special-markets?sport_id=${sportId}&is_have_odds=true&category=Player%20Props`, { // Filter by category in API call if possible
                method: 'GET',
                headers
              });

              if (response.status === 429) {
                  const retryAfter = response.headers.get('Retry-After') || 60; // Default to 60s
                  console.warn(`[PINNACLE] Rate limited for ${sportName}. Waiting ${retryAfter}s...`);
                  await delay(parseInt(retryAfter, 10) * 1000);
                  retries--;
                  continue; // Retry the current sport after waiting
              }

              if (!response.ok) {
                  const errorText = await response.text();
                  console.error(`[PINNACLE] Error fetching ${sportName} props:`, response.status, response.statusText, errorText);
                  retries = 0; // Don't retry on non-429 errors
                  break; // Break inner loop, go to next sport
              }

              const data = await response.json();
              const league = LEAGUE_MAPPINGS[sportId];
              console.log(`[PINNACLE] Received ${data.specials?.length || 0} specials for ${sportName}.`);

              if (!data.specials || data.specials.length === 0) {
                  console.log(`[PINNACLE] No specials found for ${sportName}.`);
                  success = true; // Mark as success even if no data
                  continue;
              }

              for (const special of data.specials) {
                // Double-check category just in case API filter fails
                if (special.category !== 'Player Props') continue;

                // Skip if event data is fundamentally missing
                if (!special.event) continue;

                const lines = special.lines || {};
                const handicapGroups = {};

                // Group lines by handicap
                for (const line of Object.values(lines)) {
                    if (line.handicap === null || line.handicap === undefined) continue; // Need handicap for O/U props

                    // Ensure handicap is treated consistently (e.g., as a string key)
                    const handicapKey = String(parseFloat(line.handicap)); // Standardize key
                    if (!handicapGroups[handicapKey]) {
                        handicapGroups[handicapKey] = [];
                    }
                    handicapGroups[handicapKey].push(line);
                }

                // Process each handicap group to create ONE projection per valid pair
                for (const [handicap, groupLines] of Object.entries(handicapGroups)) {
                    // Generate a unique key for this specific special + handicap combo
                    const uniqueMarketKey = `${special.special_id}-${handicap}`;
                    if (processedSpecialIds.has(uniqueMarketKey)) {
                        continue; // Already processed this exact market/line
                    }

                    if (groupLines.length !== 2) continue; // Skip if not exactly Over/Under

                    const overLine = groupLines.find(l => l.name === 'Over');
                    const underLine = groupLines.find(l => l.name === 'Under');

                    if (!overLine || !underLine) continue; // Skip if Over or Under is missing

                    // Create the projection object
                    const projection = createProjectionObject(special, overLine, underLine, league);

                    if (projection) {
                        projections.push(projection);
                        processedSpecialIds.add(uniqueMarketKey); // Mark as processed
                    }
                }
              }
              success = true; // Mark sport as successfully processed
          } catch (error) {
              console.error(`[PINNACLE] Network or parsing error processing ${sportName}:`, error);
              retries--;
              if (retries === 0) {
                 console.error(`[PINNACLE] Failed to fetch ${sportName} after multiple retries.`);
              } else {
                  await delay(5000); // Wait before retrying on general errors
              }
          }
      } // End retry while loop
      if (success) {
          console.log(`[PINNACLE] Successfully processed ${sportName}.`);
      }
      await delay(2000); // Delay between sports API calls
    } // End sports loop
  } catch (error) {
    console.error('[PINNACLE] Outer error in fetchPinnyProps:', error);
  }

  console.log(`[PINNACLE] Fetched total ${projections.length} props.`);
  return projections;
}

module.exports = { fetchPinnyProps };