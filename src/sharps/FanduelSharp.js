const { FanduelSharpAPI } = require('../api/fanduel-sharp-api');

// Function to fetch Fanduel projections
async function fetchFanduel() {
    try {
        // Initialize the FanduelSharpAPI instance
        const api = new FanduelSharpAPI();
        
        //console.log('Fetching Fanduel projections...');
        
        // Fetch projections
        const projections = await api.fetchFanduel();
        
        //console.log(`Fetched ${projections.length} projections.`);
        
        // Return the projections
        return projections;
    } catch (error) {
        console.error('[FANDUEL SHARP] Error fetching Fanduel projections:', error);
        throw error; // Re-throw the error to be handled by the caller
    }
}

module.exports = { fetchFanduel };
