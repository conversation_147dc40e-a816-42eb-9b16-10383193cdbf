// ./sharps/DraftKingsSharp.js
const { ProxyAgent } = require('undici');
const path = require('path');
const proxyList = require('../utils/proxy/proxyList').default;

let proxyCache = null;

async function readProxies() {
    if (proxyCache) {
        return proxyCache;
    }

    try {
        const proxies = proxyList['default'].map(line => {
            const [ip, port, username, password] = line.split(':');
            const proxyUrl = `http://${username}:${password}@${ip}:${port}`;
            return proxyUrl;
        });

        proxyCache = proxies;
        return proxies;
    } catch (error) {
        return [];
    }
}

function getRandomProxy(proxies) {
    const randomIndex = Math.floor(Math.random() * proxies.length);
    return proxies[randomIndex];
}


// URLs categorized by league and stat types
const leagueUrls = {
    /*
    NFL: {
        PASS_TDS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1000/subcategories/9525',
        PASS_YARDS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1000/subcategories/9524',
        PASS_RUSH_YARDS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1000/subcategories/9532',
        PASS_COMPLETIONS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1000/subcategories/9522',
        PASS_ATTEMPTS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1000/subcategories/9517',
        INTERCEPTIONS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1000/subcategories/15937',
        LONGEST_COMPLETION: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1000/subcategories/15968',
        RECEIVING_YARDS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1342/subcategories/14114',
        RECEPTIONS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1342/subcategories/14115',
        LONGEST_RECEPTION: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1342/subcategories/15948',
        RUSH_YARDS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1001/subcategories/9514',
        RUSH_ATTEMPTS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1001/subcategories/9518',
        RUSH_REC_YARDS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1001/subcategories/9523',
        LONGEST_RUSH: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1001/subcategories/9533',
        SACKS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1002/subcategories/11812',
        SOLO_TACKLES: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1002/subcategories/9530',
        ASSISTS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1002/subcategories/9531',
        TACKLES_AST: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1002/subcategories/9521',
        FG_MADE: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1002/subcategories/9529',
        KICKING_PTS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1002/subcategories/9520',
        PAT_MADE: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/88808/categories/1002/subcategories/9528',
    },
    // CFB: {
    //     PASS_TDS: 'https://sportsbook.draftkings.com/sites/US-SB/api/v5/eventgroups/87637/categories/1000/subcategories/14119?format=json',
    //     PASS_YARDS: 'https://sportsbook.draftkings.com/sites/US-SB/api/v5/eventgroups/87637/categories/1000/subcategories/15987?format=json',
    //     RECEIVING_YARDS: 'https://sportsbook.draftkings.com/sites/US-SB/api/v5/eventgroups/87637/categories/1342/subcategories/14114?format=json',
    //     RUSH_YARDS: 'https://sportsbook.draftkings.com/sites/US-SB/api/v5/eventgroups/87637/categories/1001/subcategories/9514?format=json',
    // },
    */
    NBA: {
        PTS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/1215/subcategories/12488',
        REB: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/1216/subcategories/12492',
        AST: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/1217/subcategories/12495',
        THREE_PT: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/1218/subcategories/12497',
        PRA: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/583/subcategories/5001',
        PR: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/583/subcategories/9976',
        PA: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/583/subcategories/9973',
        AR: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/583/subcategories/9974',
        // STLS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/1293/subcategories/13508',
        // BLKS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/1293/subcategories/13780',
        // SB: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/1293/subcategories/13781',
        // TO: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42648/categories/1293/subcategories/13782',
    },
    NHL: {
        SOG: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42133/categories/1189/subcategories/12040',
        PTS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42133/categories/1675/subcategories/16213',
        ASTS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42133/categories/1676/subcategories/16215',
        BLOCKS: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42133/categories/1679',
        SAVES: 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/42133/categories/1064/subcategories/16550',

    },
    // NBA1Q: {
    //     PTS: 'https://sportsbook.draftkings.com/sites/US-SB/api/v5/eventgroups/42648/categories/1215/subcategories/12490?format=json',
    // },
    // // WNBA: {
    // //     POINTS: 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/94682/categories/1215/subcategories/12488?format=json',
    // //     REBOUNDS: 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/94682/categories/1216/subcategories/12492?format=json',
    // //     ASSISTS: 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/94682/categories/1217/subcategories/12495?format=json',
    // //     PTS_REB_AST: 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/94682/categories/583/subcategories/5001?format=json',

    // // },
    // DARTS: {
    //     '180_THROWN': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/38977/categories/721/subcategories/6413?format=json',

    // },
    MLB: {
        // BATTER PROPS
        'HOME_RUNS': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/6606',   // CORRECTED ID for O/U
        'HITS': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/6719',      // CORRECTED ID for O/U
        'TOTAL_BASES': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/6607', // CORRECTED ID for O/U
        'RBIS': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/8025',      // CORRECTED ID for O/U
        'RUNS': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/17407',      // Verified Correct (Runs O/U)
        'HITS_RUNS_RBIS': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/17406', // Verified Correct (Hits + Runs + RBIs O/U)
        'STOLEN_BASES': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/17408', // Verified Correct (Stolen Bases O/U)
        'SINGLES': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/17409',      // Verified Correct (Singles O/U)
        'DOUBLES': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/17410',      // Verified Correct (Doubles O/U)
        'BATTER_WALKS': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/17411', // Verified Correct (Walks (Batter) O/U)
        'TRIPLES': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/743/subcategories/17439', // Verified Correct (Triples O/U)

        // PITCHER PROPS
        'PITCHER_STRIKEOUTS': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/1031/subcategories/15221', // Verified Correct (Strikeouts Thrown O/U)
        'HITS_ALLOWED': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/1031/subcategories/9886',      // Verified Correct (Hits Allowed O/U)
        'EARNED_RUNS_ALLOWED': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/1031/subcategories/17412', // Verified Correct (Earned Runs Allowed O/U)
        'WALKS_ALLOWED': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/1031/subcategories/15219',    // Verified Correct (Walks Allowed O/U)
        'OUTS_RECORDED': 'https://sportsbook-nash.draftkings.com/api/sportscontent/dkusnj/v1/leagues/84240/categories/1031/subcategories/17413',     // Verified Correct (Outs Recorded O/U)
    },
    // LAX: {
    //     'POINTS': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/204465/categories/870/subcategories/8082?format=json',
    //     'SAVES': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/204465/categories/870/subcategories/15501?format=json',

    // },
    // CRICKET: {
    //     'FOURS': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/37785/categories/616/subcategories/14684?format=json',
    //     'SIXES': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/37785/categories/616/subcategories/14685?format=json',
    // },
    // TENNIS: {
    //     'GAMES_WON_M': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/205959/categories/633/subcategories/8433?format=json',
    //     'GAMES_WON_W': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/205962/categories/633/subcategories/8433?format=json',

    //     /*
    //     'GAMES_WON': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/41349/categories/633/subcategories/8433?format=json',
    //     'BREAKPOINTS': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/41349/categories/1134/subcategories/11070?format=json',
    //     'DOUBLE_FAULTS': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/41349/categories/512/subcategories/6963?format=json',
    //     'ACES': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/41349/categories/512/subcategories/10930?format=json',
    //     'ACES_W': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/41409/categories/512/subcategories/10930?format=json',
    //     'DOUBLE_FAULTS_W': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/41409/categories/512/subcategories/6963?format=json',
    //     'BREAKPOINTS_W': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/41409/categories/1134/subcategories/11070?format=json',
    //     'GAMES_WON_W': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/41409/categories/633/subcategories/8433?format=json',
    //     */
    // },
    // MMA: {
    //     'SIG_STRIKES': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/9034/categories/556/subcategories/5373?format=json',
    //     'TAKEDOWNS': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/9034/categories/556/subcategories/10716?format=json',
    // },
    // GOLF: {
    //     'STROKES': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/55005/categories/1129/subcategories/11015?format=json',
    // },
    // /*
    // HRDERBY: {
    //     'TOTAL_HR_R1': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/84240/categories/897/subcategories/15717?format=json',
    //     'DISTANCE': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/84240/categories/897/subcategories/15714?format=json',
    //     'EXIT_VELO': 'https://sportsbook-nash.draftkings.com/sites/US-SB/api/v5/eventgroups/84240/categories/897/subcategories/15715?format=json'
    // },
    // */
};

async function fetchData(url, league) {
    try {
        const proxies = await readProxies();
        const proxyUrl = getRandomProxy(proxies);
        const agent = new ProxyAgent(proxyUrl);
        
        const headers = {
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36',
            'Referer': 'https://sportsbook.draftkings.com/',
            'Origin': 'https://sportsbook.draftkings.com',
            'sec-ch-ua': '"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site'
        };

        const response = await fetch(url, { 
            dispatcher: agent,
            headers: headers
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return extractData(data, league);
    } catch (error) {
        return [];
    }
}

function extractData(data, league) {
    if (!data || !data.events || !data.markets || !data.selections) {
        return [];
    }

    const projections = [];
    const events = data.events;

    // Group selections by market ID for efficient lookup
    const selectionsByMarket = data.selections.reduce((acc, selection) => {
        if (!acc[selection.marketId]) {
            acc[selection.marketId] = [];
        }
        acc[selection.marketId].push(selection);
        return acc;
    }, {});

    // Process each market
    data.markets.forEach(market => {
        const selections = selectionsByMarket[market.id];
        if (!selections) return;

        // Group selections into over/under pairs by their line value
        const pairs = selections.reduce((acc, selection) => {
            const line = selection.points;
            if (!acc[line]) acc[line] = {};

            if (selection.outcomeType === 'Over') {
                acc[line].over = selection;
            } else if (selection.outcomeType === 'Under') {
                acc[line].under = selection;
            }
            return acc;
        }, {});

        // Create projections for each valid over/under pair
        Object.values(pairs).forEach(pair => {
            if (pair.over && pair.under && pair.over.participants?.[0]) {
                const event = events.find(e => e.id === market.eventId);
                if (!event) return;

                // Clean player name by removing anything in parentheses
                const playerName = pair.over.participants[0].name.split(' (')[0].trim();

                // Clean stat type by removing the player name and parenthetical content
                let statType = market.name;
                const playerNameWithParens = pair.over.participants[0].name;
                statType = statType
                    .replace(playerNameWithParens, '')  // Remove full player name with parentheses
                    .replace(playerName, '')            // Remove just player name
                    .replace(/ O\/U$/, '')             // Remove O/U suffix
                    .replace(/^\s+/, '')               // Remove leading spaces
                    .trim();                           // Remove any remaining whitespace

                const projection = {
                    proj_id: `${pair.over.id}#${pair.under.id}`,
                    league: league,
                    player_name: playerName,
                    stat_type: statType,
                    line: pair.over.points,
                    over_odds_american: pair.over.displayOdds.american,
                    under_odds_american: pair.under.displayOdds.american,
                    over_odds_decimal: pair.over.displayOdds.decimal,
                    under_odds_decimal: pair.under.displayOdds.decimal,
                    matchup: event.name,
                    start_time: new Date(event.startEventDate),
                    is_alt: false,
                    source: 'DraftKings'
                };

                projections.push(projection);
            }
        });
    });

    return projections;
}

// Add this utility function for processing in batches with delays
async function processBatchesWithDelay(items, batchSize, delayMs, processFunction) {
    const results = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
        const batch = items.slice(i, i + batchSize);
        const batchResults = await Promise.allSettled(batch.map(processFunction));
        
        batchResults.forEach(result => {
            if (result.status === 'fulfilled') {
                results.push(...result.value);
            }
        });

        if (i + batchSize < items.length) {
            await new Promise(resolve => setTimeout(resolve, delayMs));
        }
    }
    
    return results;
}

async function fetchDraftKings() {
    let fetchTasks = [];

    for (let league in leagueUrls) {
        for (let statType in leagueUrls[league]) {
            fetchTasks.push({
                url: leagueUrls[league][statType],
                league: league,
                statType: statType
            });
        }
    }

    const projections = await processBatchesWithDelay(
        fetchTasks,
        10,
        500,
        async (task) => {
            try {
                const data = await fetchData(task.url, task.league);
                return data.map(item => ({ ...item, league: task.league }));
            } catch (error) {
                return [];
            }
        }
    );

    return projections;
}

module.exports = { fetchDraftKings };
