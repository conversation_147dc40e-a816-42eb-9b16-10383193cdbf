const fs = require('fs');
const path = require('path');
const { fetch, ProxyAgent } = require('undici');
const axios = require('axios');
const https = require('https');
const tls = require('tls');

function convertToAmericanOdds(decimalOdds) {
  let americanOdds;

  if (decimalOdds >= 2) {
    americanOdds = (decimalOdds - 1) * 100;
    americanOdds = `+${Math.round(americanOdds)}`;
  } else {
    americanOdds = -100 / (decimalOdds - 1);
    americanOdds = Math.round(americanOdds);
  }

  return americanOdds.toString();
}

const leagueMap = {
  LALIGA: 'https://troya.xyz/api/sgmGames?sb=betus&league=laliga',
  SERIEA: 'https://troya.xyz/api/sgmGames?sb=betus&league=seriea',
  BUNDES: 'https://troya.xyz/api/sgmGames?sb=betus&league=dfl',
  //FA: 'https://troya.xyz/api/sgmGames?sb=betus&league=fa',
  //EURL: 'https://troya.xyz/api/sgmGames?sb=betus&league=eurl',
  //ERE: 'https://troya.xyz/api/sgmGames?sb=betus&league=ere',
  //MXC: 'https://troya.xyz/api/sgmGames?sb=betus&league=mxc',
  EPL: 'https://troya.xyz/api/sgmGames?sb=betus&league=epl',
  //MLS: 'https://troya.xyz/api/sgmGames?sb=betus&league=mls',
  //BRZ: 'https://troya.xyz/api/sgmGames?sb=betus&league=brz',
  //MXA: 'https://troya.xyz/api/sgmGames?sb=betus&league=mxa',
  //EUROS: 'https://troya.xyz/api/sgmGames?sb=betus&league=euros',
  //COPA: 'https://troya.xyz/api/sgmGames?sb=betus&league=copa',
  UCL: 'https://troya.xyz/api/sgmGames?sb=betus&league=ucl',
  EBL: 'https://troya.xyz/api/sgmGames?sb=betus&league=ebl',
  //OBBALL: 'https://troya.xyz/api/sgmGames?sb=betus&league=olymb',
  //OLYMF: 'https://troya.xyz/api/sgmGames?sb=betus&league=olymf',
  MLB: 'https://troya.xyz/api/sgmGames?sb=betus&league=mlb',
  // CFB: 'https://troya.xyz/api/sgmGames?sb=betus&league=ncaafb',
  // NFL: 'https://troya.xyz/api/sgmGames?sb=betus&league=nfl',
  NBA: 'https://troya.xyz/api/sgmGames?sb=betus&league=nba',
  NHL: 'https://troya.xyz/api/sgmGames?sb=betus&league=nhl',
  // NCAAB: 'https://troya.xyz/api/sgmGames?sb=betus&league=ncaamb',
};

const statEndpoints = {
  /*
  OBBALL: {
    POINTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Points',
    REBOUNDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Total%2520Rebounds',
    ASSISTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Assists',
    PRA: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Pts%2520%252B%2520Reb%2520%252B%2520Ast',
    THREES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Three%2520Point%2520Field%2520Goals%2520Made',
  },
  */
  LALIGA: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  SERIEA: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  FA: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  EURL: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  ERE: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  MXC: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  EPL: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  UCL: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  MLS: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  BRZ: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  EUROS: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  COPA: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  UCL: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  BUNDES: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  MXA: {
    SHOTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520Goal',
    TACKLES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    PASSES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passes',
  },
  CFB: {
    PASS_YDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passing%2520Yards',
    PASS_TDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passing%2520TDs',
    RUSH_YDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Rushing%2520Yards',
    REC_YDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Receiving%2520Yards',
  },
  NFL: {
    PASS_YDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passing%2520Yards',
    PASS_COMP: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Pass%2520Completions',
    PASS_TDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Passing%2520TDs',
    PASS_ATT: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Pass%2520Attempts',
    RUSH_YDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Rushing%2520Yards',
    RUSH_ATT: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Carries',
    REC_YDS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Receiving%2520Yards',
    RECEPTIONS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Receptions',
    PASS_INT: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Pass%2520Interceptions',
    //TACKLES_AST: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Tackles',
    //SACKS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Sacks',
    //INT: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Interceptions',
  },
  NBA: {
    POINTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Points',
    REB: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Total%2520Rebounds',
    AST: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Assists',
    PRA: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Pts%2520%252B%2520Reb%2520%252B%2520Ast',
    THREE_PT: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Three%2520Point%2520Field%2520Goals%2520Made',
  },
  NHL: {
    POINTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Points',
    SOG: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Shots%2520on%2520goal',
    SAVES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Saves',
    GOALS: 'https://troya.xyz/api/dfm/marketsBySs?sb=betus&gameId=${gameId}&statistic=Goals'
  },
  NCAAB: {
    POINTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Points',
    REB: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Total%2520Rebounds',
    AST: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Assists',
    PRA: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Pts%2520%252B%2520Reb%2520%252B%2520Ast',
    THREE_PT: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Three%2520Point%2520Field%2520Goals%2520Made',
  },
  MLB: {
    STRIKEOUTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Strikeouts',
    HITS_ALLOWED: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Hits%2520Allowed',
    EARNED_RUNS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Earned%2520runs',
    HITS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Hits',
    //RUNS_RBIS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Runs%2520%252B%2520RBIs',
    //TOTAL_BASES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Total%2520bases',
    STOLEN_BASES: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Stolen%2520bases',
    HOME_RUNS: 'https://troya.xyz/api/dfm/marketsBySs?sb=betus&gameId=${gameId}&statistic=Home%2520runs'
  },
  EBL: {
    POINTS: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Points',
    REB: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Total%2520Rebounds',
    AST: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Assists',
    THREE_PT: 'https://troya.xyz/api/dfm/marketsByOu?sb=betus&gameId=${gameId}&statistic=Three%2520Point%2520Field%2520Goals%2520Made'
  },
};

// Read proxies from proxies.txt file
const proxiesFilePath = path.join(__dirname, '../proxies/other_proxies.txt');
const proxies = fs.readFileSync(proxiesFilePath, 'utf-8').trim().split('\n').map(proxy => proxy.replace(/\r/g, ''));

let currentProxyIndex = 0;

function getNextProxy() {
  const proxy = proxies[currentProxyIndex];
  currentProxyIndex = (currentProxyIndex + 1) % proxies.length;
  return proxy;
}

const axiosWithRetry = async (config, retries = 3, initialDelay = 1000) => {
  let attempt = 1;
  let lastError = null;

  while (attempt <= retries) {
    try {
      // Get a new proxy for each attempt
      const proxy = getNextProxy();
      const [ip, port, username, password] = proxy.split(':');
      const proxyAgent = new ProxyAgent({
        uri: `http://${username}:${password}@${ip}:${port}`,
      });

      const response = await fetch(config.url, {
        method: config.method || 'GET',
        headers: config.headers,
        dispatcher: proxyAgent,
        // Add timeout
        signal: AbortSignal.timeout(15000), // 15 second timeout
      });

      if (!response.ok) {
        throw new Error(`Request failed with status ${response.status}`);
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      lastError = error;
      if (attempt === retries) {
        console.error(`[BETONLINE] All ${retries} retries failed for ${config.url}`);
        throw error;
      }

      // Calculate delay with exponential backoff and jitter
      const delay = initialDelay * Math.pow(2, attempt - 1) * (0.5 + Math.random());
      console.log(`[BETONLINE] Attempt ${attempt}/${retries} failed: ${error.message}. Retrying in ${Math.round(delay)}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      attempt++;
    }
  }
};

async function getGameIds() {
  const requests = Object.entries(leagueMap).map(async ([league, url]) => {
    const proxy = getNextProxy();
    const [ip, port, username, password] = proxy.split(':');

    const proxyConfig = {
      protocol: 'http',
      host: ip,
      port: port,
      auth: {
        username: username,
        password: password
      }
    };

    const headers = {
      'Accept': 'application/json',
      'Connection': 'keep-alive',
      'Referer': 'https://troya.xyz/api/dfm/betbuilder?sb=betonline',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'newrelic': '********************************************************************************************************************************************************************************************************'
    };

    try {
      const response = await axiosWithRetry({
        method: 'get',
        url: url,
        headers: headers,
        proxy: proxyConfig,
      });

      const data = response.data;
      const gameInfo = data
        .filter(game => game.providers.some(provider => provider.name === 'nix'))
        .map(game => {
          const nixProvider = game.providers.find(provider => provider.name === 'nix');
          const team1 = game.team1[0].title;
          const team2 = game.team2[0].title;
          const capitalizeFirstLetter = str => str.replace(/\b\w/g, char => char.toUpperCase());
          const matchup = `${capitalizeFirstLetter(team2)} vs. ${capitalizeFirstLetter(team1)}`;
          const start_time = game.date;
          return { gameId: nixProvider.id, matchup, start_time, league };
        });

      if (gameInfo.length === 0) {
        console.log(`[BETONLINE] NO GAMES AVAILABLE FOR ${league}`);
      }

      return gameInfo;
    } catch (error) {
      console.error(`[BETONLINE] ERROR FETCHING GAME IDS FOR ${league}:`, error.message);
      return [];
    }
  });

  const results = await Promise.all(requests);
  return results.flat();
}

async function fetchProjections(gameInfo) {
  const { gameId, matchup, start_time, league } = gameInfo;
  
  // Check if the league exists in statEndpoints
  if (!statEndpoints[league]) {
    console.log(`[BETONLINE] No stat endpoints configured for league: ${league}`);
    return [];
  }
  
  const stats = Object.keys(statEndpoints[league]);

  const promises = stats.map(async (stat) => {
    const url = statEndpoints[league][stat].replace('${gameId}', gameId);
    const proxy = getNextProxy();
    const [ip, port, username, password] = proxy.split(':');

    const proxyConfig = {
      protocol: 'http',
      host: ip,
      port: port,
      auth: {
        username: username,
        password: password
      }
    };

    const headers = {
      'Accept': 'application/json',
      'Connection': 'keep-alive',
      'Referer': 'https://troya.xyz/api/dfm/betbuilder?sb=betonline',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'same-origin',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'newrelic': '********************************************************************************************************************************************************************************************************'
    };

    try {
      const response = await axiosWithRetry({
        method: 'get',
        url: url,
        headers: headers,
      });

      const data = response.data;
      const mappedLeague = ['BUNDES', 'MXA', 'LALIGA', 'SERIEA', 'FA', 'EURL', 'ERE', 'MXC', 'EPL', 'UCL', 'MLS', 'BRZ', 'EUROS', 'COPA', 'OLYMF'].includes(league) ? 'SOCCER' : league;

      // Check if data is an array first
      if (!Array.isArray(data)) {
        // Keep existing warnings
        if (league === 'MLB' || league === 'NHL') { // Log for MLB and NHL specifically if needed
            console.warn(`[BETONLINE] Non-array response received for ${league} - ${stat} (${url}):`, data);
        } else {
            console.warn(`[BETONLINE] Non-array response received for ${league} - ${stat} (${url}):`, data);
        }
        return [];
      }

      // Handle empty array response
      if (data.length === 0) {
        return [];
      }

      // Handle non-empty array
      if (data.length === 1 && data[0] && data[0].hasOwnProperty('players')) {
        const players = data[0].players;

        // --- Handle Home Runs (MLB) or Goals (NHL) (marketsBySs) ---
        const isHomeRuns = league === 'MLB' && stat === 'HOME_RUNS' && data[0].statistic === 'Home runs';
        const isGoals = league === 'NHL' && stat === 'GOALS' && data[0].statistic === 'Goals';

        if (isHomeRuns || isGoals) {
          const projections = [];
          const statTitle = isHomeRuns ? 'Home runs' : 'Goals'; // Determine correct title
          players.forEach((player) => {
            player.markets.forEach((market) => {
              // Check if market is active and valid (value > 0 for these stats)
              if (market.isActive && market.isActual && market.value > 0) {
                const projection = {
                  proj_id: market.id.toString(),
                  league: mappedLeague,
                  player_name: player.name,
                  stat_type: statTitle, // Use determined title
                  line: market.value - 0.5,
                  over_odds_american: convertToAmericanOdds(market.odds),
                  under_odds_american: null,
                  over_odds_decimal: market.odds,
                  under_odds_decimal: null,
                  matchup: matchup,
                  start_time: start_time,
                  source: 'BetOnline',
                };
                projections.push(projection);
              }
            });
          });
          return projections;

        // --- Handle standard Over/Under markets (marketsByOu) ---
        } else if (stat !== 'HOME_RUNS' && stat !== 'GOALS') { // Ensure we don't process HR/Goals here
          const projections = players.map((player) => {
            const markets = player.markets;
            if (markets.length === 2) {
              const overMarket = markets.find(market => market.condition === 3);
              const underMarket = markets.find(market => market.condition === 1);

              if (overMarket && underMarket && overMarket.isActive && overMarket.isActual && underMarket.isActive && underMarket.isActual) {
                const projection = {
                  proj_id: `${overMarket.id}_${underMarket.id}`,
                  league: mappedLeague,
                  player_name: player.name,
                  stat_type: overMarket.statistic?.title || stat,
                  line: overMarket.value,
                  over_odds_american: convertToAmericanOdds(overMarket.odds),
                  under_odds_american: convertToAmericanOdds(underMarket.odds),
                  over_odds_decimal: overMarket.odds,
                  under_odds_decimal: underMarket.odds,
                  matchup: matchup,
                  start_time: start_time,
                  source: 'BetOnline',
                };
                return projection;
              }
            }
            return null;
          }).filter(projection => projection !== null);
          return projections;
        } else {
           // Mismatch for specific stat checks - Keep this warning logic
           if ((league === 'MLB' && stat === 'HOME_RUNS') || (league === 'NHL' && stat === 'GOALS')) {
               console.warn(`[BETONLINE] Mismatch or unexpected structure for ${league} ${stat} stat check (${url})`, data[0]);
           } else {
                // Log if it wasn't OU and wasn't the specific SS stat handled above
                console.warn(`[BETONLINE] Unhandled structure for ${league} ${stat} (${url})`, data[0]);
           }
           return [];
        }

      } else {
        // Unexpected array structure - Keep this warning
         if (league === 'MLB' || league === 'NHL') { // Log for MLB and NHL specifically if needed
            console.warn(`[BETONLINE] Unexpected array structure for ${league} - ${stat} (${url}):`, JSON.stringify(data));
        } else {
            console.warn(`[BETONLINE] Unexpected array structure (no players key or wrong length) for ${league} - ${stat} (${url}):`, JSON.stringify(data));
        }
        return [];
      }

    } catch (error) {
      console.error(`[BETONLINE] ERROR FETCHING/PROCESSING PROJECTIONS FOR ${league}: ${stat} FROM ${url}:`, error.message);
      return [];
    }
  });

  const results = await Promise.all(promises);
  return results.flat();
}

async function fetchBetOnline() {
  const gameInfos = await getGameIds();
  const projectionPromises = gameInfos.map(gameInfo => fetchProjections(gameInfo));
  const projectionResults = await Promise.all(projectionPromises);
  const allProjections = projectionResults.flat();
  return allProjections;
}

module.exports = { fetchBetOnline };
