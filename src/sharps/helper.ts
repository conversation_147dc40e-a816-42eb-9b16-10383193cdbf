import { formatDecimal } from "../utils/FormatUtility";
import { Projection, SharpEV } from "../utils/types";

const { MetricUtility } = require("../utils/MetricUtility");

export function calculateSharpEVs(
  sharpProjection: Projection[],
  providerProjection: Projection,
  probabilityFunction: any
): SharpEV[] {
  if (!providerProjection.under_odds_american) {
    return calculateOneWayPropEV(sharpProjection, providerProjection);
  }

  return sharpProjection.map((projection) => {
    return calculateSharpEv(
      projection,
      providerProjection,
      probabilityFunction
    );
  });
}

export function calculateOneWayPropEV(
  sharpProjections: Projection[],
  providerProjection: Projection
) {
  const ourSides = sharpProjections.map((projection) =>
    Number(projection.over_odds_decimal)
  );
  const oppositeSides = sharpProjections.map((projection) =>
    Number(projection.under_odds_decimal)
  );

  const averageOurSide = ourSides.reduce((a, b) => a + b, 0) / ourSides.length;
  const averageOppositeSide =
    oppositeSides.reduce((a, b) => a + b, 0) / oppositeSides.length;

  // Calculate fair odds and probabilities
  const [prob1] = MetricUtility.probitMethod(
    averageOurSide,
    averageOppositeSide,
    { forceDecimal: true }
  );

  const winPercentage = (prob1 * 100).toFixed(1) + '%'; // win%, similar to what model_over_percent would be
  const ev = MetricUtility.calculateEv(
    providerProjection.over_odds_decimal,
    prob1
  ); // (actual ev)
  const evPercentage = (ev * 100).toFixed(1); // (display ev)

  return sharpProjections.map((projection) => {
    return {
      source: projection.source,
      datawise_pick: "Over",
      ev,
      ev_display: evPercentage,
      model_over_percent: winPercentage,
      datawise_percent: winPercentage,
      model_ev_over: ev,
    };
  });
}

export function calculateSharpEv(
  sharpProjection: Projection,
  providerProjection: Projection,
  probabilityFunction: any
): SharpEV {
  const { source, line, under_odds_decimal, over_odds_decimal } =
    sharpProjection;

  const impliedOddsModel = MetricUtility.probability(
    providerProjection.line,
    line,
    under_odds_decimal,
    over_odds_decimal,
    probabilityFunction
  );
  const ourLine = MetricUtility.impliedOddsToDecimal(impliedOddsModel[0]); // probabilityStrictlyLessThan -> FIRST VALUE RETURNED FROM MetricUtility.probability
  const oppositeLine = MetricUtility.impliedOddsToDecimal(impliedOddsModel[1]); // probabilityStrictlyGreaterThan -> SECOND VALUE RETURNED FROM MetricUtility.probability

  /* EV FOR UNDER */
  const evConservativeUnderModel = MetricUtility.devig(
    providerProjection.under_odds_decimal,
    ourLine,
    oppositeLine
  );

  /* EV FOR OVER */
  const evConservativeOverModel = MetricUtility.devig(
    providerProjection.over_odds_decimal,
    oppositeLine,
    ourLine
  );

  // Convert EV to percentages for under and over
  const model_ev_under = (evConservativeUnderModel * 100).toFixed(2);
  const model_ev_over = (evConservativeOverModel * 100).toFixed(2);

  const modelUnderPercentOdds = 100 * impliedOddsModel[0];
  const modelOverPercentOdds = 100 * impliedOddsModel[1];
  const modelTiePercentOdds =
    100 * (1 - impliedOddsModel[0] - impliedOddsModel[1]);

  if (evConservativeUnderModel > evConservativeOverModel) {
    return {
      source,
      impliedOddsModel,
      datawise_pick: "Under",
      ev: parseFloat(evConservativeUnderModel),
      ev_display: formatDecimal(100 * evConservativeUnderModel) + "%",
      model_under_percent: formatDecimal(modelUnderPercentOdds) + "%",
      model_over_percent: formatDecimal(modelOverPercentOdds) + "%",
      model_tie_percent: formatDecimal(modelTiePercentOdds) + "%",
      datawise_percent: formatDecimal(modelUnderPercentOdds) + "%",
      model_ev_under,
      model_ev_over,
    };
  } else {
    return {
      source,
      impliedOddsModel,
      datawise_pick: "Over",
      ev: parseFloat(evConservativeOverModel),
      ev_display: formatDecimal(100 * evConservativeOverModel) + "%",
      model_under_percent: formatDecimal(modelUnderPercentOdds) + "%",
      model_over_percent: formatDecimal(modelOverPercentOdds) + "%",
      model_tie_percent: formatDecimal(modelTiePercentOdds) + "%",
      datawise_percent: formatDecimal(modelOverPercentOdds) + "%",
      model_ev_over,
      model_ev_under,
    };
  }
}
