// Import required modules
const CircaAPI = require('../api/circa-api');

/**
 * Converts American odds to decimal odds.
 * 
 * @param {string} americanOdds - American odds to convert.
 * @returns {number|null} Decimal odds or null if conversion fails.
 */
function convertAmericanToDecimal(americanOdds) {
    if (!americanOdds || americanOdds === 'EV') {
        return americanOdds === 'EV' ? 2.0 : null; // Return 2.0 for 'EV' or null for invalid odds
    }
    const odds = parseInt(americanOdds, 10);
    if (isNaN(odds)) {
        return null; // Return null if the odds can't be converted to a number
    }
    if (odds > 0) {
        return (odds / 100) + 1;
    } else {
        return (100 / Math.abs(odds)) + 1;
    }
}

/**
 * Extracts projections from the games response.
 * 
 * @param {object} gamesResponse - Games response to extract projections from.
 * @returns {array} Array of extracted projections.
 */
function extractProjections(gamesResponse) {
    if (!gamesResponse.Games) return [];
    
    return gamesResponse.Games.map(game => {
        const slashIndex = game.Heading.indexOf('/');
        const firstSpaceAfterTeam2 = game.Heading.indexOf(' ', slashIndex);

        const team1 = game.Heading.substring(0, slashIndex);
        const team2 = game.Heading.substring(slashIndex + 1, firstSpaceAfterTeam2);
        const matchup = `${team1} vs. ${team2}`;

        // Extract player name and stat type from heading
        let playerName = '';
        let statType = '';
        let line = null;
        let overOddsAmerican = '';
        let underOddsAmerican = '';

        if (game.Heading.includes('STRIKEOUTS')) {
            // Approach for pitcher strikeouts - Format: "TEAM1/TEAM2 FIRST_NAME LAST_NAME (TEAM) STRIKEOUTS"
            
            // Extract the player name directly from the heading using a regex pattern
            const match = game.Heading.match(/[A-Z]+\/[A-Z]+ (.+?) \([A-Z]+\) STRIKEOUTS/);
            
            if (match && match[1]) {
                playerName = match[1];
            } else {
                // Fallback to manual extraction
                const headingParts = game.Heading.split(' ');
                const slashIndex = headingParts.findIndex(part => part.includes('/'));
                const parenIndex = headingParts.findIndex(part => part.startsWith('('));
                
                if (slashIndex >= 0 && parenIndex > slashIndex) {
                    playerName = headingParts.slice(slashIndex + 1, parenIndex).join(' ');
                } else {
                    // Last resort fallback
                    playerName = headingParts[2]; 
                }
            }
            
            statType = 'STRIKEOUTS';
            line = game.GameLine.RawTotalOver;
            overOddsAmerican = game.GameLine.OverOdds || '';
            underOddsAmerican = game.GameLine.UnderOdds || '';
        } else {
            // Handle other prop types
            const propTypes = {
                'HIT A HOME RUN': 'HOME RUNS',
                'TOTAL BASES': 'TOTAL BASES',
                'RECORD A HIT': 'HITS',
                'RECORD AN RBI': 'RBI',
                'SCORE A RUN': 'RUNS',
                'STEAL A BASE': 'STOLEN BASES'
            };

            for (const [key, value] of Object.entries(propTypes)) {
                if (game.Heading.includes(key)) {
                    // Extract player name properly - find the position between team matchup and prop type
                    const parts = game.Heading.split(' ');
                    
                    // Find the index where the actual player name starts (after team matchup)
                    // The format is typically "TEAM1/TEAM2 PLAYER_NAME (TEAM) PROP_TYPE"
                    const teamSeparatorIndex = parts.findIndex(p => p.includes('/'));
                    
                    // Find where the prop type or team designation starts
                    const propStartIndex = parts.findIndex(p => p.includes('(') || p === key.split(' ')[0]);
                    
                    if (teamSeparatorIndex >= 0 && propStartIndex > teamSeparatorIndex) {
                        // Extract player name between team separator and prop type/team designation
                        playerName = parts.slice(teamSeparatorIndex + 1, propStartIndex).join(' ').trim();
                    } else {
                        // Fallback - this should rarely happen
                        playerName = parts.slice(2, -4).join(' ').replace(/\([^)]*\)/g, '').trim();
                    }
                    
                    // Remove team designation if present
                    playerName = playerName.replace(/\([^)]*\)/g, '').trim();
                    
                    statType = value;

                    // For total bases, use the actual line and regular over/under odds
                    if (value === 'TOTAL BASES') {
                        line = game.GameLine.RawTotalOver;
                        overOddsAmerican = game.GameLine.OverOdds || '';
                        underOddsAmerican = game.GameLine.UnderOdds || '';
                    } else {
                        // For binary props, set line to 0.5 and map yes/no odds to over/under
                        line = 0.5;
                        overOddsAmerican = game.GameLine.VOdds || ''; // Yes maps to Over
                        underOddsAmerican = game.GameLine.HOdds || ''; // No maps to Under
                    }
                    break;
                }
            }
        }

        // Skip if we couldn't parse the player name or stat type or if we don't have odds
        if (!playerName || !statType || line === null || !overOddsAmerican || !underOddsAmerican) return null;

        // Normalize player name to match other provider formats
        playerName = playerName.replace(/\./g, '').replace(/\s+/g, ' ');
        
        if (underOddsAmerican === 'EV') {
            underOddsAmerican = '+100';
        }

        const overOddsDecimal = convertAmericanToDecimal(overOddsAmerican);
        const underOddsDecimal = convertAmericanToDecimal(underOddsAmerican === '+100' ? 'EV' : underOddsAmerican);

        if (overOddsDecimal == null || underOddsDecimal == null) {
            return null;
        }

        const startTime = new Date(game.GameDt);

        return {
            proj_id: `${game.Id}_${playerName.toUpperCase().replace(/\s+/g, '_')}_${statType}_${line}`,
            league: game.Sport,
            player_name: playerName,
            stat_type: statType,
            line: line,
            over_odds_american: overOddsAmerican,
            under_odds_american: underOddsAmerican,
            over_odds_decimal: overOddsDecimal,
            under_odds_decimal: underOddsDecimal,
            matchup: matchup,
            start_time: startTime,
            source: "Circa"
        };
    }).filter(projection => projection !== null);
}

/**
 * Initializes the API, calls getLeagueGamesAnon with leagueIds '1184' and '1319', and saves the response.
 * 
 * @returns {array} Array of extracted projections.
 */
export async function fetchCirca() {
    try {
        const api = CircaAPI(); // Adjust based on whether CircaAPI requires 'new' keyword or not
        const sessionId = await api.generateSessionId();
        await api.setSessionId(sessionId);

        // Include all MLB player prop league IDs
        const leagueIds = [
            '1124',  // MLB - PITCHING PROPS
            '1138',  // MLB - PLAYER TO HIT A HOME RUN
            '1930',  // MLB - PLAYER TOTAL BASES
            '1125',  // MLB - PLAYER TO RECORD A HIT
            '1928',  // MLB - PLAYER TO RECORD RBI
            '1139',  // MLB - PLAYER TO SCORE A RUN
            '1935',  // MLB - PLAYER TO STEAL A BASE
        ];
        const leagueGames = await api.getLeagueGamesAnon(leagueIds, '', 1, "moneyline");

        if (!leagueGames) {
            console.log('No Circa MLB props available');
            return [];
        }

        const projections = extractProjections(leagueGames);
        return projections;
    } catch (error) {
        console.error('An error occurred:', error.message);
        return []; // Return an empty array in case of error
    }
}
