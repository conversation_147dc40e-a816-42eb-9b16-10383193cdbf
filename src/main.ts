import { setGlobalDispatcher, ProxyAgent, Agent, fetch as undiciFetch } from 'undici';
import { getProxy, logProxyStats } from './utils/proxy'; // Adjust the path if needed
const pLimit = require('p-limit');

import {
  fetchSharpProjections,
  processProjections,
} from "./utils/ProjectionUtility";
import logger from "./utils/logger";
import { Provider, providers } from "./providers/providers";
import { SimpleIntervalJob, AsyncTask, ToadScheduler } from "toad-scheduler";
import { Projection } from "./utils/types";
import { ProviderSessionError } from "./utils/errors";
import PerformanceAnalytics, { MonitoringLevel } from "./utils/performance/PerformanceAnalytics";
import { measureExecutionTime, captureMemorySnapshot } from "./utils/performance/NetworkTiming";
import { getPerformanceConfig, formatConfigForDisplay } from "./utils/performance/config";
import PerformanceDashboard from "./utils/performance/Dashboard";
const providerDBClient = require("./database/client");

// Add a cycle counter to track even/odd cycles
let cycleCounter = 0;

// Initialize Performance Analytics with configuration
const performanceConfig = getPerformanceConfig();
const performanceAnalytics = PerformanceAnalytics.getInstance();
const performanceDashboard = PerformanceDashboard.getInstance();

// Configure performance monitoring
performanceAnalytics.setMonitoringLevel(performanceConfig.monitoringLevel);
performanceDashboard.setDisplayEnabled(performanceConfig.enableRealTimeDisplay);

console.log(`[PERFORMANCE] Performance monitoring initialized`);
if (performanceConfig.monitoringLevel === MonitoringLevel.DEBUG) {
    console.log(formatConfigForDisplay(performanceConfig));
}

// Create a function to get a proxy agent with retries
async function getProxyAgentWithRetry(maxRetries = 3): Promise<ProxyAgent | Agent> {
    for (let i = 0; i < maxRetries; i++) {
        try {
            const proxyUrl = await getProxy();
            return new ProxyAgent(proxyUrl);
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.error(`Proxy attempt ${i + 1}/${maxRetries} failed:`, errorMessage);
            if (i === maxRetries - 1) {
                console.warn('All proxy attempts failed, falling back to direct connection');
                return new Agent({ 
                    keepAliveTimeout: 75000,
                    keepAliveMaxTimeout: 75000
                });
            }
            // Wait before retry (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
        }
    }
    // Fallback agent if all retries fail
    return new Agent({ 
        keepAliveTimeout: 75000,
        keepAliveMaxTimeout: 75000
    });
}

// Function to refresh the global dispatcher periodically
async function refreshGlobalDispatcher() {
    try {
        const agent = await getProxyAgentWithRetry();
        setGlobalDispatcher(agent);
        console.log('Successfully refreshed global dispatcher');
    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Error refreshing global dispatcher:', errorMessage);
    }
}

// Create an async initialization function
async function initializeDispatcher() {
    await refreshGlobalDispatcher();
    // Refresh the dispatcher every 5 minutes
    setInterval(refreshGlobalDispatcher, 5 * 60 * 1000);
}

// Call the initialization function
initializeDispatcher().catch(error => {
    console.error('Failed to initialize dispatcher:', error);
});

const findPlays = async (provider: Provider, sharpProjections: Projection[]) => {
  // Start provider-level performance monitoring
  performanceAnalytics.startProvider(provider.name);

  const startTime = Date.now();
  let providerProjections: Projection[] = [];
  let fetchDuration = 0;
  let processDuration = 0;
  let storeDuration = 0;

  try {
    // Capture memory before fetch
    if (performanceConfig.monitoringLevel === MonitoringLevel.DEBUG) {
      captureMemorySnapshot(`${provider.name} - Before Fetch`);
    }

    // Fetching provider projections with detailed timing
    const fetchResult = await measureExecutionTime(
      async () => {
        return await Promise.race([
          provider.fetchFunction(sharpProjections),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Fetch timeout')), 75000)
          )
        ]);
      },
      'Fetch',
      provider.name
    );

    providerProjections = fetchResult.result;
    fetchDuration = fetchResult.duration;
    performanceAnalytics.recordProviderFetch(provider.name, fetchDuration);

    // Log total projections once after fetching
    logger(`${provider.name}: Total Projections - ${providerProjections.length}`);

    // Capture memory after fetch
    if (performanceConfig.monitoringLevel === MonitoringLevel.DEBUG) {
      captureMemorySnapshot(`${provider.name} - After Fetch`);
    }

    // Processing all projections at once with timing
    const processResult = await measureExecutionTime(
      () => processProjections(sharpProjections, providerProjections, provider.name),
      'Process',
      provider.name
    );

    const plays = processResult.result;
    processDuration = processResult.duration;
    performanceAnalytics.recordProviderProcess(provider.name, processDuration);

    // Log total EV bets found once after processing
    logger(`${provider.name}: Total EV bets found - ${plays.length}`);

    // Record provider results
    performanceAnalytics.recordProviderResults(provider.name, providerProjections.length, plays.length);

    // Storing projections (only if plays exist) with timing
    if (plays.length > 0) {
      const storeResult = await measureExecutionTime(
        async () => {
          await providerDBClient.storeProjections(
            provider.name,
            plays,
            provider.collectionName
          );
        },
        'Store',
        provider.name
      );
      storeDuration = storeResult.duration;
      performanceAnalytics.recordProviderStore(provider.name, storeDuration);
    }

  } catch (error: unknown) {
    let errorMessage = 'Unknown error';
    let shouldRetry = false;

    if (error instanceof Error) {
      errorMessage = error.message;

      if (error.message === 'Fetch timeout') {
        logger(`${provider.name}: Fetch timed out after 75 seconds`);
        performanceAnalytics.recordProviderError(provider.name, 'Fetch timeout after 75 seconds');
      } else if (error.message.includes('Proxy response (403)')) {
        logger(`${provider.name}: Proxy authentication failed - triggering refresh`);
        performanceAnalytics.recordProviderError(provider.name, 'Proxy authentication failed');
        await refreshGlobalDispatcher();
        shouldRetry = true;
      } else if (error.message.includes('Request was cancelled') || error.message.includes('fetch failed')) {
        // Handle both the cancellation and fetch failed errors
        logger(`${provider.name}: Connection issue detected - triggering refresh`);
        performanceAnalytics.recordProviderError(provider.name, 'Connection issue detected');
        await refreshGlobalDispatcher();
        shouldRetry = true;
      } else {
        logger(`${provider.name}: Error during execution - ${error.message}`);
        performanceAnalytics.recordProviderError(provider.name, `Execution error: ${error.message}`);
      }
    } else {
      logger(`${provider.name}: An unknown error occurred`);
      performanceAnalytics.recordProviderError(provider.name, 'Unknown error occurred');
    }

    // Retry the operation for both proxy errors AND cancelled requests
    if (shouldRetry) {
      try {
        logger(`${provider.name}: Retrying after error...`);
        performanceAnalytics.recordProviderRetry(provider.name);

        const retryResult = await measureExecutionTime(
          async () => await provider.fetchFunction(sharpProjections),
          'Retry Fetch',
          provider.name
        );

        providerProjections = retryResult.result;
        fetchDuration = retryResult.duration;
        performanceAnalytics.recordProviderFetch(provider.name, fetchDuration);
      } catch (retryError: unknown) {
        const retryErrorMessage = retryError instanceof Error ? retryError.message : 'Unknown error';
        logger(`${provider.name}: Retry also failed - ${retryErrorMessage}`);
        performanceAnalytics.recordProviderError(provider.name, `Retry failed: ${retryErrorMessage}`);
      }
    }
  } finally {
    // Log the breakdown regardless of success or failure
    const totalDuration = Date.now() - startTime;
    const success = providerProjections.length > 0;

    // End provider monitoring
    performanceAnalytics.endProvider(provider.name, success);

    // Capture final memory snapshot for debug mode
    if (performanceConfig.monitoringLevel === MonitoringLevel.DEBUG) {
      captureMemorySnapshot(`${provider.name} - Completed`);
    }

    logger(`${provider.name}: Cycle completed in ${totalDuration}ms`);
    logger(`${provider.name}: Breakdown - Fetch: ${fetchDuration}ms, Process: ${processDuration}ms, Store: ${storeDuration}ms`);

    if (providerProjections.length === 0) {
      logger(`${provider.name}: Warning - No projections fetched`);
      performanceAnalytics.recordProviderError(provider.name, 'No projections fetched');
    }
  }
};

const scheduler = new ToadScheduler();

const task = new AsyncTask(
  "scrapers",
  async () => {
    // Increment cycle counter and start cycle monitoring
    cycleCounter++;
    performanceAnalytics.startCycle(cycleCounter);

    console.log(`🚀 Scraping Projections - Cycle #${cycleCounter}`);

    // Fetch sharp projections with timing
    const sharpResult = await measureExecutionTime(
      async () => await fetchSharpProjections(),
      'Sharp Projections Fetch'
    );

    const sharpProjections = sharpResult.result;
    performanceAnalytics.recordSharpProjectionsFetch(sharpResult.duration);

    // Create a concurrency pool with parallel executions
    const limit = pLimit(50);

    // Run all providers every cycle
    const providersToRun = providers;

    // Capture system state before provider execution
    const systemMemoryBefore = captureMemorySnapshot('System - Before Providers');

    await Promise.all(providersToRun.map(provider =>
      limit(async () => {
        try {
          await findPlays(provider, sharpProjections);
        } catch (error) {
          performanceAnalytics.recordProviderError(provider.name, `Provider session error: ${error}`);
          throw new ProviderSessionError(provider.name, JSON.stringify(error));
        }
      })
    ));

    // Capture system state after provider execution
    const systemMemoryAfter = captureMemorySnapshot('System - After Providers');

    // End cycle monitoring and generate report
    const cycleMetrics = performanceAnalytics.endCycle();

    // Log proxy statistics after each cycle
    logProxyStats();

    // Log cycle summary
    if (cycleMetrics) {
      console.log(`✅ Cycle #${cycleCounter} completed in ${cycleMetrics.duration!.toFixed(1)}ms`);
      console.log(`📊 Providers: ${cycleMetrics.successfulProviders}/${cycleMetrics.totalProviders} successful`);
      console.log(`📈 Total: ${cycleMetrics.totalProjections} projections, ${cycleMetrics.totalEvBets} EV bets`);
    }
  },
  (err: Error) => {
    console.error({ err });
  }
);

const job = new SimpleIntervalJob({ seconds: 15, runImmediately: true }, task, {
  id: "scrapers",
  preventOverrun: true,
});

// Create and start job
scheduler.addSimpleIntervalJob(job);

// Graceful shutdown logic
process.on("SIGINT", async () => {
  console.log("Shutting down gracefully...");
  scheduler.stop();
  try {
    await providerDBClient.closeAllConnections(); // Close all database connections
    console.log("Successfully closed all database connections.");
    process.exit(0); // Exit cleanly
  } catch (error) {
    console.error(`Error while closing database connections: ${error}`);
    process.exit(1); // Exit with error
  }
});
