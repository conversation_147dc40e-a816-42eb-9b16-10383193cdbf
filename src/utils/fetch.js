// ./src/utils/fetch.js
const initCycleTLS = require('cycletls').default;
const fs = require('fs');
const path = require('path');

// Read the proxies from the file
const proxiesFilePath = path.join(__dirname, '..', 'proxies', 'proxies.txt');
const proxiesData = fs.readFileSync(proxiesFilePath, 'utf8');
const proxies = proxiesData.split('\n').map(line => line.trim()).filter(line => line !== '');

// Function to get a random proxy from the list
function getRandomProxy() {
  const randomIndex = Math.floor(Math.random() * proxies.length);
  const [ip, port, username, password] = proxies[randomIndex].split(':');
  return `http://${username}:${password}@${ip}:${port}`;
}

async function fetch(url, opts) {
  const httpClient = await initCycleTLS();

  // Get a random proxy
  const proxy = getRandomProxy();

  const res = await httpClient(url, {
    headers: opts?.headers || {},
    cookies: opts?.cookies || {},
    body: opts?.body || '',
    ja3: opts?.ja3 || '771,4865-4867-4866-49195-49199-52393-52392-49196-49200-49162-49161-49171-49172-156-157-47-53-10,0-23-65281-10-11-35-16-5-51-43-13-45-28-21,29-23-24-25-256-257,0',
    userAgent: opts?.userAgent || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:92.0) Gecko/20100101 Firefox/92.0',
    proxy: proxy,
  }, opts.method || 'get');

  httpClient.exit();

  return {
    body: res.body,
    statusCode: res.status,
    proxy: proxy,
  };
}

module.exports = fetch;