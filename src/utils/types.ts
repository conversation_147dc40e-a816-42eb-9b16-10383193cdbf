export interface Projection {
  is_one_way?: boolean;
  is_alt?: boolean;
  under_odds_american: string | null;
  over_odds_american: string | null;
  proj_id: string;
  league: string;
  team?: string;
  player_name: string;
  stat_type: string;
  line: number;
  over_odds_decimal?: number;
  under_odds_decimal?: number;
  matchup: string;
  start_time: Date;
  source: string;
  over_deep_link?: string;
  under_deep_link?: string;
  live_data?: {
    is_live: boolean;
    score?: string;
    match_clock?: string;
  };
  // Additional properties used in ProjectionUtility.ts
  datawise_pick?: string;
  ev?: number;
  ev_display?: string;
  qk?: number;
  qk_display?: string;
  novig_over_limit?: number;
  novig_under_limit?: number;
  pb_over_price?: number;
  pb_under_price?: number;
  pb_market_id?: string;
  pb_market_suspended?: boolean;
  pb_market_status?: string;
  pb_selection_id?: string;
  betr_market_id?: string;
  betr_market_status?: string;
  betr_market_suspended?: boolean;
  betr_selection_id_over?: string;
  betr_selection_id_under?: string;
  betr_selection_status_over?: string;
  betr_selection_status_under?: string;
  betr_selection_suspended_over?: boolean;
  betr_selection_suspended_under?: boolean;
  betr_price_over?: number;
  betr_price_under?: number;
  espn_over_id?: string;
  espn_under_id?: string;
  espn_status?: string;
  espn_suspended?: boolean;
  espn_rate_limit_exceeded?: boolean;
  fliff_market_id?: string;
  fliff_selection_id?: string;
  fliff_outcome_id_over?: string;
  fliff_outcome_id_under?: string;
  dabble_market_id?: string;
  dabble_event_id?: string;
  dabble_selection_id?: string;
  dabble_points?: number;
  sleeper_game_id?: string;
  sleeper_over_id?: string;
  sleeper_under_id?: string;
  vivid_event_id?: string;
  vivid_offer_id_over?: string;
  vivid_offer_id_under?: string;
  ud_appearance_stat_id?: string;
  ud_over_under_line_id?: string;
  ud_over_stat_option_id?: string;
  ud_under_stat_option_id?: string;
  pp_line_score?: number;
  pp_stat_type_id?: string;
  pp_projection_type_id?: string;
  pp_odds_type_id?: string;
  pp_custom_limit_id?: string;
  pp_is_promo?: boolean;
  parlay_play_category_id?: string;
  parlay_play_league_id?: string;
  parlay_play_market_id?: string;
  hot_streak_prop_key?: string;
  hot_streak_market_id?: string;
  hot_streak_player_id?: string;
  thrive_contest_prop_id?: string;
  thrive_prop_id?: string;
  boom_event_id?: string;
  boom_market_id?: string;
  boom_player_id?: string;
  boom_over_selection_id?: string;
  boom_under_selection_id?: string;
  boom_game_info?: {
    away_team?: string;
    home_team?: string;
    match_clock?: string;
  };
  prophetx_over_limit?: number;
  prophetx_under_limit?: number;
}

export interface MergedProjection extends Projection {
  modelInfo?: Model;
  preferred_sharp?: string;
  sharps: Sharp[];
  sharp_ev: number;
  sharp_ev_display: string;
  avg_ev: number;
  avg_ev_display: string;
  datawise_pick: string;
  datawise_percent: string;
  model_over_percent: string;
  model_under_percent?: string;
  model_tie_percent?: string;
  model_ev_under?: string;
  model_ev_over: string;
  fairOdds: {
    over: string | null;
    under?: string | null;
  };
  stat_type_initial?: string;
  proj_string: string;
  qk: number;
  qk_display: string;
}

export interface Sharp {
  source: string;
  line: number;
  under_odds_american: string | null;
  over_odds_american: string | null;
  over_odds_decimal?: number;
  under_odds_decimal?: number;
  matchup: string;
  start_time: Date;
  ev?: number;
  ev_display?: string;
  qk?: number;
  qk_display?: string;
  datawise_pick?: string;
}

export interface Translation {
  standardStatType: string;
  modelInfo?: Model;
  preferredSharp?: string;
}

export interface Model {
  model?: "negbin" | "poisson";
  p?: number;
}

export interface EVData {
  // Define the structure based on your EV calculation needs
}

export interface ProcessedProjection {
  // Define the structure based on the processed projection data
}

export interface LeagueData {
  mapping: { [sourcePlatform: string]: string };
  modelInfo: Model;
  preferredSharp: string;
}

export interface LeagueStatTypeMapping {
  [league: string]: LeagueData;
}

export interface SharpEV {
  source: string;
  impliedOddsModel?: number[];
  datawise_pick: string;
  ev: number;
  ev_display: string;
  model_under_percent?: string;
  model_over_percent: string;
  model_tie_percent?: string;
  datawise_percent: string;
  model_ev_under?: string;
  model_ev_over: string;
}
