const { lgamma } = require('mathjs');
const jStat = require('jstat').jStat;

class MetricUtility {

  static devig(oddsToBet, referenceLineOurSide, referenceLineOppSide) {


    const basicMethodImpliedOdds = MetricUtility.basicMethod(referenceLineOurSide, referenceLineOppSide);
    const basicMethodDecimalOdds = MetricUtility.impliedOddsToDecimal(basicMethodImpliedOdds[0]);


    const additiveMethodImpliedOdds = MetricUtility.additiveMethod(referenceLineOurSide, referenceLineOppSide);
    const additiveMethodDecimalOdds = MetricUtility.impliedOddsToDecimal(additiveMethodImpliedOdds[0]);


    let powerMethodImpliedOdds, powerMethodDecimalOdds;
    let tightestConstraintDecimal, tightestConstraintImplied;
    try {
      powerMethodImpliedOdds = MetricUtility.powerMethod(referenceLineOurSide, referenceLineOppSide);
      powerMethodDecimalOdds = MetricUtility.impliedOddsToDecimal(powerMethodImpliedOdds[0]);


      tightestConstraintDecimal = Math.max(basicMethodDecimalOdds, additiveMethodDecimalOdds, powerMethodDecimalOdds);
      tightestConstraintImplied = MetricUtility.decimalToImpliedOdds(tightestConstraintDecimal);
    } catch (e) {
      tightestConstraintDecimal = Math.max(basicMethodDecimalOdds, additiveMethodDecimalOdds);
      tightestConstraintImplied = MetricUtility.decimalToImpliedOdds(tightestConstraintDecimal);
    }


    const oddsToBetImplied = MetricUtility.decimalToImpliedOdds(oddsToBet);
    const ev = MetricUtility.calculateEv(oddsToBet, tightestConstraintImplied)


    return ev;
  }


  static calculateEv(decimalOddsToBet, trueProbability) {
    let impliedProbability = 1 / decimalOddsToBet;
    let potentialProfitMultiple = decimalOddsToBet - 1;
    return (trueProbability * potentialProfitMultiple - (1 - trueProbability));
  }


  static decimalToImpliedOdds(decimalOdds) {
    return 1 / decimalOdds;
  }


  static americanToDecimal(americanOdds) {
    if (americanOdds > 100 || americanOdds < -100) {  // Typically, American odds are higher than 100 or lower than -100
        return americanOdds > 0 ? (americanOdds / 100) + 1 : (100 / -americanOdds) + 1;
    } else {
        return americanOdds;  // Assuming the input might actually be decimal odds already
    }
  }


  static impliedOddsToDecimal(impliedOdds) {
    return 1 / impliedOdds;
  }


  static probabilityToZScore(probability) {
    return jStat.normal.inv(probability, 0, 1);
  }


  static zScoreToProbability(zScore) {
    return jStat.normal.cdf(zScore, 0, 1);
  } 


  static probabilityToAmericanOdds(probability) {
    // Clamping for extreme probabilities, though typically 1/P would be used with a clamping decimalToAmerican
    if (probability <= 0) return '+1000000';
    if (probability >= 1) return '-1000000';

    if (probability === 0.5) {
        return '+100';
    }
    const decimalOdds = 1 / probability;
    if (decimalOdds >= 2.0) { // Corresponds to P <= 0.5
        const american = Math.round((decimalOdds - 1) * 100);
        return `+${american}`;
    } else { // Corresponds to P > 0.5 (1 < decimalOdds < 2)
        const american = Math.round(-100 / (decimalOdds - 1));
        return `${american}`; // Math.round returns number, ensure string for negative
    }
   }

  static adjustForPush(pOver, pUnder, pPush) {
    if (pPush === 0 || pPush === undefined || pPush === null || isNaN(pPush)) {
        return [pOver, pUnder];
    }
    const adjOver = pOver + pPush / 2;
    const adjUnder = pUnder + pPush / 2;
    const norm = adjOver + adjUnder;
    if (norm === 0 || isNaN(norm)) {
        // Avoid division by zero or NaN results; return unadjusted or zero probabilities
        // Depending on desired behavior, could return [0,0] or original [pOver, pUnder]
        // For safety, returning normalized 0s if sum is 0.
        if (pOver === 0 && pUnder === 0) return [0,0];
        // If norm is zero due to pOver/pUnder being negative after adjustment (should not happen with valid inputs)
        // Fallback to original or handle error. For now, let's try to normalize if possible or return 0s.
        const originalNorm = pOver + pUnder;
        if (originalNorm > 0) return [pOver/originalNorm, pUnder/originalNorm]; // Fallback to original normalization if push adjustment fails
        return [0, 0];
    }
    return [adjOver / norm, adjUnder / norm];
  }


  static basicMethod(firstAverageOdds, secondAverageOdds) {
    const eachFactor = [];


    const invFirstAvgOdds = 1 / firstAverageOdds;
    const invSecondAvgOdds = 1 / secondAverageOdds;


    const prob1 = invFirstAvgOdds / (invFirstAvgOdds + invSecondAvgOdds);
    const prob2 = invSecondAvgOdds / (invFirstAvgOdds + invSecondAvgOdds);


    eachFactor.push(prob1, prob2);


    return eachFactor;
  }


  static additiveMethod(firstAverageOdds, secondAverageOdds) {
    const eachFactor = [];


    const invFirstAvgOdds = 1 / firstAverageOdds;
    const invSecondAvgOdds = 1 / secondAverageOdds;


    const prob1 = invFirstAvgOdds - ((invFirstAvgOdds + invSecondAvgOdds - 1) / 2);
    const prob2 = invSecondAvgOdds - ((invFirstAvgOdds + invSecondAvgOdds - 1) / 2);


    eachFactor.push(prob1, prob2);


    return eachFactor;
  }


  static powerMethod(firstAverageOdds, secondAverageOdds) {
    try {
      const eachFactor = [];

      const r1 = 1 / firstAverageOdds;
      const r2 = 1 / secondAverageOdds;

      let start = 0.000001;
      let end = 0.999999;
      let n = start;

      let iterations = 0;

      while (start < end) {
        iterations++;
        if (iterations > 100) {
          // Fall back to Probit method if power method doesn't converge
          return MetricUtility.probitMethod(firstAverageOdds, secondAverageOdds);
        }

        const valAtN = MetricUtility.powerMethodHelper(r1, r2, n);

        if (Math.abs(1 - valAtN) < 0.00001) {
          break;
        }

        if (valAtN > 1) {
          end = n;
        } else if (valAtN < 1) {
          start = n;
        } else {
          break;
        }

        n = (start + end) / 2;
      }

      const prob1 = Math.pow(r1, 1 / n);
      const prob2 = Math.pow(r2, 1 / n);

      eachFactor.push(prob1, prob2);

      return eachFactor;
    } catch (error) {
      // Fall back to Probit method if any error occurs
      return MetricUtility.probitMethod(firstAverageOdds, secondAverageOdds);
    }
  }


  static probitMethod(firstAverageOdds, secondAverageOdds, options = {}) {
    const eachFactor = [];
    const forceDecimal = !!options.forceDecimal;

    const firstDecimal = forceDecimal
      ? firstAverageOdds
      : MetricUtility.americanToDecimal(firstAverageOdds);
    const secondDecimal = forceDecimal
      ? secondAverageOdds
      : MetricUtility.americanToDecimal(secondAverageOdds);

    const firstProb = MetricUtility.decimalToImpliedOdds(firstDecimal);
    const secondProb = MetricUtility.decimalToImpliedOdds(secondDecimal);

    const firstZScore = MetricUtility.probabilityToZScore(firstProb);
    const secondZScore = MetricUtility.probabilityToZScore(secondProb);

    const constant = (firstZScore + secondZScore) / -2;

    const adjustedFirstZScore = firstZScore + constant;
    const adjustedSecondZScore = secondZScore + constant;

    const prob1 = MetricUtility.zScoreToProbability(adjustedFirstZScore);
    const prob2 = MetricUtility.zScoreToProbability(adjustedSecondZScore);
    
    eachFactor.push(prob1, prob2);

    return eachFactor;
  }


  static worstCaseMethod(firstAverageOdds, secondAverageOdds) {
    // List all methods to be compared
    const methods = [
        MetricUtility.basicMethod,
        MetricUtility.additiveMethod,
        MetricUtility.powerMethod,
        MetricUtility.probitMethod
    ];

    // Initialize variables to track the method yielding the lowest prob1
    let lowestProb1 = Infinity;
    let worstCaseProbabilities = null;

    // Iterate over each method to find the one with the lowest prob1
    methods.forEach(method => {
        const probabilities = method(firstAverageOdds, secondAverageOdds);
        const prob1 = probabilities[0];  // Assuming the first element is prob1

        // Check if the current method's prob1 is the lowest we've encountered
        if (prob1 < lowestProb1) {
            lowestProb1 = prob1;
            worstCaseProbabilities = probabilities;
        }
    });

    // Return the probabilities associated with the lowest prob1
    return worstCaseProbabilities;
  }


  static powerMethodHelper(r1, r2, n) {
    return Math.pow(r1, 1 / n) + Math.pow(r2, 1 / n);
  }

  // Calculates the Poisson probability mass function.
  static poissonPmf(k, l) {
    return Math.exp(k * Math.log(l) - l - lgamma(k + 1));
  }

  // Calculates the negative binomial probability mass function.
  static negativeBinomialPmf(k, r, p) {
    let m = k + r - 1;
    let n = k;
    return Math.exp(
      lgamma(m + 1) -
      lgamma(n + 1) -
      lgamma(m - n + 1) +
      k * Math.log(1 - p) +
      r * Math.log(p)
    );
  }

  // Finds the positive root of a monotonic function using the bisection method.
  static bisectPositive(f, tol = 1e-16) {
    let a = 1, b = 1;
    let maxDoublingIter = 128;
    for (let iteration = 0; iteration < maxDoublingIter; iteration++) {
      a /= 2;
      b *= 2;
      if (f(a) * f(b) < 0) {
        break;
      }
    }


    if (f(a) * f(b) >= 0) {
      throw new Error("The provided function is either non-monotone or the same sign for all positive arguments");
    }


    while ((b - a) / 2.0 > tol) {
      let c = (a + b) / 2.0;
      if (f(c) === 0 || c === a || c === b) {
        return c;
      } else if (f(c) * f(a) < 0) {
        b = c;
      } else {
        a = c;
      }
    }


    return (a + b) / 2.0;
  }
  /*
   * Calculates the probability under and over a given line based on reference
   * odds and a probability mass function.
   */
  static probability(inferenceLine, referenceLine, referenceUnderOdds, referenceOverOdds, probabilityMassFunction) {
    function impliedProbabilityUsingPowerMethod(referenceOddsA, referenceOddsB) {
      let pA = 1 / referenceOddsA;
      let pB = 1 / referenceOddsB;
      try {
        let x = MetricUtility.bisectPositive(
          x => Math.pow(pA, x) + Math.pow(pB, x) - 1
        );
        return [Math.pow(pA, x), Math.pow(pB, x)];
      } catch (error) {
        // If bisectPositive fails, fallback to the probit method with decimal odds
        // forcing decimal via options if necessary:
        const fallback = MetricUtility.probitMethod(
          referenceOddsA, referenceOddsB,
          { forceDecimal: true }
        );
        return fallback;
      }
    }


    function probabilityStrictlyLessThan(pmf, x, p) {
      let probability = 0;
      for (let n = 0; n < Math.ceil(x); n++) {
        probability += pmf(n, p);
      }
      return probability;
    }


    function probabilityStrictlyGreaterThan(pmf, x, p) {
      let probability = 0;
      for (let n = 0; n <= Math.floor(x); n++) {
        probability += pmf(n, p);
      }
      return 1 - probability;
    }


    let [pUnderReference, pOverReference] = impliedProbabilityUsingPowerMethod(referenceUnderOdds, referenceOverOdds);


    // Find parameter for probability mass function that satisfies
    // p(outcome < reference_line) / p(outcome > reference_line)
    // = p_under_reference / p_over_reference
    let param = MetricUtility.bisectPositive(param => {
      return pOverReference * probabilityStrictlyLessThan(probabilityMassFunction, referenceLine, param)
        - pUnderReference * probabilityStrictlyGreaterThan(probabilityMassFunction, referenceLine, param);
    });


    return [
      probabilityStrictlyLessThan(probabilityMassFunction, inferenceLine, param),
      probabilityStrictlyGreaterThan(probabilityMassFunction, inferenceLine, param)
    ];
  }


  static impliedOddsToImpliedOddsWithPush(impliedOdds0, impliedOdds1) {
    return impliedOdds0 / (impliedOdds0 + impliedOdds1);
  }
}

module.exports = {
    MetricUtility
  };