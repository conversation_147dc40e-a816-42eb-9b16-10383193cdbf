import { CycleMetrics, ProviderMetrics, PerformanceReport } from './PerformanceAnalytics';

export class PerformanceDashboard {
    private static instance: PerformanceDashboard;
    private isDisplayEnabled: boolean = true;
    private lastDisplayTime: number = 0;
    private displayInterval: number = 1000; // 1 second minimum between updates

    private constructor() {}

    public static getInstance(): PerformanceDashboard {
        if (!PerformanceDashboard.instance) {
            PerformanceDashboard.instance = new PerformanceDashboard();
        }
        return PerformanceDashboard.instance;
    }

    public setDisplayEnabled(enabled: boolean): void {
        this.isDisplayEnabled = enabled;
    }

    public displayProgressBar(current: number, total: number, label: string = 'Progress'): void {
        if (!this.isDisplayEnabled) return;

        const percentage = Math.round((current / total) * 100);
        const barLength = 30;
        const filledLength = Math.round((percentage / 100) * barLength);
        const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
        
        process.stdout.write(`\r${label}: [${bar}] ${percentage}% (${current}/${total})`);
        
        if (current === total) {
            process.stdout.write('\n');
        }
    }

    public displayProviderStatus(providers: Map<string, ProviderMetrics>): void {
        if (!this.isDisplayEnabled) return;

        const now = Date.now();
        if (now - this.lastDisplayTime < this.displayInterval) return;
        this.lastDisplayTime = now;

        console.clear();
        console.log('🎯 REAL-TIME PERFORMANCE DASHBOARD');
        console.log('=' .repeat(80));
        
        const sortedProviders = Array.from(providers.values())
            .sort((a, b) => (b.duration || 0) - (a.duration || 0));

        console.log('\n📊 Provider Status:');
        console.log('Provider'.padEnd(15) + 'Status'.padEnd(12) + 'Duration'.padEnd(12) + 'Projections'.padEnd(12) + 'Errors');
        console.log('-'.repeat(80));

        sortedProviders.forEach(provider => {
            const status = provider.endTime ? '✅ Complete' : '🔄 Running';
            const duration = provider.duration ? `${provider.duration.toFixed(1)}ms` : 'Running...';
            const projections = provider.totalProjections.toString();
            const errors = provider.errors.length.toString();

            console.log(
                provider.name.padEnd(15) +
                status.padEnd(12) +
                duration.padEnd(12) +
                projections.padEnd(12) +
                errors
            );
        });

        // Display network statistics
        const totalRequests = sortedProviders.reduce((sum, p) => sum + p.networkRequests, 0);
        const avgResponseTime = this.calculateAverageResponseTime(sortedProviders);
        
        console.log('\n🌐 Network Statistics:');
        console.log(`Total Requests: ${totalRequests}`);
        console.log(`Average Response Time: ${avgResponseTime.toFixed(1)}ms`);
        
        // Display memory usage
        const memUsage = process.memoryUsage();
        console.log('\n💾 Memory Usage:');
        console.log(`RSS: ${(memUsage.rss / 1024 / 1024).toFixed(1)} MB`);
        console.log(`Heap Used: ${(memUsage.heapUsed / 1024 / 1024).toFixed(1)} MB`);
        console.log(`Heap Total: ${(memUsage.heapTotal / 1024 / 1024).toFixed(1)} MB`);
    }

    public displayCycleSummary(cycleMetrics: CycleMetrics): void {
        if (!this.isDisplayEnabled) return;

        console.log('\n' + '='.repeat(80));
        console.log(`🏁 CYCLE #${cycleMetrics.cycleNumber} SUMMARY`);
        console.log('='.repeat(80));

        // Overall metrics
        console.log(`⏱️  Total Duration: ${cycleMetrics.duration!.toFixed(1)}ms`);
        console.log(`📊 Providers: ${cycleMetrics.successfulProviders}/${cycleMetrics.totalProviders} successful`);
        console.log(`📈 Results: ${cycleMetrics.totalProjections} projections, ${cycleMetrics.totalEvBets} EV bets`);
        console.log(`🚀 Concurrency Efficiency: ${cycleMetrics.concurrencyEfficiency!.toFixed(1)}%`);

        // Performance breakdown
        console.log('\n📋 Performance Breakdown:');
        const sharpTime = cycleMetrics.sharpProjectionsFetchTime || 0;
        const providerTime = cycleMetrics.duration! - sharpTime;
        
        console.log(`  Sharp Projections: ${sharpTime.toFixed(1)}ms (${((sharpTime / cycleMetrics.duration!) * 100).toFixed(1)}%)`);
        console.log(`  Provider Execution: ${providerTime.toFixed(1)}ms (${((providerTime / cycleMetrics.duration!) * 100).toFixed(1)}%)`);

        // Top performers and bottlenecks
        const providers = Array.from(cycleMetrics.providerMetrics.values());
        const fastest = providers.reduce((min, p) => (p.duration || Infinity) < (min.duration || Infinity) ? p : min);
        const slowest = providers.reduce((max, p) => (p.duration || 0) > (max.duration || 0) ? p : max);

        console.log('\n🏆 Performance Leaders:');
        console.log(`  Fastest: ${fastest.name} (${fastest.duration!.toFixed(1)}ms)`);
        console.log(`  Slowest: ${slowest.name} (${slowest.duration!.toFixed(1)}ms)`);

        // Error summary
        const totalErrors = providers.reduce((sum, p) => sum + p.errors.length, 0);
        if (totalErrors > 0) {
            console.log(`\n⚠️  Errors: ${totalErrors} total errors detected`);
            const errorProviders = providers.filter(p => p.errors.length > 0);
            errorProviders.forEach(p => {
                console.log(`    ${p.name}: ${p.errors.length} error(s)`);
            });
        }

        console.log('='.repeat(80));
    }

    public displayTrendAnalysis(historicalData: CycleMetrics[]): void {
        if (!this.isDisplayEnabled || historicalData.length < 2) return;

        console.log('\n📈 TREND ANALYSIS');
        console.log('-'.repeat(50));

        const recent = historicalData.slice(-5); // Last 5 cycles
        const avgDuration = recent.reduce((sum, cycle) => sum + (cycle.duration || 0), 0) / recent.length;
        const avgProjections = recent.reduce((sum, cycle) => sum + cycle.totalProjections, 0) / recent.length;
        const avgEvBets = recent.reduce((sum, cycle) => sum + cycle.totalEvBets, 0) / recent.length;

        console.log(`Average Duration (last 5): ${avgDuration.toFixed(1)}ms`);
        console.log(`Average Projections (last 5): ${avgProjections.toFixed(0)}`);
        console.log(`Average EV Bets (last 5): ${avgEvBets.toFixed(0)}`);

        // Calculate trend
        if (recent.length > 1) {
            const firstDuration = recent[0].duration || 0;
            const lastDuration = recent[recent.length - 1].duration || 0;
            const trend = ((lastDuration - firstDuration) / firstDuration) * 100;
            
            const trendIcon = trend > 5 ? '📈' : trend < -5 ? '📉' : '➡️';
            console.log(`Performance Trend: ${trendIcon} ${trend > 0 ? '+' : ''}${trend.toFixed(1)}%`);
        }
    }

    public displayAlerts(report: PerformanceReport): void {
        if (!this.isDisplayEnabled) return;

        const alerts: string[] = [];

        // Check cycle time alerts
        if (report.cycleMetrics.duration! > 15000) {
            alerts.push('🚨 CRITICAL: Cycle time exceeds 15 seconds');
        } else if (report.cycleMetrics.duration! > 8000) {
            alerts.push('⚠️  WARNING: Cycle time exceeds 8 seconds');
        }

        // Check error rate alerts
        if (report.networkEfficiency.errorRate > 25) {
            alerts.push('🚨 CRITICAL: Error rate exceeds 25%');
        } else if (report.networkEfficiency.errorRate > 10) {
            alerts.push('⚠️  WARNING: Error rate exceeds 10%');
        }

        // Check memory usage alerts
        const memUsageMB = report.systemMetrics.memoryUsage.rss / 1024 / 1024;
        if (memUsageMB > 2048) {
            alerts.push('🚨 CRITICAL: Memory usage exceeds 2GB');
        } else if (memUsageMB > 1024) {
            alerts.push('⚠️  WARNING: Memory usage exceeds 1GB');
        }

        // Display alerts if any
        if (alerts.length > 0) {
            console.log('\n🚨 ALERTS:');
            alerts.forEach(alert => console.log(`  ${alert}`));
        }
    }

    public displayComparisonMetrics(report: PerformanceReport): void {
        if (!this.isDisplayEnabled || !report.comparisonMetrics) return;

        console.log('\n📊 COMPARISON METRICS');
        console.log('-'.repeat(30));

        const improvement = report.comparisonMetrics.improvementVsBaseline;
        const targetAchievement = report.comparisonMetrics.targetAchievement;

        const improvementIcon = improvement > 0 ? '🚀' : improvement < 0 ? '📉' : '➡️';
        const targetIcon = targetAchievement >= 100 ? '🎯' : targetAchievement >= 80 ? '🔶' : '🔴';

        console.log(`${improvementIcon} vs Baseline: ${improvement > 0 ? '+' : ''}${improvement.toFixed(1)}%`);
        console.log(`${targetIcon} Target Achievement: ${targetAchievement.toFixed(1)}%`);

        if (targetAchievement >= 100) {
            console.log('🎉 Performance target achieved!');
        } else {
            const timeToTarget = (5000 / report.cycleMetrics.duration!) * 100;
            console.log(`Need ${(100 - targetAchievement).toFixed(1)}% improvement to reach target`);
        }
    }

    private calculateAverageResponseTime(providers: ProviderMetrics[]): number {
        const allTimings = providers.flatMap(p => p.networkTimings);
        if (allTimings.length === 0) return 0;
        
        return allTimings.reduce((sum, timing) => sum + timing.total, 0) / allTimings.length;
    }

    public generateASCIIChart(data: number[], label: string, width: number = 50): void {
        if (!this.isDisplayEnabled || data.length === 0) return;

        const max = Math.max(...data);
        const min = Math.min(...data);
        const range = max - min || 1;

        console.log(`\n📊 ${label}`);
        console.log('-'.repeat(width + 10));

        data.forEach((value, index) => {
            const normalizedValue = ((value - min) / range) * width;
            const bar = '█'.repeat(Math.round(normalizedValue));
            const padding = ' '.repeat(Math.max(0, width - Math.round(normalizedValue)));
            console.log(`${index.toString().padStart(3)}: ${bar}${padding} ${value.toFixed(1)}`);
        });
    }
}

export default PerformanceDashboard;
