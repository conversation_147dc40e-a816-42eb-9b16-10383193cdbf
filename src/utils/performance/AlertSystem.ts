import { PerformanceConfig, ProviderTimeoutConfig } from './config';
import { ProviderMetrics } from './PerformanceAnalytics';

export enum AlertLevel {
    INFO = 'INFO',
    WARNING = 'WARNING',
    CRITICAL = 'CRITICAL'
}

export interface Alert {
    level: AlertLevel;
    message: string;
    timestamp: Date;
    providerName?: string;
    metric?: string;
    value?: number;
    threshold?: number;
}

export class AlertSystem {
    private static instance: AlertSystem;
    private config: PerformanceConfig;
    private alertHistory: Alert[] = [];
    private maxHistorySize = 100;

    private constructor(config: PerformanceConfig) {
        this.config = config;
    }

    public static getInstance(config?: PerformanceConfig): AlertSystem {
        if (!AlertSystem.instance && config) {
            AlertSystem.instance = new AlertSystem(config);
        }
        return AlertSystem.instance;
    }

    public checkProviderPerformance(metrics: ProviderMetrics): Alert[] {
        const alerts: Alert[] = [];
        
        if (!metrics.duration) return alerts;

        const providerTimeout = this.config.providerTimeouts[metrics.name];
        
        // Check provider-specific timeout alerts
        if (providerTimeout) {
            if (metrics.duration > providerTimeout.totalTimeout) {
                alerts.push({
                    level: AlertLevel.CRITICAL,
                    message: `${metrics.name} exceeded total timeout limit`,
                    timestamp: new Date(),
                    providerName: metrics.name,
                    metric: 'duration',
                    value: metrics.duration,
                    threshold: providerTimeout.totalTimeout
                });
            } else if (metrics.duration > providerTimeout.totalTimeout * 0.8) {
                alerts.push({
                    level: AlertLevel.WARNING,
                    message: `${metrics.name} approaching timeout limit`,
                    timestamp: new Date(),
                    providerName: metrics.name,
                    metric: 'duration',
                    value: metrics.duration,
                    threshold: providerTimeout.totalTimeout
                });
            }
        }

        // Check general performance thresholds
        if (metrics.duration > this.config.alertThresholds.providerTimeoutCritical) {
            alerts.push({
                level: AlertLevel.CRITICAL,
                message: `${metrics.name} exceeded critical performance threshold`,
                timestamp: new Date(),
                providerName: metrics.name,
                metric: 'duration',
                value: metrics.duration,
                threshold: this.config.alertThresholds.providerTimeoutCritical
            });
        } else if (metrics.duration > this.config.alertThresholds.providerTimeoutWarning) {
            alerts.push({
                level: AlertLevel.WARNING,
                message: `${metrics.name} exceeded warning performance threshold`,
                timestamp: new Date(),
                providerName: metrics.name,
                metric: 'duration',
                value: metrics.duration,
                threshold: this.config.alertThresholds.providerTimeoutWarning
            });
        }

        // Check error rate
        if (metrics.errors.length > 0) {
            const errorRate = (metrics.errors.length / (metrics.networkRequests || 1)) * 100;
            
            if (errorRate > this.config.alertThresholds.errorRateCritical) {
                alerts.push({
                    level: AlertLevel.CRITICAL,
                    message: `${metrics.name} has critical error rate`,
                    timestamp: new Date(),
                    providerName: metrics.name,
                    metric: 'errorRate',
                    value: errorRate,
                    threshold: this.config.alertThresholds.errorRateCritical
                });
            } else if (errorRate > this.config.alertThresholds.errorRateWarning) {
                alerts.push({
                    level: AlertLevel.WARNING,
                    message: `${metrics.name} has elevated error rate`,
                    timestamp: new Date(),
                    providerName: metrics.name,
                    metric: 'errorRate',
                    value: errorRate,
                    threshold: this.config.alertThresholds.errorRateWarning
                });
            }
        }

        // Check retry count
        if (metrics.retryCount > 2) {
            alerts.push({
                level: AlertLevel.WARNING,
                message: `${metrics.name} required excessive retries`,
                timestamp: new Date(),
                providerName: metrics.name,
                metric: 'retryCount',
                value: metrics.retryCount,
                threshold: 2
            });
        }

        // Store alerts in history
        alerts.forEach(alert => this.addAlert(alert));

        return alerts;
    }

    public checkCyclePerformance(cycleMetrics: any): Alert[] {
        const alerts: Alert[] = [];

        if (cycleMetrics.duration > this.config.alertThresholds.cycleTimeCritical) {
            alerts.push({
                level: AlertLevel.CRITICAL,
                message: 'Cycle time exceeded critical threshold',
                timestamp: new Date(),
                metric: 'cycleTime',
                value: cycleMetrics.duration,
                threshold: this.config.alertThresholds.cycleTimeCritical
            });
        } else if (cycleMetrics.duration > this.config.alertThresholds.cycleTimeWarning) {
            alerts.push({
                level: AlertLevel.WARNING,
                message: 'Cycle time exceeded warning threshold',
                timestamp: new Date(),
                metric: 'cycleTime',
                value: cycleMetrics.duration,
                threshold: this.config.alertThresholds.cycleTimeWarning
            });
        }

        // Store alerts in history
        alerts.forEach(alert => this.addAlert(alert));

        return alerts;
    }

    public checkMemoryUsage(): Alert[] {
        const alerts: Alert[] = [];
        const memUsage = process.memoryUsage();
        const rssInMB = memUsage.rss / 1024 / 1024;

        if (rssInMB > this.config.alertThresholds.memoryUsageCritical) {
            alerts.push({
                level: AlertLevel.CRITICAL,
                message: 'Memory usage exceeded critical threshold',
                timestamp: new Date(),
                metric: 'memoryUsage',
                value: rssInMB,
                threshold: this.config.alertThresholds.memoryUsageCritical
            });
        } else if (rssInMB > this.config.alertThresholds.memoryUsageWarning) {
            alerts.push({
                level: AlertLevel.WARNING,
                message: 'Memory usage exceeded warning threshold',
                timestamp: new Date(),
                metric: 'memoryUsage',
                value: rssInMB,
                threshold: this.config.alertThresholds.memoryUsageWarning
            });
        }

        // Store alerts in history
        alerts.forEach(alert => this.addAlert(alert));

        return alerts;
    }

    private addAlert(alert: Alert): void {
        this.alertHistory.unshift(alert);
        
        // Trim history to max size
        if (this.alertHistory.length > this.maxHistorySize) {
            this.alertHistory = this.alertHistory.slice(0, this.maxHistorySize);
        }

        // Log alert immediately if enabled
        if (this.config.enableAutomatedAlerts) {
            this.logAlert(alert);
        }
    }

    private logAlert(alert: Alert): void {
        const icon = alert.level === AlertLevel.CRITICAL ? '🚨' : 
                    alert.level === AlertLevel.WARNING ? '⚠️' : 'ℹ️';
        
        const message = `${icon} [${alert.level}] ${alert.message}`;
        const details = alert.value && alert.threshold ? 
            ` (${alert.value.toFixed(1)} > ${alert.threshold})` : '';
        
        console.log(`${message}${details}`);
    }

    public getRecentAlerts(count: number = 10): Alert[] {
        return this.alertHistory.slice(0, count);
    }

    public getAlertsByLevel(level: AlertLevel): Alert[] {
        return this.alertHistory.filter(alert => alert.level === level);
    }

    public getAlertsByProvider(providerName: string): Alert[] {
        return this.alertHistory.filter(alert => alert.providerName === providerName);
    }

    public clearAlerts(): void {
        this.alertHistory = [];
    }

    public generateAlertSummary(): string {
        const criticalCount = this.getAlertsByLevel(AlertLevel.CRITICAL).length;
        const warningCount = this.getAlertsByLevel(AlertLevel.WARNING).length;
        const infoCount = this.getAlertsByLevel(AlertLevel.INFO).length;

        return `Alerts: ${criticalCount} Critical, ${warningCount} Warning, ${infoCount} Info`;
    }
}

export default AlertSystem;
