import { performance } from 'perf_hooks';
import PerformanceAnalytics, { NetworkTiming } from './PerformanceAnalytics';

export class NetworkTimer {
    private startTime: number;
    private dnsStart?: number;
    private connectStart?: number;
    private tlsStart?: number;
    private requestStart?: number;
    private responseStart?: number;
    private providerName: string;

    constructor(providerName: string) {
        this.providerName = providerName;
        this.startTime = performance.now();
    }

    markDnsStart(): void {
        this.dnsStart = performance.now();
    }

    markConnectStart(): void {
        this.connectStart = performance.now();
    }

    markTlsStart(): void {
        this.tlsStart = performance.now();
    }

    markRequestStart(): void {
        this.requestStart = performance.now();
    }

    markResponseStart(): void {
        this.responseStart = performance.now();
    }

    finish(): NetworkTiming {
        const endTime = performance.now();
        const total = endTime - this.startTime;

        const timing: NetworkTiming = {
            total,
            dnsLookup: this.connectStart && this.dnsStart ? this.connectStart - this.dnsStart : undefined,
            tcpConnect: this.tlsStart && this.connectStart ? this.tlsStart - this.connectStart : undefined,
            tlsHandshake: this.requestStart && this.tlsStart ? this.requestStart - this.tlsStart : undefined,
            firstByte: this.responseStart && this.requestStart ? this.responseStart - this.requestStart : undefined,
            contentDownload: this.responseStart ? endTime - this.responseStart : undefined
        };

        // Record the timing with the performance analytics
        PerformanceAnalytics.getInstance().recordNetworkRequest(this.providerName, timing);

        return timing;
    }
}

// Wrapper functions for common HTTP clients
export function wrapUndiciRequest(originalFetch: any, providerName: string) {
    return async function(url: string, options: any = {}) {
        const timer = new NetworkTimer(providerName);
        
        try {
            timer.markRequestStart();
            const response = await originalFetch(url, options);
            timer.markResponseStart();
            
            // Wrap the response to capture content download timing
            const originalJson = response.json;
            const originalText = response.text;
            
            if (originalJson) {
                response.json = async function() {
                    const result = await originalJson.call(this);
                    timer.finish();
                    return result;
                };
            }
            
            if (originalText) {
                response.text = async function() {
                    const result = await originalText.call(this);
                    timer.finish();
                    return result;
                };
            }
            
            return response;
        } catch (error) {
            timer.finish();
            throw error;
        }
    };
}

export function wrapCycleTLSRequest(originalCycleTLS: any, providerName: string) {
    return async function(url: string, options: any = {}, method: string = 'GET') {
        const timer = new NetworkTimer(providerName);
        
        try {
            timer.markRequestStart();
            const response = await originalCycleTLS(url, options, method);
            timer.finish();
            return response;
        } catch (error) {
            timer.finish();
            throw error;
        }
    };
}

// Database timing wrapper
export function wrapDatabaseOperation(operation: Function, operationName: string, providerName: string) {
    return async function(this: any, ...args: any[]) {
        const startTime = performance.now();

        try {
            const result = await operation.apply(this, args);
            const duration = performance.now() - startTime;

            if (operationName === 'store') {
                PerformanceAnalytics.getInstance().recordProviderStore(providerName, duration);
            }

            return result;
        } catch (error) {
            const duration = performance.now() - startTime;
            PerformanceAnalytics.getInstance().recordProviderError(providerName, `${operationName} failed: ${error}`);
            throw error;
        }
    };
}

// Memory monitoring utility
export function captureMemorySnapshot(label: string): NodeJS.MemoryUsage {
    const memUsage = process.memoryUsage();
    
    if (process.env.NODE_ENV === 'development') {
        console.log(`[MEMORY] ${label}:`, {
            rss: `${(memUsage.rss / 1024 / 1024).toFixed(2)} MB`,
            heapUsed: `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
            heapTotal: `${(memUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
            external: `${(memUsage.external / 1024 / 1024).toFixed(2)} MB`
        });
    }
    
    return memUsage;
}

// Utility to measure function execution time
export function measureExecutionTime<T>(
    fn: () => Promise<T> | T,
    label: string,
    providerName?: string
): Promise<{ result: T; duration: number }> {
    return new Promise(async (resolve, reject) => {
        const startTime = performance.now();
        
        try {
            const result = await fn();
            const duration = performance.now() - startTime;
            
            if (providerName && process.env.NODE_ENV === 'development') {
                console.log(`[TIMING] ${providerName} - ${label}: ${duration.toFixed(2)}ms`);
            }
            
            resolve({ result, duration });
        } catch (error) {
            const duration = performance.now() - startTime;
            
            if (providerName) {
                PerformanceAnalytics.getInstance().recordProviderError(
                    providerName, 
                    `${label} failed after ${duration.toFixed(2)}ms: ${error}`
                );
            }
            
            reject(error);
        }
    });
}

// Proxy performance tracking
export function trackProxyPerformance(proxyUrl: string, providerName: string) {
    const startTime = performance.now();
    
    return {
        recordSuccess: () => {
            const duration = performance.now() - startTime;
            // This could be extended to track proxy-specific metrics
            console.log(`[PROXY] ${providerName} - ${proxyUrl.split('@')[1] || 'direct'}: ${duration.toFixed(2)}ms`);
        },
        recordFailure: (error: any) => {
            const duration = performance.now() - startTime;
            PerformanceAnalytics.getInstance().recordProviderError(
                providerName, 
                `Proxy failure (${proxyUrl.split('@')[1] || 'direct'}) after ${duration.toFixed(2)}ms: ${error}`
            );
        }
    };
}

// Concurrency efficiency calculator
export function calculateConcurrencyEfficiency(
    actualDuration: number,
    individualDurations: number[]
): number {
    const theoreticalSequentialTime = individualDurations.reduce((sum, duration) => sum + duration, 0);
    return theoreticalSequentialTime > 0 ? (theoreticalSequentialTime / actualDuration) * 100 : 0;
}

// Performance decorator for provider methods
export function performanceMonitor(providerName: string, operationType: 'fetch' | 'process' | 'store') {
    return function(target: any, propertyName: string, descriptor: PropertyDescriptor) {
        const method = descriptor.value;
        
        descriptor.value = async function(...args: any[]) {
            const analytics = PerformanceAnalytics.getInstance();
            const startTime = performance.now();
            
            try {
                const result = await method.apply(this, args);
                const duration = performance.now() - startTime;
                
                switch (operationType) {
                    case 'fetch':
                        analytics.recordProviderFetch(providerName, duration);
                        break;
                    case 'process':
                        analytics.recordProviderProcess(providerName, duration);
                        break;
                    case 'store':
                        analytics.recordProviderStore(providerName, duration);
                        break;
                }
                
                return result;
            } catch (error) {
                const duration = performance.now() - startTime;
                analytics.recordProviderError(providerName, `${operationType} failed after ${duration.toFixed(2)}ms: ${error}`);
                throw error;
            }
        };
        
        return descriptor;
    };
}

export default {
    NetworkTimer,
    wrapUndiciRequest,
    wrapCycleTLSRequest,
    wrapDatabaseOperation,
    captureMemorySnapshot,
    measureExecutionTime,
    trackProxyPerformance,
    calculateConcurrencyEfficiency,
    performanceMonitor
};
