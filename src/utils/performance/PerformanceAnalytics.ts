import fs from 'fs';
import path from 'path';
import { performance } from 'perf_hooks';

export enum MonitoringLevel {
    BASIC = 'BASIC',
    DETAILED = 'DETAILED', 
    DEBUG = 'DEBUG'
}

export interface NetworkTiming {
    dnsLookup?: number;
    tcpConnect?: number;
    tlsHandshake?: number;
    firstByte?: number;
    contentDownload?: number;
    total: number;
}

export interface ProviderMetrics {
    name: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    fetchTime?: number;
    processTime?: number;
    storeTime?: number;
    networkRequests: number;
    totalProjections: number;
    evBetsFound: number;
    errors: string[];
    networkTimings: NetworkTiming[];
    memoryUsage?: NodeJS.MemoryUsage;
    proxyUsed?: string;
    retryCount: number;
}

export interface CycleMetrics {
    cycleNumber: number;
    startTime: number;
    endTime?: number;
    duration?: number;
    sharpProjectionsFetchTime?: number;
    totalProviders: number;
    successfulProviders: number;
    failedProviders: number;
    totalProjections: number;
    totalEvBets: number;
    concurrencyEfficiency?: number;
    memoryUsage?: NodeJS.MemoryUsage;
    providerMetrics: Map<string, ProviderMetrics>;
}

export interface PerformanceReport {
    timestamp: string;
    cycleMetrics: CycleMetrics;
    systemMetrics: {
        cpuUsage?: NodeJS.CpuUsage;
        memoryUsage: NodeJS.MemoryUsage;
        uptime: number;
    };
    networkEfficiency: {
        totalRequests: number;
        averageResponseTime: number;
        requestsPerSecond: number;
        errorRate: number;
    };
    bottleneckAnalysis: {
        slowestProvider: string;
        slowestOperation: string;
        recommendations: string[];
    };
    comparisonMetrics?: {
        improvementVsBaseline: number;
        targetAchievement: number;
    };
}

class PerformanceAnalytics {
    private static instance: PerformanceAnalytics;
    private monitoringLevel: MonitoringLevel = MonitoringLevel.DETAILED;
    private currentCycle?: CycleMetrics;
    private historicalData: CycleMetrics[] = [];
    private baselineMetrics?: CycleMetrics;
    private performanceTarget = 5000; // 5 seconds target
    private logDirectory: string;
    private startCpuUsage?: NodeJS.CpuUsage;

    private constructor() {
        this.logDirectory = path.join(process.cwd(), 'logs', 'performance');
        this.ensureLogDirectory();
    }

    public static getInstance(): PerformanceAnalytics {
        if (!PerformanceAnalytics.instance) {
            PerformanceAnalytics.instance = new PerformanceAnalytics();
        }
        return PerformanceAnalytics.instance;
    }

    public setMonitoringLevel(level: MonitoringLevel): void {
        this.monitoringLevel = level;
        console.log(`[PERFORMANCE] Monitoring level set to: ${level}`);
    }

    public setBaselineMetrics(metrics: CycleMetrics): void {
        this.baselineMetrics = metrics;
        console.log(`[PERFORMANCE] Baseline metrics set - Duration: ${metrics.duration}ms`);
    }

    private ensureLogDirectory(): void {
        if (!fs.existsSync(this.logDirectory)) {
            fs.mkdirSync(this.logDirectory, { recursive: true });
        }
    }

    // Cycle-level timing
    public startCycle(cycleNumber: number): void {
        this.startCpuUsage = process.cpuUsage();
        this.currentCycle = {
            cycleNumber,
            startTime: performance.now(),
            totalProviders: 0,
            successfulProviders: 0,
            failedProviders: 0,
            totalProjections: 0,
            totalEvBets: 0,
            providerMetrics: new Map()
        };

        if (this.monitoringLevel !== MonitoringLevel.BASIC) {
            console.log(`[PERFORMANCE] 🚀 Cycle #${cycleNumber} started`);
        }
    }

    public recordSharpProjectionsFetch(duration: number): void {
        if (this.currentCycle) {
            this.currentCycle.sharpProjectionsFetchTime = duration;
        }
    }

    public endCycle(): CycleMetrics | undefined {
        if (!this.currentCycle) return undefined;

        this.currentCycle.endTime = performance.now();
        this.currentCycle.duration = this.currentCycle.endTime - this.currentCycle.startTime;
        this.currentCycle.memoryUsage = process.memoryUsage();

        // Calculate concurrency efficiency
        const theoreticalTime = Array.from(this.currentCycle.providerMetrics.values())
            .reduce((sum, provider) => sum + (provider.duration || 0), 0);
        this.currentCycle.concurrencyEfficiency = theoreticalTime > 0 ? 
            (theoreticalTime / this.currentCycle.duration) * 100 : 0;

        this.historicalData.push({ ...this.currentCycle });
        
        // Generate and log performance report
        const report = this.generatePerformanceReport();
        this.logPerformanceReport(report);
        
        if (this.monitoringLevel !== MonitoringLevel.BASIC) {
            this.displayRealTimeReport(report);
        }

        const completedCycle = this.currentCycle;
        this.currentCycle = undefined;
        return completedCycle;
    }

    // Provider-level timing
    public startProvider(providerName: string): void {
        if (!this.currentCycle) return;

        const metrics: ProviderMetrics = {
            name: providerName,
            startTime: performance.now(),
            networkRequests: 0,
            totalProjections: 0,
            evBetsFound: 0,
            errors: [],
            networkTimings: [],
            retryCount: 0
        };

        this.currentCycle.providerMetrics.set(providerName, metrics);
        this.currentCycle.totalProviders++;
    }

    public recordProviderFetch(providerName: string, duration: number): void {
        const metrics = this.currentCycle?.providerMetrics.get(providerName);
        if (metrics) {
            metrics.fetchTime = duration;
        }
    }

    public recordProviderProcess(providerName: string, duration: number): void {
        const metrics = this.currentCycle?.providerMetrics.get(providerName);
        if (metrics) {
            metrics.processTime = duration;
        }
    }

    public recordProviderStore(providerName: string, duration: number): void {
        const metrics = this.currentCycle?.providerMetrics.get(providerName);
        if (metrics) {
            metrics.storeTime = duration;
        }
    }

    public recordNetworkRequest(providerName: string, timing: NetworkTiming): void {
        const metrics = this.currentCycle?.providerMetrics.get(providerName);
        if (metrics) {
            metrics.networkRequests++;
            metrics.networkTimings.push(timing);
        }
    }

    public recordProviderResults(providerName: string, projections: number, evBets: number): void {
        const metrics = this.currentCycle?.providerMetrics.get(providerName);
        if (metrics && this.currentCycle) {
            metrics.totalProjections = projections;
            metrics.evBetsFound = evBets;
            this.currentCycle.totalProjections += projections;
            this.currentCycle.totalEvBets += evBets;
        }
    }

    public recordProviderError(providerName: string, error: string): void {
        const metrics = this.currentCycle?.providerMetrics.get(providerName);
        if (metrics) {
            metrics.errors.push(error);
        }
    }

    public recordProviderRetry(providerName: string): void {
        const metrics = this.currentCycle?.providerMetrics.get(providerName);
        if (metrics) {
            metrics.retryCount++;
        }
    }

    public endProvider(providerName: string, success: boolean = true): void {
        const metrics = this.currentCycle?.providerMetrics.get(providerName);
        if (metrics && this.currentCycle) {
            metrics.endTime = performance.now();
            metrics.duration = metrics.endTime - metrics.startTime;
            
            if (this.monitoringLevel === MonitoringLevel.DEBUG) {
                metrics.memoryUsage = process.memoryUsage();
            }

            if (success) {
                this.currentCycle.successfulProviders++;
            } else {
                this.currentCycle.failedProviders++;
            }

            if (this.monitoringLevel === MonitoringLevel.DEBUG) {
                console.log(`[PERFORMANCE] ${providerName} completed in ${metrics.duration?.toFixed(1)}ms`);
            }
        }
    }

    private generatePerformanceReport(): PerformanceReport {
        if (!this.currentCycle) {
            throw new Error('No active cycle to generate report from');
        }

        const networkTimings = Array.from(this.currentCycle.providerMetrics.values())
            .flatMap(provider => provider.networkTimings);
        
        const totalRequests = networkTimings.length;
        const averageResponseTime = totalRequests > 0 ? 
            networkTimings.reduce((sum, timing) => sum + timing.total, 0) / totalRequests : 0;
        
        const errorCount = Array.from(this.currentCycle.providerMetrics.values())
            .reduce((sum, provider) => sum + provider.errors.length, 0);

        const slowestProvider = Array.from(this.currentCycle.providerMetrics.values())
            .reduce((slowest, current) => 
                (current.duration || 0) > (slowest.duration || 0) ? current : slowest
            );

        const recommendations = this.generateRecommendations();

        return {
            timestamp: new Date().toISOString(),
            cycleMetrics: this.currentCycle,
            systemMetrics: {
                cpuUsage: this.startCpuUsage ? process.cpuUsage(this.startCpuUsage) : undefined,
                memoryUsage: process.memoryUsage(),
                uptime: process.uptime()
            },
            networkEfficiency: {
                totalRequests,
                averageResponseTime,
                requestsPerSecond: totalRequests / ((this.currentCycle.duration || 1) / 1000),
                errorRate: totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0
            },
            bottleneckAnalysis: {
                slowestProvider: slowestProvider.name,
                slowestOperation: this.identifySlowOperation(slowestProvider),
                recommendations
            },
            comparisonMetrics: this.baselineMetrics ? {
                improvementVsBaseline: ((this.baselineMetrics.duration! - this.currentCycle.duration!) / this.baselineMetrics.duration!) * 100,
                targetAchievement: (this.performanceTarget / this.currentCycle.duration!) * 100
            } : undefined
        };
    }

    private identifySlowOperation(provider: ProviderMetrics): string {
        const operations = {
            fetch: provider.fetchTime || 0,
            process: provider.processTime || 0,
            store: provider.storeTime || 0
        };

        return Object.entries(operations).reduce((slowest, [op, time]) => 
            time > operations[slowest] ? op : slowest, 'fetch'
        );
    }

    private generateRecommendations(): string[] {
        const recommendations: string[] = [];
        
        if (!this.currentCycle) return recommendations;

        // Analyze cycle duration
        if (this.currentCycle.duration! > this.performanceTarget) {
            recommendations.push(`Cycle duration (${this.currentCycle.duration!.toFixed(1)}ms) exceeds target (${this.performanceTarget}ms)`);
        }

        // Analyze provider performance
        const slowProviders = Array.from(this.currentCycle.providerMetrics.values())
            .filter(provider => (provider.duration || 0) > 10000) // > 10 seconds
            .map(provider => provider.name);

        if (slowProviders.length > 0) {
            recommendations.push(`Slow providers detected: ${slowProviders.join(', ')}`);
        }

        // Analyze error rates
        const errorRate = Array.from(this.currentCycle.providerMetrics.values())
            .reduce((sum, provider) => sum + provider.errors.length, 0) / this.currentCycle.totalProviders;

        if (errorRate > 0.1) {
            recommendations.push(`High error rate detected: ${(errorRate * 100).toFixed(1)}%`);
        }

        return recommendations;
    }

    private displayRealTimeReport(report: PerformanceReport): void {
        console.log('\n' + '='.repeat(80));
        console.log(`🎯 PERFORMANCE REPORT - Cycle #${report.cycleMetrics.cycleNumber}`);
        console.log('='.repeat(80));
        
        // Overall metrics
        console.log(`⏱️  Total Duration: ${report.cycleMetrics.duration!.toFixed(1)}ms`);
        console.log(`📊 Providers: ${report.cycleMetrics.successfulProviders}/${report.cycleMetrics.totalProviders} successful`);
        console.log(`📈 Projections: ${report.cycleMetrics.totalProjections} total, ${report.cycleMetrics.totalEvBets} EV bets`);
        console.log(`🚀 Concurrency Efficiency: ${report.cycleMetrics.concurrencyEfficiency!.toFixed(1)}%`);
        
        if (report.comparisonMetrics) {
            console.log(`📉 vs Baseline: ${report.comparisonMetrics.improvementVsBaseline > 0 ? '+' : ''}${report.comparisonMetrics.improvementVsBaseline.toFixed(1)}%`);
            console.log(`🎯 Target Achievement: ${report.comparisonMetrics.targetAchievement.toFixed(1)}%`);
        }

        // Network efficiency
        console.log(`🌐 Network: ${report.networkEfficiency.totalRequests} requests, ${report.networkEfficiency.averageResponseTime.toFixed(1)}ms avg`);
        
        // Top 3 slowest providers
        const sortedProviders = Array.from(report.cycleMetrics.providerMetrics.values())
            .sort((a, b) => (b.duration || 0) - (a.duration || 0))
            .slice(0, 3);

        console.log('\n🐌 Slowest Providers:');
        sortedProviders.forEach((provider, index) => {
            console.log(`  ${index + 1}. ${provider.name}: ${provider.duration!.toFixed(1)}ms (F:${provider.fetchTime || 0}ms P:${provider.processTime || 0}ms S:${provider.storeTime || 0}ms)`);
        });

        // Recommendations
        if (report.bottleneckAnalysis.recommendations.length > 0) {
            console.log('\n💡 Recommendations:');
            report.bottleneckAnalysis.recommendations.forEach(rec => {
                console.log(`  • ${rec}`);
            });
        }

        console.log('='.repeat(80) + '\n');
    }

    private logPerformanceReport(report: PerformanceReport): void {
        const filename = `performance_${new Date().toISOString().split('T')[0]}.json`;
        const filepath = path.join(this.logDirectory, filename);
        
        try {
            let existingData: any[] = [];
            if (fs.existsSync(filepath)) {
                const content = fs.readFileSync(filepath, 'utf8');
                existingData = JSON.parse(content);
            }
            
            existingData.push(report);
            fs.writeFileSync(filepath, JSON.stringify(existingData, null, 2));
        } catch (error) {
            console.error('[PERFORMANCE] Error logging performance report:', error);
        }
    }

    public getHistoricalData(): CycleMetrics[] {
        return [...this.historicalData];
    }

    public generateTrendAnalysis(): any {
        if (this.historicalData.length < 2) return null;

        const recent = this.historicalData.slice(-5); // Last 5 cycles
        const avgDuration = recent.reduce((sum, cycle) => sum + (cycle.duration || 0), 0) / recent.length;
        const avgProjections = recent.reduce((sum, cycle) => sum + cycle.totalProjections, 0) / recent.length;

        return {
            averageDuration: avgDuration,
            averageProjections: avgProjections,
            trend: recent.length > 1 ? 
                (recent[recent.length - 1].duration! - recent[0].duration!) / recent[0].duration! * 100 : 0
        };
    }
}

export default PerformanceAnalytics;
