import { MonitoringLevel } from './PerformanceAnalytics';

export interface PerformanceConfig {
    monitoringLevel: MonitoringLevel;
    enableNetworkTiming: boolean;
    enableMemoryTracking: boolean;
    enableDatabaseTiming: boolean;
    enableProxyTracking: boolean;
    performanceTarget: number; // Target execution time in milliseconds
    reportingInterval: number; // How often to generate reports (in cycles)
    historicalDataRetention: number; // How many cycles to keep in memory
    logToFile: boolean;
    logDirectory: string;
    enableRealTimeDisplay: boolean;
    enableTrendAnalysis: boolean;
    alertThresholds: {
        cycleTimeWarning: number; // ms
        cycleTimeCritical: number; // ms
        errorRateWarning: number; // percentage
        errorRateCritical: number; // percentage
        memoryUsageWarning: number; // MB
        memoryUsageCritical: number; // MB
    };
}

// Default configuration
export const defaultConfig: PerformanceConfig = {
    monitoringLevel: MonitoringLevel.DETAILED,
    enableNetworkTiming: true,
    enableMemoryTracking: true,
    enableDatabaseTiming: true,
    enableProxyTracking: true,
    performanceTarget: 5000, // 5 seconds
    reportingInterval: 1, // Every cycle
    historicalDataRetention: 50, // Keep last 50 cycles
    logToFile: true,
    logDirectory: './logs/performance',
    enableRealTimeDisplay: true,
    enableTrendAnalysis: true,
    alertThresholds: {
        cycleTimeWarning: 8000, // 8 seconds
        cycleTimeCritical: 15000, // 15 seconds
        errorRateWarning: 10, // 10%
        errorRateCritical: 25, // 25%
        memoryUsageWarning: 1024, // 1GB
        memoryUsageCritical: 2048 // 2GB
    }
};

// Environment-specific configurations
export const developmentConfig: Partial<PerformanceConfig> = {
    monitoringLevel: MonitoringLevel.DEBUG,
    enableRealTimeDisplay: true,
    reportingInterval: 1
};

export const productionConfig: Partial<PerformanceConfig> = {
    monitoringLevel: MonitoringLevel.BASIC,
    enableRealTimeDisplay: false,
    reportingInterval: 5, // Every 5 cycles
    enableMemoryTracking: false
};

export const testingConfig: Partial<PerformanceConfig> = {
    monitoringLevel: MonitoringLevel.DETAILED,
    enableRealTimeDisplay: false,
    logToFile: false,
    reportingInterval: 1
};

// Function to get configuration based on environment
export function getPerformanceConfig(): PerformanceConfig {
    const env = process.env.NODE_ENV || 'development';
    let config = { ...defaultConfig };

    switch (env) {
        case 'development':
            config = { ...config, ...developmentConfig };
            break;
        case 'production':
            config = { ...config, ...productionConfig };
            break;
        case 'test':
            config = { ...config, ...testingConfig };
            break;
    }

    // Override with environment variables if present
    if (process.env.PERFORMANCE_MONITORING_LEVEL) {
        config.monitoringLevel = process.env.PERFORMANCE_MONITORING_LEVEL as MonitoringLevel;
    }

    if (process.env.PERFORMANCE_TARGET) {
        config.performanceTarget = parseInt(process.env.PERFORMANCE_TARGET, 10);
    }

    if (process.env.PERFORMANCE_LOG_DIRECTORY) {
        config.logDirectory = process.env.PERFORMANCE_LOG_DIRECTORY;
    }

    if (process.env.PERFORMANCE_ENABLE_REAL_TIME_DISPLAY) {
        config.enableRealTimeDisplay = process.env.PERFORMANCE_ENABLE_REAL_TIME_DISPLAY === 'true';
    }

    return config;
}

// Validation function for configuration
export function validateConfig(config: PerformanceConfig): string[] {
    const errors: string[] = [];

    if (config.performanceTarget <= 0) {
        errors.push('Performance target must be greater than 0');
    }

    if (config.reportingInterval <= 0) {
        errors.push('Reporting interval must be greater than 0');
    }

    if (config.historicalDataRetention <= 0) {
        errors.push('Historical data retention must be greater than 0');
    }

    if (config.alertThresholds.cycleTimeWarning >= config.alertThresholds.cycleTimeCritical) {
        errors.push('Cycle time warning threshold must be less than critical threshold');
    }

    if (config.alertThresholds.errorRateWarning >= config.alertThresholds.errorRateCritical) {
        errors.push('Error rate warning threshold must be less than critical threshold');
    }

    if (config.alertThresholds.memoryUsageWarning >= config.alertThresholds.memoryUsageCritical) {
        errors.push('Memory usage warning threshold must be less than critical threshold');
    }

    return errors;
}

// Helper function to format configuration for display
export function formatConfigForDisplay(config: PerformanceConfig): string {
    return `
Performance Monitoring Configuration:
=====================================
Monitoring Level: ${config.monitoringLevel}
Performance Target: ${config.performanceTarget}ms
Network Timing: ${config.enableNetworkTiming ? 'Enabled' : 'Disabled'}
Memory Tracking: ${config.enableMemoryTracking ? 'Enabled' : 'Disabled'}
Database Timing: ${config.enableDatabaseTiming ? 'Enabled' : 'Disabled'}
Proxy Tracking: ${config.enableProxyTracking ? 'Enabled' : 'Disabled'}
Real-time Display: ${config.enableRealTimeDisplay ? 'Enabled' : 'Disabled'}
Log to File: ${config.logToFile ? 'Enabled' : 'Disabled'}
Log Directory: ${config.logDirectory}
Reporting Interval: Every ${config.reportingInterval} cycle(s)
Historical Retention: ${config.historicalDataRetention} cycles

Alert Thresholds:
- Cycle Time Warning: ${config.alertThresholds.cycleTimeWarning}ms
- Cycle Time Critical: ${config.alertThresholds.cycleTimeCritical}ms
- Error Rate Warning: ${config.alertThresholds.errorRateWarning}%
- Error Rate Critical: ${config.alertThresholds.errorRateCritical}%
- Memory Warning: ${config.alertThresholds.memoryUsageWarning}MB
- Memory Critical: ${config.alertThresholds.memoryUsageCritical}MB
=====================================
    `.trim();
}

export default {
    getPerformanceConfig,
    validateConfig,
    formatConfigForDisplay,
    defaultConfig,
    developmentConfig,
    productionConfig,
    testingConfig
};
