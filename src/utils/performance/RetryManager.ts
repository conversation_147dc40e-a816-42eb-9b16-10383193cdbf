import { ProviderTimeoutConfig, getProviderTimeout } from './config';
import { reportProxySuccess, reportProxyFailure } from '../proxy';

export interface RetryOptions {
    maxRetries?: number;
    retryDelay?: number;
    exponentialBackoff?: boolean;
    timeoutMs?: number;
    enableEarlyTermination?: boolean;
    onRetry?: (attempt: number, error: Error) => void;
    onSuccess?: (attempt: number) => void;
    onFailure?: (finalError: Error, totalAttempts: number) => void;
}

export interface RetryResult<T> {
    success: boolean;
    result?: T;
    error?: Error;
    attempts: number;
    totalDuration: number;
}

export class RetryManager {
    private static instance: RetryManager;
    
    private constructor() {}

    public static getInstance(): RetryManager {
        if (!RetryManager.instance) {
            RetryManager.instance = new RetryManager();
        }
        return RetryManager.instance;
    }

    /**
     * Execute a function with automatic retry logic based on provider configuration
     */
    public async executeWithRetry<T>(
        providerName: string,
        operation: () => Promise<T>,
        customOptions?: Partial<RetryOptions>
    ): Promise<RetryResult<T>> {
        const config = getProviderTimeout(providerName);
        const startTime = Date.now();
        
        const options: RetryOptions = {
            maxRetries: config.maxRetries,
            retryDelay: config.retryDelay,
            exponentialBackoff: false,
            timeoutMs: config.totalTimeout,
            enableEarlyTermination: config.enableEarlyTermination,
            ...customOptions
        };

        let lastError: Error | null = null;
        let attempts = 0;

        for (let attempt = 0; attempt < (options.maxRetries || 3); attempt++) {
            attempts++;
            
            try {
                // Check if we're approaching time limit
                if (options.enableEarlyTermination && options.timeoutMs) {
                    const elapsed = Date.now() - startTime;
                    if (elapsed > options.timeoutMs * 0.8) {
                        console.warn(`[${providerName}] Approaching time limit (${elapsed}ms), stopping retries`);
                        break;
                    }
                }

                console.log(`[${providerName}] Attempt ${attempt + 1}/${options.maxRetries}`);
                
                const result = await this.executeWithTimeout(operation, config.fetchTimeout);
                
                // Success callback
                if (options.onSuccess) {
                    options.onSuccess(attempt + 1);
                }

                return {
                    success: true,
                    result,
                    attempts,
                    totalDuration: Date.now() - startTime
                };

            } catch (error) {
                lastError = error as Error;
                console.log(`[${providerName}] Attempt ${attempt + 1} failed: ${lastError.message}`);
                
                // Retry callback
                if (options.onRetry) {
                    options.onRetry(attempt + 1, lastError);
                }

                // If we're not on the last attempt, wait before retrying
                if (attempt < (options.maxRetries || 3) - 1) {
                    const delay = options.exponentialBackoff 
                        ? (options.retryDelay || 300) * Math.pow(2, attempt)
                        : (options.retryDelay || 300);
                    
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        // All attempts failed
        if (options.onFailure && lastError) {
            options.onFailure(lastError, attempts);
        }

        return {
            success: false,
            error: lastError || new Error('Unknown error'),
            attempts,
            totalDuration: Date.now() - startTime
        };
    }

    /**
     * Execute a function with timeout
     */
    private async executeWithTimeout<T>(
        operation: () => Promise<T>,
        timeoutMs: number
    ): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const timer = setTimeout(() => {
                reject(new Error(`Operation timed out after ${timeoutMs}ms`));
            }, timeoutMs);

            operation()
                .then(result => {
                    clearTimeout(timer);
                    resolve(result);
                })
                .catch(error => {
                    clearTimeout(timer);
                    reject(error);
                });
        });
    }

    /**
     * Execute a network request with retry logic and proxy reporting
     */
    public async executeNetworkRequestWithRetry<T>(
        providerName: string,
        requestFunction: (proxyUrl: string) => Promise<T>,
        getProxyFunction: () => Promise<string>,
        customOptions?: Partial<RetryOptions>
    ): Promise<RetryResult<T>> {
        let currentProxy: string | null = null;

        const wrappedOperation = async (): Promise<T> => {
            currentProxy = await getProxyFunction();
            
            try {
                const result = await requestFunction(currentProxy);
                reportProxySuccess(currentProxy);
                return result;
            } catch (error) {
                reportProxyFailure(currentProxy, error as Error);
                throw error;
            }
        };

        const options: RetryOptions = {
            ...customOptions,
            onRetry: (attempt, error) => {
                console.log(`[${providerName}] Retry ${attempt} with new proxy`);
                if (customOptions?.onRetry) {
                    customOptions.onRetry(attempt, error);
                }
            },
            onSuccess: (attempt) => {
                console.log(`[${providerName}] Success on attempt ${attempt}`);
                if (customOptions?.onSuccess) {
                    customOptions.onSuccess(attempt);
                }
            },
            onFailure: (error, attempts) => {
                console.error(`[${providerName}] All ${attempts} attempts failed: ${error.message}`);
                if (customOptions?.onFailure) {
                    customOptions.onFailure(error, attempts);
                }
            }
        };

        return this.executeWithRetry(providerName, wrappedOperation, options);
    }

    /**
     * Get retry statistics for a provider
     */
    public getRetryStats(providerName: string): {
        recommendedRetries: number;
        recommendedDelay: number;
        timeoutLimit: number;
    } {
        const config = getProviderTimeout(providerName);
        
        return {
            recommendedRetries: config.maxRetries,
            recommendedDelay: config.retryDelay,
            timeoutLimit: config.totalTimeout
        };
    }

    /**
     * Check if a provider should use early termination
     */
    public shouldUseEarlyTermination(providerName: string): boolean {
        const config = getProviderTimeout(providerName);
        return config.enableEarlyTermination;
    }

    /**
     * Calculate optimal retry delay based on provider performance
     */
    public calculateOptimalDelay(
        providerName: string, 
        attempt: number, 
        useExponentialBackoff: boolean = false
    ): number {
        const config = getProviderTimeout(providerName);
        
        if (useExponentialBackoff) {
            return Math.min(config.retryDelay * Math.pow(2, attempt), 5000); // Cap at 5 seconds
        }
        
        return config.retryDelay;
    }
}

export default RetryManager;
