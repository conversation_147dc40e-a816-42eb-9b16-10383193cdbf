class FormatUtility {
  // Removes accents from player names
  static removeDiacritics(str) {
    if (str === null || str === undefined) {
      return "";
    }

    return str.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
  }

  // Function to format a number to one decimal place with no trailing zeros
  static formatToOneDecimal(value) {
    // Convert to a number if it's a string
    if (typeof value === "string") {
      value = parseFloat(value.replace("%", ""));
    }
    // Round to one decimal place and remove trailing zeros
    return parseFloat(value.toFixed(1));
  }
}

export function formatDecimal(value: string | number, digits: number = 1) {
  return parseFloat((parseFloat((value).toString())).toFixed(digits));
}

module.exports = {
  FormatUtility,
  formatDecimal
};
