import { LeagueData, LeagueStatTypeMapping, Model, Translation } from './types';
import { leagueStatTypeMapping } from "./constants";

export function translateStat(league: string, sourcePlatform: string, statType: string, normalizedStatType?: string): Translation {
    let modelInfo: Model | undefined;

    // Special handling for NHL SERIES - do not normalize this league
    if (league === 'NHL SERIES') {
        console.log(`NHL SERIES detected for ${statType} - keeping as separate league`);
        // Use poisson model for NHL SERIES (better for higher values like series-long stats)
        return {
            standardStatType: statType,
            modelInfo: { model: 'poisson' },
            preferredSharp: 'DraftKings'
        };
    }

    const leagueData = leagueStatTypeMapping[league];
    if (!leagueData) {
        return { standardStatType: statType, modelInfo };
    }

    if (!statType) {
        return { modelInfo, standardStatType: statType };
    }

    let standardStatType: string = statType;
    let preferredSharp: string = sourcePlatform;

    for (const [key, value] of Object.entries(leagueData)) {
        const typedValue = value as LeagueData;
        const mapping = typedValue.mapping[sourcePlatform];
        
        // Check if the provider's stat type matches any of the mapped values
        // Use normalized version if available, otherwise do lowercase comparison
        const statTypeToCompare = normalizedStatType || statType.toLowerCase();
        const providerStatMatches = Array.isArray(mapping) 
            ? mapping.some(m => normalizedStatType 
                ? m.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '') === statTypeToCompare
                : m.toLowerCase() === statTypeToCompare)
            : mapping && (normalizedStatType 
                ? mapping.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '') === statTypeToCompare
                : mapping.toLowerCase() === statTypeToCompare);

        // Check if the standard stat type matches the key
        if (providerStatMatches) {
            standardStatType = key;
            modelInfo = typedValue.modelInfo;
            preferredSharp = typedValue.preferredSharp;
            break;
        }
    }

    // console.log(`Translating: ${sourcePlatform} ${statType} → ${standardStatType}`);

    return { standardStatType, modelInfo, preferredSharp };
}