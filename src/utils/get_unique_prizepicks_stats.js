// Import the fetch function. If you are using Node.js v18+,
// you might not need 'node-fetch' and can use the global fetch directly.
// For broader compatibility, we'll use node-fetch here.
const fetch = require('node-fetch'); // Or dynamic import: const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const url = 'https://api.prizepicks.com/projections?per_page=9999';

async function getUniqueStatTypesByLeague() {
    try {
        console.log(`Fetching data from ${url}...`);
        const response = await fetch(url, {
             headers: { // Adding common headers often helps avoid blocking
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Data fetched successfully. Processing...");

        if (!data || !Array.isArray(data.data)) {
            console.log('Invalid data structure received.');
            return;
        }

        // Use an object to store stat types grouped by league ID
        const statsByLeague = {};

        data.data.forEach(projection => {
            const attributes = projection.attributes;
            const relationships = projection.relationships;

            // Safely access nested properties
            const statType = attributes?.stat_type;
            const leagueId = relationships?.league?.data?.id;

            if (statType && leagueId) {
                // If the league ID isn't a key yet, initialize it with a new Set
                if (!statsByLeague[leagueId]) {
                    statsByLeague[leagueId] = new Set();
                }
                // Add the stat type to the Set for this league ID
                statsByLeague[leagueId].add(statType);
            }
        });

        console.log('\nUnique Stat Types Found (Grouped by League ID):');

        // Get league IDs and sort them numerically for consistent output
        const sortedLeagueIds = Object.keys(statsByLeague).sort((a, b) => a - b);

        sortedLeagueIds.forEach(leagueId => {
            console.log(`\nLeague ID: ${leagueId}`);
            // Convert the Set to an Array and sort stat types alphabetically
            const sortedStatTypes = Array.from(statsByLeague[leagueId]).sort();
            sortedStatTypes.forEach(stat => console.log(`  - ${stat}`));
        });

    } catch (error) {
        console.error('Error fetching or processing data:', error);
    }
}

getUniqueStatTypesByLeague(); 