import * as fs from 'fs';
import * as path from 'path';

// Read proxies from the central proxies.txt file
const readProxiesFromFile = () => {
  try {
    const proxiesPath = path.resolve(__dirname, '../../providers/proxies/proxies.txt');
    const proxyContent = fs.readFileSync(proxiesPath, 'utf8');
    return proxyContent.split('\n').filter(line => line.trim() !== '');
  } catch (error) {
    console.error('Error reading proxies file:', error);
    return [];
  }
};

// Get the list of proxies
const commonProxies = readProxiesFromFile();

// Create a proxy object with the same list for all providers
const createProxyObject = () => {
  // Default export with common proxies for all providers
  const proxyObject: { [key: string]: string[] } = {
    'default': commonProxies
  };

  // Add the same proxy list for all known providers
  const providers = [
    'Caesars', 'Betr', 'Dabble', 'Underdog', 'PrizePicks', 'Fliff', 
    'BetMGM', 'BetSaracen', 'Bovada', 'DraftKings', 'FanDuel', 
    'VividPicks', 'Rebet', 'Circa', 'Thrillz', 'Chalkboard',
    'Sleeper', 'Monkey Knife Fight', 'Jock MKT', 'Betcha', 'ClutchBet', 'OwnersBox'
  ];

  // Assign the same proxy list to all providers
  providers.forEach(provider => {
    proxyObject[provider] = commonProxies;
  });

  return proxyObject;
};

export default createProxyObject();
