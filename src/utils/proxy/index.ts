const proxyManager = require('./ProxyManager');

// Initialize proxy manager on first import
let initPromise: Promise<void> | null = null;

async function ensureInitialized() {
    if (!initPromise) {
        initPromise = proxyManager.initialize();
    }
    await initPromise;
}

export async function getProxy(key?: string): Promise<string> {
    try {
        await ensureInitialized();
        const proxy = proxyManager.getProxy(key || "default");

        // Validate proxy URL format
        if (!proxy || typeof proxy !== 'string' || !proxy.startsWith('http')) {
            throw new Error(`Invalid proxy URL received: ${proxy}`);
        }

        return proxy;
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`[PROXY] Failed to get proxy for key '${key}':`, errorMessage);
        throw new Error(`No proxy available for key '${key}': ${errorMessage}`);
    }
}



export function reportProxySuccess(proxyUrl: string) {
    proxyManager.reportSuccess(proxyUrl);
}

export function reportProxyFailure(proxyUrl: string, error?: any) {
    proxyManager.reportFailure(proxyUrl, error);
}

export function getProxyStats() {
    return proxyManager.getStats();
}

export function logProxyStats() {
    proxyManager.logStats();
}

// For backward compatibility with synchronous calls
export function getProxySync(key?: string) {
    if (!proxyManager.initialized) {
        // Fallback to old behavior if not initialized
        console.warn('[PROXY] ProxyManager not initialized, using fallback');
        return getProxyFallback(key);
    }
    return proxyManager.getProxy(key || "default");
}

function getProxyFallback(key?: string) {
    // Simple fallback implementation
    const fs = require('fs');
    const path = require('path');

    try {
        const proxiesPath = path.resolve(__dirname, '../../providers/proxies/proxies.txt');
        const proxyContent = fs.readFileSync(proxiesPath, 'utf8');
        const lines = proxyContent.split('\n').filter(line => line.trim() !== '');

        if (lines.length === 0) {
            throw new Error('No proxies available');
        }

        const randomLine = lines[Math.floor(Math.random() * lines.length)];
        const [host, port, username, password] = randomLine.split(':');
        return `http://${username}:${password}@${host}:${port}`;
    } catch (error) {
        console.error('[PROXY] Fallback proxy loading failed:', error);
        throw error;
    }
}