const fs = require('fs');
const path = require('path');

class ProxyManager {
    constructor() {
        this.proxies = new Map(); // Map of proxy URL -> { url, failures, lastUsed, isHealthy, successCount }
        this.failureThreshold = 3; // Number of failures before marking proxy as unhealthy
        this.healthCheckInterval = 5 * 60 * 1000; // 5 minutes
        this.recoveryTime = 10 * 60 * 1000; // 10 minutes before retrying failed proxy
        this.roundRobinIndex = 0;
        this.initialized = false;
        
        // Provider-specific proxy pools
        this.providerPools = new Map();
    }

    async initialize() {
        if (this.initialized) return;
        
        try {
            await this.loadProxies();
            this.startHealthCheckTimer();
            this.initialized = true;
            console.log(`[PROXY MANAGER] Initialized with ${this.proxies.size} proxies`);
        } catch (error) {
            console.error('[PROXY MANAGER] Failed to initialize:', error);
            throw error;
        }
    }

    async loadProxies() {
        const proxiesPath = path.resolve(__dirname, '../../providers/proxies/proxies.txt');
        
        try {
            const proxyContent = fs.readFileSync(proxiesPath, 'utf8');
            const proxyLines = proxyContent.split('\n').filter(line => line.trim() !== '');
            
            for (const line of proxyLines) {
                const parts = line.trim().split(':');
                if (parts.length === 4) {
                    const [host, port, username, password] = parts;
                    const proxyUrl = `http://${username}:${password}@${host}:${port}`;
                    
                    this.proxies.set(proxyUrl, {
                        url: proxyUrl,
                        host,
                        port,
                        username,
                        password,
                        failures: 0,
                        successCount: 0,
                        lastUsed: 0,
                        isHealthy: true,
                        lastHealthCheck: 0
                    });
                }
            }
            
            console.log(`[PROXY MANAGER] Loaded ${this.proxies.size} proxies from ${proxiesPath}`);
        } catch (error) {
            console.error('[PROXY MANAGER] Error loading proxies:', error);
            throw error;
        }
    }

    getHealthyProxies(providerKey = 'default') {
        const now = Date.now();
        const healthyProxies = [];
        
        for (const [url, proxy] of this.proxies) {
            // Check if proxy should be considered for recovery
            if (!proxy.isHealthy && (now - proxy.lastUsed) > this.recoveryTime) {
                proxy.isHealthy = true;
                proxy.failures = 0;
                console.log(`[PROXY MANAGER] Recovering proxy: ${proxy.host}:${proxy.port}`);
            }
            
            if (proxy.isHealthy) {
                healthyProxies.push(proxy);
            }
        }
        
        return healthyProxies;
    }

    getProxy(providerKey = 'default') {
        if (!this.initialized) {
            throw new Error('ProxyManager not initialized. Call initialize() first.');
        }
        
        const healthyProxies = this.getHealthyProxies(providerKey);
        
        if (healthyProxies.length === 0) {
            console.warn('[PROXY MANAGER] No healthy proxies available, using least failed proxy');
            // Fallback: use the proxy with the least failures
            const allProxies = Array.from(this.proxies.values());
            allProxies.sort((a, b) => a.failures - b.failures);
            if (allProxies.length > 0) {
                const proxy = allProxies[0];
                proxy.lastUsed = Date.now();
                return proxy.url;
            }
            throw new Error('No proxies available');
        }
        
        // Round-robin selection among healthy proxies
        const proxy = healthyProxies[this.roundRobinIndex % healthyProxies.length];
        this.roundRobinIndex++;
        
        proxy.lastUsed = Date.now();
        return proxy.url;
    }

    reportSuccess(proxyUrl) {
        const proxy = this.proxies.get(proxyUrl);
        if (proxy) {
            proxy.successCount++;
            proxy.failures = Math.max(0, proxy.failures - 1); // Reduce failure count on success
            proxy.isHealthy = true;
        }
    }

    reportFailure(proxyUrl, error = null) {
        const proxy = this.proxies.get(proxyUrl);
        if (proxy) {
            proxy.failures++;
            proxy.lastUsed = Date.now();
            
            if (proxy.failures >= this.failureThreshold) {
                proxy.isHealthy = false;
                console.warn(`[PROXY MANAGER] Marking proxy as unhealthy: ${proxy.host}:${proxy.port} (${proxy.failures} failures)`);
            }
            
            if (error) {
                console.log(`[PROXY MANAGER] Proxy failure: ${proxy.host}:${proxy.port} - ${error.message || error}`);
            }
        }
    }

    async healthCheck() {
        const now = Date.now();
        const healthCheckPromises = [];
        
        for (const [url, proxy] of this.proxies) {
            // Only health check proxies that haven't been checked recently
            if ((now - proxy.lastHealthCheck) > this.healthCheckInterval) {
                healthCheckPromises.push(this.checkProxyHealth(proxy));
            }
        }
        
        if (healthCheckPromises.length > 0) {
            console.log(`[PROXY MANAGER] Running health check on ${healthCheckPromises.length} proxies`);
            await Promise.allSettled(healthCheckPromises);
        }
    }

    async checkProxyHealth(proxy) {
        try {
            const { fetch, ProxyAgent } = require('undici');
            const proxyAgent = new ProxyAgent(proxy.url);
            
            const response = await fetch('https://httpbin.org/ip', {
                dispatcher: proxyAgent,
                signal: AbortSignal.timeout(10000) // 10 second timeout
            });
            
            if (response.ok) {
                this.reportSuccess(proxy.url);
                proxy.lastHealthCheck = Date.now();
            } else {
                this.reportFailure(proxy.url, new Error(`HTTP ${response.status}`));
            }
        } catch (error) {
            this.reportFailure(proxy.url, error);
        }
    }

    startHealthCheckTimer() {
        // Run health check every 5 minutes
        setInterval(() => {
            this.healthCheck().catch(error => {
                console.error('[PROXY MANAGER] Health check error:', error);
            });
        }, this.healthCheckInterval);
        
        console.log('[PROXY MANAGER] Health check timer started');
    }

    getStats() {
        const stats = {
            total: this.proxies.size,
            healthy: 0,
            unhealthy: 0,
            totalSuccesses: 0,
            totalFailures: 0
        };
        
        for (const proxy of this.proxies.values()) {
            if (proxy.isHealthy) {
                stats.healthy++;
            } else {
                stats.unhealthy++;
            }
            stats.totalSuccesses += proxy.successCount;
            stats.totalFailures += proxy.failures;
        }
        
        return stats;
    }

    logStats() {
        const stats = this.getStats();
        console.log(`[PROXY MANAGER] Stats - Total: ${stats.total}, Healthy: ${stats.healthy}, Unhealthy: ${stats.unhealthy}, Success Rate: ${((stats.totalSuccesses / (stats.totalSuccesses + stats.totalFailures)) * 100).toFixed(1)}%`);
    }
}

// Create singleton instance
const proxyManager = new ProxyManager();

module.exports = proxyManager;
