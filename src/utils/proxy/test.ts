import { fetch, ProxyAgent } from 'undici';
import proxyList from './proxyList';

interface IpifyResponse {
    ip: string;
}

async function testProxy(proxyString: string): Promise<{ success: boolean; error?: string }> {
    const [ip, port, username, password] = proxyString.split(':');
    const proxyUrl = `http://${username}:${password}@${ip}:${port}`;
    
    try {
        const proxyAgent = new ProxyAgent(proxyUrl);
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 10000); // 10 second timeout

        const response = await fetch('https://api.ipify.org?format=json', {
            dispatcher: proxyAgent,
            signal: controller.signal
        });

        clearTimeout(timeout);

        if (!response.ok) {
            return { 
                success: false, 
                error: `HTTP ${response.status}: ${response.statusText}` 
            };
        }

        const data = await response.json() as IpifyResponse;
        return { 
            success: true,
            error: `Connected successfully. IP: ${data.ip}`
        };
    } catch (error: any) {
        return { 
            success: false, 
            error: error.message || 'Unknown error'
        };
    }
}

async function testAllProxies() {
    console.log('Starting proxy tests...\n');

    for (const [provider, proxies] of Object.entries(proxyList)) {
        console.log(`\nTesting ${provider} proxies:`);
        console.log('-'.repeat(50));

        const results = await Promise.all(
            proxies.map(async (proxy) => {
                const result = await testProxy(proxy);
                return {
                    proxy,
                    ...result
                };
            })
        );

        // Print results
        let working = 0;
        let failed = 0;

        results.forEach(({ proxy, success, error }) => {
            const [ip, port] = proxy.split(':');
            if (success) {
                console.log(`✅ ${ip}:${port} - ${error}`);
                working++;
            } else {
                console.log(`❌ ${ip}:${port} - ${error}`);
                failed++;
            }
        });

        console.log('-'.repeat(50));
        console.log(`Summary for ${provider}:`);
        console.log(`Working: ${working}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((working / proxies.length) * 100).toFixed(1)}%`);
    }
}

// Run the tests
testAllProxies().catch(console.error); 