const { fetch } = require('undici');

// Define the prefix mapping from UnderdogProvider.js
const prefixMapping = {
  '1Q': '1Q',
  '1H': '1H',
  '2H': '2H',
  // Add more prefixes here if needed based on observed stats
};

async function getUniqueDisplayStatsByLeague() {
  const endpoint = 'https://api.underdogfantasy.com/beta/v6/over_under_lines';
  // Use an object to store Sets of stats for each league
  const statsByLeague = {};

  try {
    console.log(`Fetching data from ${endpoint}...`);
    const response = await fetch(endpoint, {
      headers: {
        'accept': 'application/json',
        'accept-language': 'en-US,en;q=0.9',
        // Using a generic user-agent, might need adjustment if Underdog blocks it
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
    }

    // Get all necessary data parts
    const data = await response.json();
    const { over_under_lines, appearances, games, players, solo_games } = data;

    if (over_under_lines && appearances && games && players && solo_games) {
      console.log(`Processing ${over_under_lines.length} lines...`);

      over_under_lines.forEach(line => {
        // Ensure the basic structure for the stat exists
        if (!line?.over_under?.appearance_stat?.display_stat || !line?.over_under?.appearance_stat?.appearance_id) {
            // console.log("Skipping line due to missing stat data:", JSON.stringify(line));
            return; // Skip if essential stat info is missing
        }

        const stat_type_raw = line.over_under.appearance_stat.display_stat;
        let stat_type_processed = stat_type_raw; // Keep a mutable version

        // Find appearance
        const appearance = appearances.find(ap => ap.id === line.over_under.appearance_stat.appearance_id);
        if (!appearance) {
            // console.log(`Skipping line, appearance not found for ID: ${line.over_under.appearance_stat.appearance_id}`);
            return;
        }

        // Find game (handling SoloGame)
        let game;
        if (appearance.match_type === "SoloGame") {
            game = solo_games.find(g => g.id === appearance.match_id);
        } else {
            game = games.find(g => g.id === appearance.match_id);
        }
        if (!game) {
            // console.log(`Skipping line, game not found for ID: ${appearance.match_id}`);
            return;
        }

        // Find player
        const player = players.find(p => p.id === appearance.player_id);
         if (!player) {
            // console.log(`Skipping line, player not found for ID: ${appearance.player_id}`);
            return;
         }

        // Determine league based on UnderdogProvider logic
        let league = game.sport_id;

        if (game.sport_id === 'ESPORTS') {
            league = player.first_name.replace(/:$/, '');
            if (league === 'Dota') league = 'DOTA2';
            if (league === 'CS') league = 'CS2';
            // Add other ESPORTS mappings if needed
        } else {
             // Iterate over the mapping to apply prefixes to league and clean stat_type
            Object.keys(prefixMapping).forEach(prefix => {
                if (stat_type_processed.includes(prefix)) {
                    if (!league.endsWith(prefixMapping[prefix])) {
                         league += prefixMapping[prefix];
                    }
                    // Keep the prefix in the stat type for display purposes in this script
                    // stat_type_processed = stat_type_processed.replace(prefix, '').trim();
                }
            });

            // Apply league mappings
            switch (league) {
                case 'FIFA':
                case 'WSOCCER':
                case 'O_SOCCER':
                    league = 'SOCCER';
                    break;
                case 'BASKETBALL':
                    league = 'EURO';
                    break;
                case 'CBB':
                    league = 'NCAAB';
                    break;
                case 'PGA':
                case 'OLYMPIC_GOLF':
                    league = 'GOLF';
                    break;
                case 'HR':
                    league = 'HRDERBY';
                    break;
                case 'OLYMPIC_BASKETBALL':
                    league = 'OBBALL';
                    break;
                // Add WNBA mapping if needed, or other specific sport_ids
                // case 'WNBA':
                //      league = 'WNBA'; // Or keep as is if desired
                //      break;
            }
        }

        league = league.toUpperCase(); // Standardize league name

        // Initialize Set for the league if it doesn't exist
        if (!statsByLeague[league]) {
            statsByLeague[league] = new Set();
        }

        // Add the original, unprocessed stat type to the set for that league
        statsByLeague[league].add(stat_type_raw);

      });
    } else {
      console.log("Missing necessary data arrays (lines, appearances, games, players, solo_games) in the response.");
    }

    console.log("\nUnique Display Stats Found (Grouped by League):");

    // Get leagues, sort alphabetically
    const sortedLeagues = Object.keys(statsByLeague).sort();

    sortedLeagues.forEach(league => {
        console.log(`\n--- ${league} ---`);
        // Convert Set to Array, sort alphabetically, and print each stat
        Array.from(statsByLeague[league]).sort().forEach(stat => {
            console.log(`- ${stat}`);
        });
    });


  } catch (error) {
    console.error('Error fetching or processing data:', error);
  }
}

// Run the function
getUniqueDisplayStatsByLeague();
