// Define the mapping for each league
export const leagueStatTypeMapping = {
    'NBA': {
        'Points': {
            mapping: {
                'DraftKings': 'Points',
                'Caesars': 'Player Total Points',
                'BetOnline': 'Points',
                'PropBuilder': 'Points', // Added mapping for PropBuilder
                'Circa': 'POINTS SCORED',
                'BetSaracen': 'Points',
                'Fanduel': 'Points',
                'BetMGM': 'Points',
                'Betr': 'POINTS',
                'BetRivers': 'POINTS',
                'Boom': 'POINTS',
                'Bovada': 'Total Points',
                'Chalkboard': 'Points',
                'Dabble': 'points',
                'ESPNBet': 'Player Total Points',
                'Fliff': 'Player Points',
                'HardRock': 'Points',
                'Hotstreak': 'points',
                'OaklawnSports': 'Points scored by the player',
                'ParlayPlay': 'Points',
                'PrizePicks': 'Points',
                'Sleeper': 'points',
                'Underdog': 'Points',
                'VividPicks': 'Points',
                'Fanatics': 'Points',
                'Rebet': 'total points',
                'Novig': 'POINTS',
                'Pinnacle': 'Points',
                'Pick6': 'Points',
                'OwnersBox': 'Points',
                'ProphetX': 'Points',
                'Epick': 'points', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Fanduel'
        },
        'Double+Double': {
            mapping: {
                'Pinnacle': 'Double+Double',
                'ProphetX': 'Double Double',
                'Epick': 'double_double', // Verified Epick mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Fanduel'
        },
        'Triple+Double': {
            mapping: {
                'Pinnacle': 'Triple+Double',
                'ProphetX': 'Triple Double',
                'Epick': 'triple_double', // Verified Epick mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Fanduel'
        },
        'Rebounds': {
            mapping: {
                'DraftKings': 'Rebounds',
                'Circa': 'REBOUNDS',
                'Caesars': 'Player Total Rebounds',
                'BetOnline': 'Total Rebounds',
                'PropBuilder': 'Total Rebounds', // Added mapping for PropBuilder
                'BetSaracen': 'Rebounds',
                'Fanduel': 'Rebounds',
                'BetMGM': 'Rebounds',
                'Betr': 'REBOUNDS',
                'BetRivers': 'REBOUNDS',
                'Boom': 'REBOUNDS',
                'Bovada': 'Total Rebounds',
                'Chalkboard': 'Rebounds',
                'Dabble': 'rebounds',
                'ESPNBet': 'Player Total Rebounds',
                'Fliff': 'Player Rebounds',
                'HardRock': 'Rebounds',
                'Hotstreak': 'rebounds',
                'OaklawnSports': 'Rebounds by the player',
                'ParlayPlay': 'Rebounds',
                'PrizePicks': 'Rebounds',
                'Sleeper': 'rebounds',
                'SportsBattle': 'Rebounds',
                'Underdog': 'Rebounds',
                'VividPicks': 'Rebounds',
                'JockMKT': 'rebounds',
                'Pick6': 'Rebounds',
                'Fanatics': 'Rebounds',
                'Rebet': 'total rebounds',
                'Novig': 'REBOUNDS',
                'Pinnacle': 'Rebounds',
                'OwnersBox': 'Rebounds',
                'ProphetX': 'Rebounds',
                'Epick': 'rebounds', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.3 },
            preferredSharp: 'Fanduel'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists ',
                'Circa': 'ASSISTS',
                'Caesars': 'Player Total Assists',
                'BetOnline': 'Assists',
                'PropBuilder': 'Assists', // Added mapping for PropBuilder
                'BetSaracen': 'Assists',
                'Fanduel': 'Assists',
                'BetMGM': 'Assists',
                'Betr': 'ASSISTS',
                'BetRivers': 'ASSISTS',
                'Boom': 'ASSISTS',
                'Bovada': 'Total Assists',
                'Chalkboard': 'Assists',
                'Dabble': 'assists',
                'ESPNBet': 'Player Total Assists',
                'Fliff': 'Player Assists',
                'HardRock': 'Assists',
                'Hotstreak': 'assists',
                'OaklawnSports': 'Assists by the player',
                'ParlayPlay': 'Assists',
                'PrizePicks': 'Assists',
                'Sleeper': 'assists',
                'Underdog': 'Assists',
                'VividPicks': 'Assists',
                'Fanatics': 'Assists',
                'Rebet': 'total assists',
                'Novig': 'ASSISTS',
                'Pinnacle': 'Assists',
                'Pick6': 'Assists',
                'OwnersBox': 'Assists',
                'ProphetX': 'Assists',
                'Epick': 'assists', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.3},
            preferredSharp: 'Fanduel'
        },
        'Pts + Reb + Ast': {
            mapping: {
                'DraftKings': 'Points + Rebounds + Assists',
                'Caesars': 'Player Total Points + Assists + Rebounds',
                'BetOnline': 'Pts + Reb + Ast',
                'PropBuilder': 'Pts + Reb + Ast', // Added mapping for PropBuilder
                'BetSaracen': 'Points + Assists + Rebounds',
                'Fanduel': 'Pts + Reb + Ast',
                'BetMGM': 'Total points, rebounds and assists',
                'Betr': 'POINTS_REBOUNDS_ASSISTS',
                'BetRivers': 'PRA',
                'Boom': 'POINTS_REBOUNDS_ASSISTS',
                'Bovada': 'Total Points, Rebounds and Assists',
                'Chalkboard': 'P + R + A',
                'Dabble': 'assists_points_rebounds',
                'ESPNBet': 'Player Total Points, Rebounds And Assists',
                'Fliff': 'Player Points And Assists And Rebounds',
                'HardRock': 'Points + Assists + Rebounds',
                'Hotstreak': 'pra',
                'OaklawnSports': 'Points, rebounds & assists by the player',
                'ParlayPlay': 'Pts + Reb + Ast',
                'PrizePicks': 'Pts+Rebs+Asts',
                'Sleeper': 'pts_reb_ast',
                'SportsBattle': 'Pts + Rebs + Asts',
                'Underdog': 'Pts + Rebs + Asts',
                'VividPicks': 'Points + Rebounds + Assists',
                'JockMKT': 'points_plus_assists_plus_rebounds',
                'Pick6': 'Points + Rebounds + Assists',
                'Fanatics': 'Points + Rbd + Ast',
                'Novig': 'POINTS_REBOUNDS_ASSISTS',
                'Pinnacle': 'Pts+Rebs+Asts',
                'OwnersBox': 'Pts + Rebs + Asts',
                'ProphetX': 'Pts + Reb + Ast',
                'Epick': 'points+rebounds+assists', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.15 },
            preferredSharp: 'Fanduel'
        },
        'Pts + Reb': {
            mapping: {
                'DraftKings': 'Points + Rebounds',
                'Caesars': 'Player Total Points + Rebounds',
                'Fanduel': 'Pts + Reb',
                'BetMGM': 'Total points and rebounds',
                'Betr': 'POINTS_REBOUNDS',
                'Bovada': 'Total Points and Rebounds',
                'BetSaracen': 'Points + Rebounds',
                'Chalkboard': 'Pts + Reb',
                'Dabble': 'points_rebounds',
                'ESPNBet': 'Player Total Points And Rebounds',
                'Fliff': 'Player Points And Rebounds',
                'HardRock': 'Points + Rebounds',
                'Hotstreak': 'pr',
                'ParlayPlay': 'Pts + Reb',
                'PrizePicks': 'Pts+Rebs',
                'Sleeper': 'points_and_rebounds',
                'SportsBattle': 'Pts + Rebs',
                'Underdog': 'Points + Rebounds',
                'VividPicks': 'Pts + Reb',
                'JockMKT': 'points_plus_rebounds',
                'Fanatics': 'Points + Rebounds',
                'OaklawnSports': 'Points & Rebounds by the player',
                'Pick6': 'Points + Rebounds',
                'OwnersBox': 'Pts + Rebs',
                'ProphetX': 'Pts + Reb',
                'Epick': 'points+rebounds', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.15 },
            preferredSharp: 'Fanduel'
        },
        'Reb + Ast': {
            mapping: {
                'DraftKings': 'Rebounds + Assists',
                'Caesars': 'Player Total Rebounds + Assists',
                'Fanduel': 'Reb + Ast',
                'BetMGM': 'Total assists and rebounds',
                'BetSaracen': 'Assists + Rebounds',
                'Betr': 'ASSISTS_REBOUNDS',
                'Bovada': 'Total Rebounds and Assists',
                'Chalkboard': 'Ast + Reb',
                'Dabble': 'assists_rebounds',
                'ESPNBet': 'Player Total Assists And Rebounds',
                'Fliff': 'Player Assists And Rebounds',
                'HardRock': 'Assists + Rebounds',
                'Hotstreak': 'ra',
                'ParlayPlay': 'Reb + Ast',
                'PrizePicks': 'Rebs+Asts',
                'Sleeper': 'rebounds_and_assists',
                'Underdog': 'Rebounds + Assists',
                'VividPicks': 'Reb + Ast',
                'Fanatics': 'Rebounds + Assists',
                'OaklawnSports': 'Rebounds & Assists by the player',
                'Pick6': 'Assists + Rebounds',
                'OwnersBox': 'Rebs + Asts',
                'ProphetX': 'Reb + Ast',
                'Epick': 'rebounds+assists', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Fanduel'
        },
        'Pts + Ast': {
            mapping: {
                'DraftKings': 'Points + Assists',
                'Caesars': 'Player Total Points + Assists',
                'Fanduel': 'Pts + Ast',
                'BetMGM': 'Total points and assists',
                'Betr': 'POINTS_ASSISTS',
                'BetSaracen': 'Points + Assists',
                'Bovada': 'Total Points and Assists',
                'Chalkboard': 'Pts + Ast',
                'Dabble': 'assists_points',
                'ESPNBet': 'Player Total Points And Assists',
                'Fliff': 'Player Points And Assists',
                'HardRock': 'Points + Assists',
                'Hotstreak': 'pa',
                'ParlayPlay': 'Pts + Ast',
                'PrizePicks': 'Pts+Asts',
                'Sleeper': 'points_and_assists',
                'Underdog': 'Points + Assists',
                'VividPicks': 'Pts + Ast',
                'Fanatics': 'Points + Assists',
                'OaklawnSports': 'Points & Assists by the player',
                'Pick6': 'Points + Assists',
                'OwnersBox': 'Pts + Asts',
                'ProphetX': 'Pts + Ast',
                'Epick': 'points+assists', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.15 },
            preferredSharp: 'Fanduel'
        },
        'Steals': {
            mapping: {
                'DraftKings': 'Steals',
                'Caesars': 'Player Total Steals',
                'Fanduel': 'Steals',
                'BetMGM': 'Steals',
                'Boom': 'STEALS',
                'Betr': 'STEALS',
                'BetSaracen': 'Steals',
                'Bovada': 'Total Steals',
                'Chalkboard': 'Steals',
                'Dabble': 'steals',
                'ESPNBet': 'Player Total Steals',
                'Fliff': 'Player Steals',
                'HardRock': 'Steals',
                'Hotstreak': 'steals',
                'OaklawnSports': 'Steals by the player',
                'ParlayPlay': 'Steals',
                'PrizePicks': 'Steals',
                'Sleeper': 'steals',
                'Underdog': 'Steals',
                'VividPicks': 'Steals',
                'Fanatics': 'Steals',
                'Pick6': 'Steals',
                'OwnersBox': 'Steals',
                'ProphetX': 'Steals',
                'Epick': 'steals', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.3 },
            preferredSharp: 'Fanduel'
        },
        'Blocks': {
            mapping: {
                'DraftKings': 'Blocks',
                'Caesars': 'Player Total Blocks',
                'Fanduel': 'Blocks',
                'BetMGM': 'Blocks',
                'BetSaracen': 'Blocks',
                'Betr': 'BLOCKS',
                'Boom': 'BLOCKS',
                'Bovada': 'Total Blocks',
                'Chalkboard': 'Blocks',
                'Dabble': 'blocks',
                'ESPNBet': 'Player Total Blocks',
                'Fliff': 'Player Blocks',
                'HardRock': 'Blocks',
                'Hotstreak': 'blocks',
                'OaklawnSports': 'Blocks by the player',
                'ParlayPlay': 'Blocks',
                'PrizePicks': 'Blocked Shots',
                'Sleeper': 'blocks',
                'Underdog': 'Blocks',
                'VividPicks': 'Blocks',
                'Fanatics': 'Blocks',
                'Pick6': 'Blocks',
                'OwnersBox': 'Blocked Shots',
                'ProphetX': 'Blocks',
                'Epick': 'blocks', // Corrected Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "negbin", p: 0.3 },
            preferredSharp: 'Fanduel'
        },
        'Blocks + Steals': {
            mapping: {
                'DraftKings': 'Steals + Blocks',
                'Caesars': 'Player Total Blocks + Steals',
                'BetMGM': 'Total steals and blocks',
                'Boom': 'BLOCKS_AND_STEALS',
                'Betr': 'STEALS_BLOCKS',
                'BetSaracen': 'Steals + Blocks',
                'Chalkboard': 'Stl + Blk',
                'Dabble': 'blocks_steals',
                'ESPNBet': 'Player Total Steals And Blocks',
                'Fliff': 'Player Steals And Blocks',
                'HardRock': 'Steals + Blocks',
                'Hotstreak': 'stocks',
                'ParlayPlay': 'Stl + Blk',
                'PrizePicks': 'Blks+Stls',
                'Sleeper': 'blocks_and_steals',
                'Underdog': 'Blocks + Steals',
                'VividPicks': 'Blocks + Steals',
                'OaklawnSports': 'Steals & Blocks by the player',
                'Pick6': 'Steals + Blocks',
                'Fanatics': 'Steals',
                'OwnersBox': 'Steals + Blocks',
                'ProphetX': 'Stl + Blk',
                'Epick': 'steals+blocks', // Corrected Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "negbin", p: 0.3 },
            preferredSharp: 'DraftKings'
        },
        'Turnovers': {
            mapping: {
                'DraftKings': 'Turnovers',
                'Caesars': 'Player Total Turnovers',
                'BetMGM': 'Turnovers',
                'BetSaracen': 'Turnovers',
                'Betr': 'TURNOVERS',
                'Boom': 'TURNOVERS',
                'Bovada': 'Total Turnovers',
                'Chalkboard': 'Turnovers',
                'Dabble': 'turnovers',
                'ESPNBet': 'Player Total Turnovers',
                'Fliff': 'Player Turnovers',
                'Hotstreak': 'turnovers',
                'ParlayPlay': 'Turnovers',
                'PrizePicks': 'Turnovers',
                'Sleeper': 'turnovers',
                'Underdog': 'Turnovers',
                'VividPicks': 'Turnovers',
                'Fanatics': 'Turnovers',
                'Pick6': 'Turnovers',
                'OwnersBox': 'Turnovers',
                'ProphetX': 'Turnovers',
                'Epick': 'turnovers', // Verified Epick mapping
            },
            modelInfo: {model: "negbin", p: 0.5 },
            preferredSharp: 'DraftKings'
        },
        '3-PT Made': {
            mapping: {
                'DraftKings': 'Three Pointers Made',
                'Circa': '3PT FG MADE',
                'Caesars': 'Player Total 3pt Field Goals',
                'BetOnline': 'Three Point Field Goals Made',
                'PropBuilder': 'Three Point Field Goals Made', // Added mapping for PropBuilder
                'Fanduel': ['Made Threes', 'Threes'],
                'BetMGM': 'Three-pointers made',
                'Betr': 'THREE_POINTERS_MADE',
                'BetRivers': 'THREE_PT',
                'BetSaracen': 'Threes Made',
                'Boom': 'MADE_THREE_POINTERS',
                'Bovada': 'Total Made 3 Points Shots',
                'Chalkboard': '3pt Made',
                'Dabble': 'three-made',
                'ESPNBet': 'Player Total Threes Made',
                'Fanatics': '3 Pointers Made',
                'Fliff': 'Player Three Points Made',
                'HardRock': 'Threes Made',
                'Hotstreak': 'three_points_made',
                'OaklawnSports': "3-point field goals made by the player",
                'ParlayPlay': '3PT Made',
                'PrizePicks': '3-PT Made',
                'Sleeper': 'threes_made',
                'Underdog': '3-Pointers Made',
                'VividPicks': '3PT Made',
                'Rebet': 'total 3-point field goals',
                'Novig': 'THREE_POINTERS_MADE',
                'Pinnacle': '3 Point FG',
                'Pick6': '3-Pointers Made',
                'OwnersBox': '3-Pointers Made',
                'ProphetX': 'Threes',
                'Epick': '3pt_made', // Corrected Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.5 },
            preferredSharp: 'Fanduel'
        },
    },
    'NBA1Q': {
        'Points': {
            mapping: {
                'DraftKings': 'Points - 1st Quarter',
                'Caesars': '1st Quarter Total Points',
                'Fanduel': '1st Qtr Points',
                'Boom': 'POINTS',
                'Chalkboard': '1Q Pts',
                'Dabble': 'first-quarter-points',
                'HardRock': '1st Quarter Points',
                'Hotstreak': 'points',
                'PrizePicks': 'Points',
                'Sleeper': 'first_qtr_points',
                'Underdog': 'Points',
                'VividPicks': 'Points',
                'ESPNBet': 'Player First Quarter Points',
                'Fliff': 'Player First Quarter Points',
                // 'Epick': '?', // Need Epick specific 1Q stat name if available
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Fanduel'
        },
        'Rebounds': {
            mapping: {
                'DraftKings': 'Rebounds - 1st Quarter',
                'Caesars': 'Player 1st Quarter Total Rebounds',
                'Fanduel': '1st Qtr Rebounds',
                'Boom': 'REBOUNDS',
                'Chalkboard': '1Q Reb',
                'Dabble': 'first-quarter-rebounds',
                'HardRock': '1st Quarter Rebounds',
                'Hotstreak': 'rebounds',
                'PrizePicks': 'Rebounds',
                'Sleeper': 'first_qtr_rebounds',
                'Underdog': 'Rebounds',
                'VividPicks': 'Rebounds',
                'ESPNBet': 'Player First Quarter Rebounds',
                'Fliff': 'Player First Quarter Rebounds',
                // 'Epick': '?', // Need Epick specific 1Q stat name if available
            },
            modelInfo: { model: "negbin", p: 0.5 },
            preferredSharp: 'DraftKings'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists - 1st Quarter',
                'Caesars': 'Player 1st Quarter Total Assists',
                'Fanduel': '1st Qtr Assists',
                'Boom': 'ASSISTS',
                'Chalkboard': '1Q Ast',
                'Dabble': 'first-quarter-assists',
                'HardRock': '1st Quarter Assists',
                'Hotstreak': 'assists',
                'PrizePicks': 'Assists',
                'Sleeper': 'first_qtr_assists',
                'Underdog': 'Assists',
                'VividPicks': 'Assists',
                'ESPNBet': 'Player First Quarter Assists',
                'Fliff': 'Player First Quarter Assists',
                // 'Epick': '?', // Need Epick specific 1Q stat name if available
            },
            modelInfo: { model: "negbin", p: 0.5 },
            preferredSharp: 'DraftKings'
        },
        // Add more NBA1Q mappings as needed
    },
    'OBBALL': { // Placeholder for potential future mapping
        'Points': {
            mapping: {
                'DraftKings': 'Points',
                'Caesars': 'Total Points',
                'BetOnline': 'Points',
                'PropBuilder': 'Points', // Added mapping for PropBuilder
                'BetSaracen': 'Points',
                'Fanduel': 'Points',
                'BetMGM': 'Points',
                'Betr': 'POINTS',
                'BetRivers': 'POINTS',
                'Boom': 'POINTS',
                'Bovada': 'Total Points',
                'Chalkboard': 'Points',
                'Dabble': 'points',
                'ESPNBet': 'Player Total Points',
                'Fliff': 'Player Points',
                'HardRock': 'Points',
                'Hotstreak': 'points',
                'OaklawnSports': 'Points scored by the player',
                'ParlayPlay': 'Points',
                'PrizePicks': 'Points',
                'Sleeper': 'points',
                'SportsBattle': 'Points',
                'Underdog': 'Points',
                'VividPicks': 'Points',
                'JockMKT': 'points',
                'Fanatics': 'Points',
                'Rebet': 'total points',
            },
            modelInfo: { model: "negbin", p: 0.25 },
            preferredSharp: 'Caesars'
        },
        'Rebounds': {
            mapping: {
                'DraftKings': 'Rebounds',
                'Caesars': 'Total Rebounds',
                'BetOnline': 'Total Rebounds',
                'PropBuilder': 'Total Rebounds', // Added mapping for PropBuilder
                'BetSaracen': 'Rebounds',
                'Fanduel': 'Rebounds',
                'BetMGM': 'Rebounds',
                'Betr': 'REBOUNDS',
                'BetRivers': 'REBOUNDS',
                'Boom': 'REBOUNDS',
                'Bovada': 'Total Rebounds',
                'Chalkboard': 'Rebounds',
                'Dabble': 'rebounds',
                'ESPNBet': 'Player Total Rebounds',
                'Fliff': 'Player Rebounds',
                'HardRock': 'Rebounds',
                'Hotstreak': 'rebounds',
                'OaklawnSports': 'Rebounds by the player',
                'ParlayPlay': 'Rebounds',
                'PrizePicks': 'Rebounds',
                'Sleeper': 'rebounds',
                'SportsBattle': 'Rebounds',
                'Underdog': 'Rebounds',
                'VividPicks': 'Rebounds',
                'JockMKT': 'rebounds',
                'Fanatics': 'Rebounds',
                'Rebet': 'total rebounds',
            },
            modelInfo: { model: "negbin", p: 0.7 },
            preferredSharp: 'Caesars'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists',
                'Caesars': 'Total Assists',
                'BetOnline': 'Assists',
                'PropBuilder': 'Assists', // Added mapping for PropBuilder
                'BetSaracen': 'Assists',
                'Fanduel': 'Assists',
                'BetMGM': 'Assists',
                'Betr': 'ASSISTS',
                'BetRivers': 'ASSISTS',
                'Boom': 'ASSISTS',
                'Bovada': 'Total Assists',
                'Chalkboard': 'Assists',
                'Dabble': 'assists',
                'ESPNBet': 'Player Total Assists',
                'Fliff': 'Player Assists',
                'HardRock': 'Assists',
                'Hotstreak': 'assists',
                'OaklawnSports': 'Assists by the player',
                'ParlayPlay': 'Assists',
                'PrizePicks': 'Assists',
                'Sleeper': 'assists',
                'SportsBattle': 'Assists',
                'Underdog': 'Assists',
                'VividPicks': 'Assists',
                'JockMKT': 'assists',
                'Fanatics': 'Assists',
                'Rebet': 'total assists',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Caesars'
        },
        'Pts + Rebs + Asts': {
            mapping: {
                'DraftKings': 'Pts + Reb + Ast',
                'Caesars': 'Total Points + Assists + Rebounds',
                'BetOnline': 'Pts + Reb + Ast',
                'PropBuilder': 'Pts + Reb + Ast', // Added mapping for PropBuilder
                'BetSaracen': 'Points + Assists + Rebounds',
                'Fanduel': 'Pts + Reb + Ast',
                'BetMGM': 'Total points, rebounds and assists',
                'Betr': 'POINTS_REBOUNDS_ASSISTS',
                'BetRivers': 'PRA',
                'Boom': 'POINTS_REBOUNDS_ASSISTS',
                'Bovada': 'Total Points, Rebounds and Assists',
                'Chalkboard': 'P + R + A',
                'Dabble': 'assists_points_rebounds',
                'ESPNBet': 'Player Total Points, Rebounds And Assists',
                'Fliff': 'Player Points And Assists And Rebounds',
                'HardRock': 'Points + Assists + Rebounds',
                'Hotstreak': 'pra',
                'OaklawnSports': 'Points, rebounds & assists by the player',
                'ParlayPlay': 'Pts + Reb + Ast',
                'PrizePicks': 'Pts+Rebs+Asts',
                'Sleeper': 'pts_reb_ast',
                'SportsBattle': 'Pts + Rebs + Asts',
                'Underdog': 'Pts + Rebs + Asts',
                'VividPicks': 'Points + Rebounds + Assists',
                'JockMKT': 'points_plus_assists_plus_rebounds',
                'Fanatics': 'Points + Rbd + Ast',
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Caesars'
        },
        'Pts + Rebs': {
            mapping: {
                'DraftKings': 'Pts + Reb',
                'Caesars': 'Total Points + Rebounds',
                'Fanduel': 'Pts + Reb',
                'BetMGM': 'Total points and rebounds',
                'Bovada': 'Total Points and Rebounds',
                'BetSaracen': 'Points + Rebounds',
                'Chalkboard': 'Pts + Reb',
                'Dabble': 'points_rebounds',
                'ESPNBet': 'Player Total Points And Rebounds',
                'Fliff': 'Player Points And Rebounds',
                'HardRock': 'Points + Rebounds',
                'Hotstreak': 'pr',
                'ParlayPlay': 'Pts + Reb',
                'PrizePicks': 'Pts+Rebs',
                'Sleeper': 'points_and_rebounds',
                'SportsBattle': 'Pts + Rebs',
                'Underdog': 'Points + Rebounds',
                'VividPicks': 'Pts + Reb',
                'JockMKT': 'points_plus_rebounds',
                'Fanatics': 'Points + Rebounds',
                'OaklawnSports': 'Points & Rebounds by the player',
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Caesars'
        },
        'Rebs + Asts': {
            mapping: {
                'DraftKings': 'Ast + Reb',
                'Caesars': 'Total Rebounds + Assists',
                'Fanduel': 'Reb + Ast',
                'BetMGM': 'Total assists and rebounds',
                'BetSaracen': 'Assists + Rebounds',
                'Betr': 'ASSISTS_REBOUNDS',
                'Bovada': 'Total Rebounds and Assists',
                'Chalkboard': 'Ast + Reb',
                'Dabble': 'assists_rebounds',
                'ESPNBet': 'Player Total Assists And Rebounds',
                'Fliff': 'Player Assists And Rebounds',
                'HardRock': 'Assists + Rebounds',
                'Hotstreak': 'ra',
                'ParlayPlay': 'Reb + Ast',
                'PrizePicks': 'Rebs+Asts',
                'Sleeper': 'rebounds_and_assists',
                'SportsBattle': 'Rebs + Asts',
                'Underdog': 'Rebounds + Assists',
                'VividPicks': 'Reb + Ast',
                'JockMKT': 'assists_plus_rebounds',
                'Fanatics': 'Rebounds + Assists',
                'OaklawnSports': 'Rebounds & Assists by the player',
            },
            modelInfo: { model: "negbin", p: 0.5 },
            preferredSharp: 'Caesars'
        },
        'Pts + Asts': {
            mapping: {
                'DraftKings': 'Pts + Ast',
                'Caesars': 'Total Points + Assists',
                'Fanduel': 'Pts + Ast',
                'BetMGM': 'Total points and assists',
                'Betr': 'POINTS_ASSISTS',
                'BetSaracen': 'Points + Assists',
                'Bovada': 'Total Points and Assists',
                'Chalkboard': 'Pts + Ast',
                'Dabble': 'assists_points',
                'ESPNBet': 'Player Total Points And Assists',
                'Fliff': 'Player Points And Assists',
                'HardRock': 'Points + Assists',
                'Hotstreak': 'pa',
                'ParlayPlay': 'Pts + Ast',
                'PrizePicks': 'Pts+Asts',
                'Sleeper': 'points_and_assists',
                'SportsBattle': 'Pts + Asts',
                'Underdog': 'Points + Assists',
                'VividPicks': 'Pts + Ast',
                'JockMKT': 'points_plus_assists',
                'Fanatics': 'Points + Assists',
                'OaklawnSports': 'Points & Assists by the player',
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Caesars'
        },
        'Steals': {
            mapping: {
                'DraftKings': 'Steals',
                'Caesars': 'Total Steals',
                'Fanduel': 'Steals',
                'BetMGM': 'Steals',
                'Boom': 'STEALS',
                'Betr': 'STEALS',
                'BetSaracen': 'Steals',
                'Bovada': 'Total Steals',
                'Chalkboard': 'Steals',
                'Dabble': 'steals',
                'ESPNBet': 'Player Total Steals',
                'Fliff': 'Player Steals',
                'HardRock': 'Steals',
                'Hotstreak': 'steals',
                'OaklawnSports': 'Steals by the player',
                'ParlayPlay': 'Steals',
                'PrizePicks': 'Steals',
                'Sleeper': 'steals',
                'SportsBattle': 'Steals',
                'Underdog': 'Steals',
                'VividPicks': 'Steals',
                'JockMKT': 'steals',
                'Fanatics': 'Steals',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Caesars'
        },
        'Blocks': {
            mapping: {
                'DraftKings': 'Blocks',
                'Caesars': 'Total Blocks',
                'Fanduel': 'Blocks',
                'BetMGM': 'Blocks',
                'BetSaracen': 'Blocks',
                'Betr': 'BLOCKS',
                'Boom': 'BLOCKS',
                'Bovada': 'Total Blocks',
                'Chalkboard': 'Blocks',
                'Dabble': 'blocks',
                'ESPNBet': 'Player Total Blocks',
                'Fliff': 'Player Blocks',
                'HardRock': 'Blocks',
                'Hotstreak': 'blocks',
                'OaklawnSports': 'Blocks by the player',
                'ParlayPlay': 'Blocks',
                'PrizePicks': 'Blocked Shots',
                'Sleeper': 'blocks',
                'SportsBattle': 'Blocks',
                'Underdog': 'Blocks',
                'VividPicks': 'Blocks',
                'Fanatics': 'Blocks',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Caesars'
        },
        'Blocks + Steals': {
            mapping: {
                'DraftKings': 'Steals + Blocks',
                'Caesars': 'Total Blocks + Steals',
                'BetMGM': 'Total steals and blocks',
                'Boom': 'BLOCKS_AND_STEALS',
                'Betr': 'STEALS_BLOCKS',
                'BetSaracen': 'Steals + Blocks',
                'Chalkboard': 'Stl + Blk',
                'Dabble': 'blocks_steals',
                'ESPNBet': 'Player Total Steals And Blocks',
                'Fliff': 'Player Steals And Blocks',
                'HardRock': 'Steals + Blocks',
                'Hotstreak': 'stocks',
                'ParlayPlay': 'Stl + Blk',
                'PrizePicks': 'Blks+Stls',
                'Sleeper': 'blocks_and_steals',
                'Underdog': 'Blocks + Steals',
                'VividPicks': 'Blocks + Steals',
                'OaklawnSports': 'Steals & Blocks by the player',
                'Pick6': 'Steals + Blocks',
                'Fanatics': 'Steals',
                'OwnersBox': 'Steals + Blocks',
                'ProphetX': 'Stl + Blk',
            },
            modelInfo: { model: "negbin", p: 0.3 },
            preferredSharp: 'DraftKings'
        },
        'Turnovers': {
            mapping: {
                'DraftKings': 'Turnovers',
                'Caesars': 'Total Turnovers',
                'BetMGM': 'Turnovers',
                'BetSaracen': 'Turnovers',
                'Betr': 'TURNOVERS',
                'Boom': 'TURNOVERS',
                'Bovada': 'Total Turnovers',
                'Chalkboard': 'Turnovers',
                'Dabble': 'turnovers',
                'ESPNBet': 'Player Total Turnovers',
                'Fliff': 'Player Turnovers',
                'Hotstreak': 'turnovers',
                'ParlayPlay': 'Turnovers',
                'PrizePicks': 'Turnovers',
                'Sleeper': 'turnovers',
                'Underdog': 'Turnovers',
                'VividPicks': 'Turnovers',
                'Fanatics': 'Turnovers',
                'Pick6': 'Turnovers',
                'OwnersBox': 'Turnovers',
                'ProphetX': 'Turnovers',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Caesars'
        },
        '3-PT Made': {
            mapping: {
                'DraftKings': 'Threes',
                'Caesars': 'Total 3pt Field Goals',
                'BetOnline': 'Three Point Field Goals Made',
                'Fanduel': 'Made Threes',
                'BetMGM': 'Three-pointers made',
                'Betr': 'THREE_POINTERS_MADE',
                'BetRivers': 'THREE_PT',
                'BetSaracen': 'Threes Made',
                'Boom': 'MADE_THREE_POINTERS',
                'Bovada': 'Total Made 3 Points Shots',
                'Chalkboard': '3pt Made',
                'Dabble': 'three-made',
                'ESPNBet': 'Player Total Threes Made',
                'Fliff': 'Player Three Points Made',
                'HardRock': 'Threes Made',
                'Hotstreak': 'three_points_made',
                'OaklawnSports': "3-point field goals made by the player",
                'ParlayPlay': '3PT Made',
                'PrizePicks': '3-PT Made',
                'Sleeper': 'threes_made',
                'SportsBattle': '3PM',
                'Underdog': '3-Pointers Made',
                'VividPicks': '3PT Made',
                'Fanatics': "3 Pointers Made",
                'Rebet': 'total 3-point field goals',
                'Novig': 'THREE_POINTERS_MADE',
                'Pinnacle': '3 Point FG',
                'Pick6': '3-Pointers Made',
                'OwnersBox': '3-Pointers Made',
                'ProphetX': 'Threes',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Fanduel'
        },
    },
    'WNBA': {
        'Points': {
            mapping: {
                'DraftKings': 'Points',
                'Caesars': 'Total Points',
                'BetOnline': 'Points',
                'BetSaracen': 'Points',
                'Fanduel': 'Points WNBA',
                'BetMGM': 'Points',
                'Betr': 'POINTS',
                'BetRivers': 'POINTS',
                'Boom': 'POINTS',
                'Bovada': 'Total Points',
                'Chalkboard': 'Points',
                'Dabble': 'oddsjam_points',
                'ESPNBet': 'Player Total Points',
                'Fliff': 'Player Points',
                'HardRock': 'Points',
                'Hotstreak': 'points',
                'OaklawnSports': 'Points scored by the player',
                'ParlayPlay': 'Points',
                'PrizePicks': 'Points',
                'Sleeper': 'points',
                'SportsBattle': 'Points',
                'Underdog': 'Points',
                'VividPicks': 'Points',
                'Fanatics': 'Points',
                'Rebet': 'total points',
                // 'Epick': 'points', // Add if Epick supports WNBA
            },
            modelInfo: { model: "negbin", p: 0.25 },
            preferredSharp: 'DraftKings'
        },
        'Rebounds': {
            mapping: {
                'DraftKings': 'Rebounds',
                'Caesars': 'Total Rebounds',
                'BetOnline': 'Total Rebounds',
                'BetSaracen': 'Rebounds',
                'Fanduel': 'Rebounds WNBA',
                'BetMGM': 'Rebounds',
                'Betr': 'REBOUNDS',
                'BetRivers': 'REBOUNDS',
                'Boom': 'REBOUNDS',
                'Bovada': 'Total Rebounds',
                'Chalkboard': 'Rebounds',
                'Dabble': 'oddsjam_rebounds',
                'ESPNBet': 'Player Total Rebounds',
                'Fliff': 'Player Rebounds',
                'HardRock': 'Rebounds',
                'Hotstreak': 'rebounds',
                'OaklawnSports': 'Rebounds by the player',
                'ParlayPlay': 'Rebounds',
                'PrizePicks': 'Rebounds',
                'Sleeper': 'rebounds',
                'SportsBattle': 'Rebounds',
                'Underdog': 'Rebounds',
                'VividPicks': 'Rebounds',
                'Fanatics': 'Rebounds',
                'Rebet': 'total rebounds',
                // 'Epick': 'rebounds', // Add if Epick supports WNBA
            },
            modelInfo: { model: "negbin", p: 0.7 },
            preferredSharp: 'DraftKings'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists',
                'Caesars': 'Total Assists',
                'BetOnline': 'Assists',
                'BetSaracen': 'Assists',
                'Fanduel': 'Assists WNBA',
                'BetMGM': 'Assists',
                'Betr': 'ASSISTS',
                'BetRivers': 'ASSISTS',
                'Boom': 'ASSISTS',
                'Bovada': 'Total Assists',
                'Chalkboard': 'Assists',
                'Dabble': 'oddsjam_assists',
                'ESPNBet': 'Player Total Assists',
                'Fliff': 'Player Assists',
                'HardRock': 'Assists',
                'Hotstreak': 'assists',
                'OaklawnSports': 'Assists by the player',
                'ParlayPlay': 'Assists',
                'PrizePicks': 'Assists',
                'Sleeper': 'assists',
                'SportsBattle': 'Assists',
                'Underdog': 'Assists',
                'VividPicks': 'Assists',
                'Fanatics': 'Assists',
                'Rebet': 'total assists',
                // 'Epick': 'assists', // Add if Epick supports WNBA
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Pts + Reb + Ast': {
            mapping: {
                'DraftKings': 'Pts + Reb + Ast',
                'Caesars': 'Total Points + Assists + Rebounds',
                'BetOnline': 'Pts + Reb + Ast',
                'BetSaracen': 'Points + Assists + Rebounds',
                'Fanduel': 'Pts + Reb + Ast WNBA',
                'BetMGM': 'Total points, rebounds and assists',
                'Betr': 'POINTS_REBOUNDS_ASSISTS',
                'BetRivers': 'PRA',
                'Boom': 'POINTS_REBOUNDS_ASSISTS',
                'Bovada': 'Total Points, Rebounds and Assists',
                'Chalkboard': 'P + R + A',
                'Dabble': 'oddsjam_points_rebounds_assists',
                'ESPNBet': 'Player Total Points, Rebounds And Assists',
                'Fliff': 'Player Points And Assists And Rebounds',
                'HardRock': 'Points + Assists + Rebounds',
                'Hotstreak': 'pra',
                'OaklawnSports': 'Points, rebounds & assists by the player',
                'ParlayPlay': 'Pts + Reb + Ast',
                'PrizePicks': 'Pts+Rebs+Asts',
                'Sleeper': 'pts_reb_ast',
                'SportsBattle': 'Pts + Rebs + Asts',
                'Underdog': 'Pts + Rebs + Asts',
                'VividPicks': 'Points + Rebounds + Assists',
                'Fanatics': 'Points + Rbd + Ast',
                // 'Epick': 'points+rebounds+assists', // Add if Epick supports WNBA
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        'Pts + Reb': {
            mapping: {
                'DraftKings': 'Pts + Reb',
                'Caesars': 'Total Points + Rebounds',
                'Fanduel': 'Pts + Reb WNBA',
                'BetMGM': 'Total points and rebounds',
                'Bovada': 'Total Points and Rebounds',
                'BetSaracen': 'Points + Rebounds',
                'Chalkboard': 'Pts + Reb',
                'Dabble': 'points_rebounds',
                'ESPNBet': 'Player Total Points And Rebounds',
                'Fliff': 'Player Points And Rebounds',
                'HardRock': 'Points + Rebounds',
                'Hotstreak': 'pr',
                'ParlayPlay': 'Pts + Reb',
                'PrizePicks': 'Pts+Rebs',
                'Sleeper': 'points_and_rebounds',
                'SportsBattle': 'Pts + Rebs',
                'Underdog': 'Points + Rebounds',
                'VividPicks': 'Pts + Reb',
                'Fanatics': 'Points + Rebounds',
                'OaklawnSports': 'Points & Rebounds by the player',
                'Boom': 'POINTS_AND_REBOUNDS',
                // 'Epick': 'points+rebounds', // Add if Epick supports WNBA
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Fanduel'
        },
        'Reb + Ast': {
            mapping: {
                'DraftKings': 'Ast + Reb',
                'Caesars': 'Total Rebounds + Assists',
                'Fanduel': 'Reb + Ast WNBA',
                'BetMGM': 'Total assists and rebounds',
                'BetSaracen': 'Assists + Rebounds',
                'Betr': 'ASSISTS_REBOUNDS',
                'Bovada': 'Total Rebounds and Assists',
                'Chalkboard': 'Ast + Reb',
                'Dabble': 'assists_rebounds',
                'ESPNBet': 'Player Total Assists And Rebounds',
                'Fliff': 'Player Assists And Rebounds',
                'HardRock': 'Assists + Rebounds',
                'Hotstreak': 'ra',
                'ParlayPlay': 'Reb + Ast',
                'PrizePicks': 'Rebs+Asts',
                'Sleeper': 'rebounds_and_assists',
                'SportsBattle': 'Rebs + Asts',
                'Underdog': 'Rebounds + Assists',
                'VividPicks': 'Reb + Ast',
                'JockMKT': 'assists_plus_rebounds',
                'Fanatics': 'Rebounds + Assists',
                'OaklawnSports': 'Rebounds & Assists by the player',
                'Boom': 'REBOUNDS_AND_ASSISTS',
                // 'Epick': 'rebounds+assists', // Add if Epick supports WNBA
            },
            modelInfo: { model: "negbin", p: 0.5 },
            preferredSharp: 'Fanduel'
        },
        'Pts + Ast': {
            mapping: {
                'DraftKings': 'Pts + Ast',
                'Caesars': 'Total Points + Assists',
                'Fanduel': 'Pts + Ast WNBA',
                'BetMGM': 'Total points and assists',
                'Betr': 'POINTS_ASSISTS',
                'BetSaracen': 'Points + Assists',
                'Bovada': 'Total Points and Assists',
                'Chalkboard': 'Pts + Ast',
                'Dabble': 'assists_points',
                'ESPNBet': 'Player Total Points And Assists',
                'Fliff': 'Player Points And Assists',
                'HardRock': 'Points + Assists',
                'Hotstreak': 'pa',
                'ParlayPlay': 'Pts + Ast',
                'PrizePicks': 'Pts+Asts',
                'Sleeper': 'points_and_assists',
                'SportsBattle': 'Pts + Asts',
                'Underdog': 'Points + Assists',
                'VividPicks': 'Pts + Ast',
                'JockMKT': 'points_plus_assists',
                'Fanatics': 'Points + Assists',
                'OaklawnSports': 'Points & Assists by the player',
                'Boom': 'POINTS_AND_ASSISTS',
                // 'Epick': 'points+assists', // Add if Epick supports WNBA
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Fanduel'
        },
        '3-PT Made': {
            mapping: {
                'DraftKings': 'Three Pointers Made',
                'Circa': '3PT FG MADE',
                'Caesars': 'Total 3pt Field Goals',
                'BetOnline': 'Three Point Field Goals Made',
                'PropBuilder': 'Three Point Field Goals Made', // Added mapping for PropBuilder
                'Fanduel': ['Made Threes', 'Threes'],
                'BetMGM': 'Three-pointers made',
                'Betr': 'THREE_POINTERS_MADE',
                'BetRivers': 'THREE_PT',
                'BetSaracen': 'Threes Made',
                'Boom': 'MADE_THREE_POINTERS',
                'Bovada': 'Total Made 3 Points Shots',
                'Chalkboard': '3pt Made',
                'Dabble': 'three-made',
                'ESPNBet': 'Player Total Threes Made',
                'Fanatics': '3 Pointers Made',
                'Fliff': 'Player Three Points Made',
                'HardRock': 'Threes Made',
                'Hotstreak': 'three_points_made',
                'OaklawnSports': "3-point field goals made by the player",
                'ParlayPlay': '3PT Made',
                'PrizePicks': '3-PT Made',
                'Sleeper': 'threes_made',
                'SportsBattle': '3PM',
                'Underdog': '3-Pointers Made',
                'VividPicks': '3PT Made',
                'Rebet': 'total 3-point field goals',
                'Novig': 'THREE_POINTERS_MADE',
                'Pinnacle': '3 Point FG',
                'Pick6': '3-Pointers Made',
                'OwnersBox': '3-Pointers Made',
                'ProphetX': 'Threes',
                // 'Epick': '3pt_made', // Add if Epick supports WNBA
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Fanduel'
        },
        'Double+Double': {
            mapping: {
                'Pinnacle': 'Double+Double',
                'ProphetX': 'Double Double',
                'Epick': 'double_double', // Verified Epick mapping for WNBA
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Fanduel'
        },
        'Triple+Double': {
            mapping: {
                'Pinnacle': 'Triple+Double',
                'ProphetX': 'Triple Double',
                'Epick': 'triple_double', // Verified Epick mapping for WNBA
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Fanduel'
        },
        'Steals': {
            mapping: {
                'DraftKings': 'Steals',
                'Caesars': 'Player Total Steals',
                'Fanduel': 'Steals',
                'BetMGM': 'Steals',
                'Boom': 'STEALS',
                'Betr': 'STEALS',
                'BetSaracen': 'Steals',
                'Bovada': 'Total Steals',
                'Chalkboard': 'Steals',
                'Dabble': 'steals',
                'ESPNBet': 'Player Total Steals',
                'Fliff': 'Player Steals',
                'HardRock': 'Steals',
                'Hotstreak': 'steals',
                'OaklawnSports': 'Steals by the player',
                'ParlayPlay': 'Steals',
                'PrizePicks': 'Steals',
                'Sleeper': 'steals',
                'Underdog': 'Steals',
                'VividPicks': 'Steals',
                'Fanatics': 'Steals',
                'Pick6': 'Steals',
                'OwnersBox': 'Steals',
                'ProphetX': 'Steals',
                'Epick': 'steals', // Verified Epick mapping for WNBA
            },
            modelInfo: { model: "negbin", p: 0.3 },
            preferredSharp: 'Fanduel'
        },
        'Blocks': {
            mapping: {
                'DraftKings': 'Blocks',
                'Caesars': 'Player Total Blocks',
                'Fanduel': 'Blocks',
                'BetMGM': 'Blocks',
                'BetSaracen': 'Blocks',
                'Betr': 'BLOCKS',
                'Boom': 'BLOCKS',
                'Bovada': 'Total Blocks',
                'Chalkboard': 'Blocks',
                'Dabble': 'blocks',
                'ESPNBet': 'Player Total Blocks',
                'Fliff': 'Player Blocks',
                'HardRock': 'Blocks',
                'Hotstreak': 'blocks',
                'OaklawnSports': 'Blocks by the player',
                'ParlayPlay': 'Blocks',
                'PrizePicks': 'Blocked Shots',
                'Sleeper': 'blocks',
                'Underdog': 'Blocks',
                'VividPicks': 'Blocks',
                'Fanatics': 'Blocks',
                'Pick6': 'Blocks',
                'OwnersBox': 'Blocked Shots',
                'ProphetX': 'Blocks',
                'Epick': 'blocks', // Epick mapping for WNBA, copied from NBA
            },
            modelInfo: { model: "negbin", p: 0.3 },
            preferredSharp: 'Fanduel'
        },
        'Blocks + Steals': {
            mapping: {
                'DraftKings': 'Steals + Blocks',
                'Caesars': 'Player Total Blocks + Steals',
                'BetMGM': 'Total steals and blocks',
                'Boom': 'BLOCKS_AND_STEALS',
                'Betr': 'STEALS_BLOCKS',
                'BetSaracen': 'Steals + Blocks',
                'Chalkboard': 'Stl + Blk',
                'Dabble': 'blocks_steals',
                'ESPNBet': 'Player Total Steals And Blocks',
                'Fliff': 'Player Steals And Blocks',
                'HardRock': 'Steals + Blocks',
                'Hotstreak': 'stocks',
                'ParlayPlay': 'Stl + Blk',
                'PrizePicks': 'Blks+Stls',
                'Sleeper': 'blocks_and_steals',
                'Underdog': 'Blocks + Steals',
                'VividPicks': 'Blocks + Steals',
                'OaklawnSports': 'Steals & Blocks by the player',
                'Pick6': 'Steals + Blocks',
                'Fanatics': 'Steals',
                'OwnersBox': 'Steals + Blocks',
                'ProphetX': 'Stl + Blk',
                'Epick': 'steals+blocks', // Epick mapping for WNBA, copied from NBA
            },
            modelInfo: { model: "negbin", p: 0.3 },
            preferredSharp: 'DraftKings'
        },
        'Turnovers': {
            mapping: {
                'DraftKings': 'Turnovers',
                'Caesars': 'Player Total Turnovers',
                'BetMGM': 'Turnovers',
                'BetSaracen': 'Turnovers',
                'Betr': 'TURNOVERS',
                'Boom': 'TURNOVERS',
                'Bovada': 'Total Turnovers',
                'Chalkboard': 'Turnovers',
                'Dabble': 'turnovers',
                'ESPNBet': 'Player Total Turnovers',
                'Fliff': 'Player Turnovers',
                'Hotstreak': 'turnovers',
                'ParlayPlay': 'Turnovers',
                'PrizePicks': 'Turnovers',
                'Sleeper': 'turnovers',
                'Underdog': 'Turnovers',
                'VividPicks': 'Turnovers',
                'Fanatics': 'Turnovers',
                'Pick6': 'Turnovers',
                'OwnersBox': 'Turnovers',
                'ProphetX': 'Turnovers',
                'Epick': 'turnovers', // Verified Epick mapping for WNBA
            },
            modelInfo: {model: "negbin", p: 0.5 },
            preferredSharp: 'DraftKings'
        }
    },
    'NCAAB': {
        'Points': {
            mapping: {
                'DraftKings': 'Points',
                'BetOnline': 'Points',
                'PropBuilder': 'Points', // Added mapping for PropBuilder
                'Fanduel': ['PLAYER_TOTAL_POINTS', 'Total Points'],
                'BetRivers': 'POINTS',
                'BetSaracen': 'Points',
                'OaklawnSports': 'Points scored',
                'Boom': 'POINTS',
                'Dabble': 'points',
                'ESPNBet': 'Player Total Points',
                'ParlayPlay': 'Points',
                'PrizePicks': 'Points',
                'Sleeper': 'points',
                'SportsBattle': 'Points',
                'Underdog': 'Points',
                'VividPicks': 'Points',
                'Betr': 'POINTS', // Add Betr mapping here for NCAAB Points
                'Fliff': 'Player Points', // Adding Fliff mapping
                'Pick6': 'Points',
                // 'Epick': 'points', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "negbin", p: 0.25 },
            preferredSharp: 'DraftKings'
        },
        'Rebounds': {
            mapping: {
                'DraftKings': 'Rebounds',
                'BetOnline': 'Total Rebounds',
                'PropBuilder': 'Total Rebounds', // Added mapping for PropBuilder
                'Fanduel': ['PLAYER_TOTAL_REBOUNDS', 'Total Rebounds'],
                'BetRivers': 'REBOUNDS',
                'BetSaracen': 'Rebounds',
                'OaklawnSports': 'Rebounds',
                'Boom': 'REBOUNDS',
                'Dabble': 'rebounds',
                'ESPNBet': 'Player Total Rebounds',
                'ParlayPlay': 'Rebounds',
                'PrizePicks': 'Rebounds',
                'Sleeper': 'rebounds',
                'SportsBattle': 'Rebounds',
                'Underdog': 'Rebounds',
                'VividPicks': 'Rebounds',
                'Betr': 'REBOUNDS', // Add Betr mapping here for NCAAB Rebounds
                'Fliff': 'Player Rebounds', // Adding Fliff mapping
                'Pick6': 'Rebounds',
                // 'Epick': 'rebounds', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists',
                'BetOnline': 'Assists',
                'PropBuilder': 'Assists', // Added mapping for PropBuilder
                'Fanduel': ['PLAYER_TOTAL_ASSISTS', 'Total Assists'],
                'BetRivers': 'ASSISTS',
                'BetSaracen': 'Assists',
                'OaklawnSports': 'Assists',
                'Boom': 'ASSISTS',
                'Dabble': 'assists',
                'ESPNBet': 'Player Total Assists',
                'ParlayPlay': 'Assists',
                'PrizePicks': 'Assists',
                'Sleeper': 'assists',
                'SportsBattle': 'Assists',
                'Underdog': 'Assists',
                'VividPicks': 'Assists',
                'Betr': 'ASSISTS', // Add Betr mapping here for NCAAB Assists
                'Fliff': 'Player Assists', // Adding Fliff mapping
                'Pick6': 'Assists',
                // 'Epick': 'assists', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Pts + Rebs + Asts': {
            mapping: {
                'DraftKings': 'Pts + Reb + Ast',
                'BetSaracen': 'Points + Assists + Rebounds',
                'PropBuilder': 'Points + Assists + Rebounds', // Added mapping for PropBuilder
                'BetOnline': 'Points + Assists + Rebounds', // Added mapping for BetOnline
                'OaklawnSports': 'Points, rebounds & assists',
                'Boom': 'POINTS_REBOUNDS_ASSISTS',
                'Fanduel': ['PLAYER_POINTS_REBOUNDS_ASSISTS', 'Total Pts + Reb + Ast', 'Total Points + Rebounds + Assists'],
                'Dabble': 'assists_points_rebounds',
                'ESPNBet': 'Player Total Points + Rebounds + Assists',
                'ParlayPlay': 'Pts + Reb + Ast',
                'PrizePicks': 'Pts+Rebs+Asts',
                'Sleeper': 'pts_reb_ast',
                'SportsBattle': 'Pts + Rebs + Asts',
                'Underdog': 'Pts + Rebs + Asts',
                'VividPicks': 'Points + Rebounds + Assists',
                'Betr': 'POINTS_REBOUNDS_ASSISTS', // Add Betr mapping here for NCAAB PRA
                'Fliff': 'Player Points And Assists And Rebounds', // Adding Fliff mapping
                'Pick6': 'Points + Rebounds + Assists',
                // 'Epick': 'points+rebounds+assists', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        '3-PT Made': {
            mapping: {
                'DraftKings': 'Threes',
                'BetOnline': 'Three Point Field Goals Made',
                'PropBuilder': 'Three Point Field Goals Made', // Added mapping for PropBuilder
                'Fanduel': ['PLAYER_MADE_THREES', 'Total Threes'],
                'BetMGM': 'Three-pointers made',
                'Betr': 'THREE_POINTERS_MADE',
                'BetRivers': 'THREE_PT',
                'BetSaracen': 'Threes Made',
                'Boom': 'MADE_THREE_POINTERS',
                'Bovada': 'Total Made 3 Points Shots',
                'Chalkboard': '3pt Made',
                'Dabble': 'three-made',
                'ESPNBet': 'Player Total Threes Made',
                'Fanatics': '3 Pointers Made',
                'Fliff': 'Player Three Points Made',
                'HardRock': 'Threes Made',
                'Hotstreak': 'three_points_made',
                'OaklawnSports': "3-point field goals made by the player",
                'ParlayPlay': '3PT Made',
                'PrizePicks': '3-PT Made',
                'Sleeper': 'threes_made',
                'SportsBattle': '3PM',
                'Underdog': '3-Pointers Made',
                'VividPicks': '3PT Made',
                'Pick6': '3-Pointers Made',
                // 'Epick': '3pt_made', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Pts + Rebs': {
            mapping: {
                'DraftKings': 'Pts + Reb',
                'Caesars': 'Total Points + Rebounds',
                'Fanduel': ['PLAYER_POINTS_REBOUNDS', 'Total Pts + Reb', 'Total Points + Rebounds'],
                'BetMGM': 'Total points and rebounds',
                'Bovada': 'Total Points and Rebounds',
                'BetSaracen': 'Points + Rebounds',
                'Chalkboard': 'Pts + Reb',
                'Dabble': 'points_rebounds',
                'ESPNBet': 'Player Total Points And Rebounds',
                'Fliff': 'Player Points And Rebounds', // Adding Fliff mapping
                'HardRock': 'Points + Rebounds',
                'Hotstreak': 'pr',
                'ParlayPlay': 'Pts + Reb',
                'PrizePicks': 'Pts+Rebs',
                'Sleeper': 'points_and_rebounds',
                'SportsBattle': 'Pts + Rebs',
                'Underdog': 'Points + Rebounds',
                'VividPicks': 'Pts + Reb',
                'JockMKT': 'points_plus_rebounds',
                'Betr': 'POINTS_REBOUNDS', // Add Betr mapping here for NCAAB PR
                'Pick6': 'Points + Rebounds',
                 // 'Epick': 'points+rebounds', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        'Rebs + Asts': {
            mapping: {
                'DraftKings': 'Ast + Reb',
                'Caesars': 'Total Rebounds + Assists',
                'BetMGM': 'Total assists and rebounds',
                'BetSaracen': 'Assists + Rebounds',
                'Bovada': 'Total Rebounds and Assists',
                'Chalkboard': 'Ast + Reb',
                'Dabble': 'assists_rebounds',
                'ESPNBet': 'Player Total Assists And Rebounds',
                'Fanduel': ['PLAYER_ASSISTS_REBOUNDS', 'Total Ast + Reb', 'Total Assists + Rebounds'],
                'Fliff': 'Player Assists And Rebounds', // Adding Fliff mapping
                'HardRock': 'Assists + Rebounds',
                'Hotstreak': 'ra',
                'ParlayPlay': 'Reb + Ast',
                'PrizePicks': 'Rebs+Asts',
                'Sleeper': 'rebounds_and_assists',
                'SportsBattle': 'Rebs + Asts',
                'Underdog': 'Rebounds + Assists',
                'VividPicks': 'Reb + Ast',
                'JockMKT': 'assists_plus_rebounds',
                'Betr': 'ASSISTS_REBOUNDS', // Adds Betr mapping here for NCAAB RA
                'Pick6': 'Assists + Rebounds',
                // 'Epick': 'rebounds+assists', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "negbin", p: 0.5 },
            preferredSharp: 'DraftKings'
        },
        'Pts + Asts': {
            mapping: {
                'DraftKings': 'Pts + Ast',
                'Caesars': 'Total Points + Assists',
                'Fanduel': ['PLAYER_POINTS_ASSISTS', 'Total Pts + Ast', 'Total Points + Assists'],
                'BetMGM': 'Total points and assists',
                'Betr': 'POINTS_ASSISTS',
                'BetSaracen': 'Points + Assists',
                'Bovada': 'Total Points and Assists',
                'Chalkboard': 'Pts + Ast',
                'Dabble': 'assists_points',
                'ESPNBet': 'Player Total Points And Assists',
                'Fliff': 'Player Points And Assists', // Adding Fliff mapping
                'HardRock': 'Points + Assists',
                'Hotstreak': 'pa',
                'ParlayPlay': 'Pts + Ast',
                'PrizePicks': 'Pts+Asts',
                'Sleeper': 'points_and_assists',
                'SportsBattle': 'Pts + Asts',
                'Underdog': 'Points + Assists',
                'VividPicks': 'Pts + Ast',
                'Pick6': 'Points + Assists',
                // 'Epick': 'points+assists', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        'Steals': {
            mapping: {
                'DraftKings': 'Steals',
                'Caesars': 'Total Steals',
                'Fanduel': ['PLAYER_STEALS', 'Total Steals'],
                'BetMGM': 'Steals',
                'Betr': 'STEALS',
                'BetSaracen': 'Steals',
                'Bovada': 'Total Steals',
                'Chalkboard': 'Steals',
                'Dabble': 'steals',
                'ESPNBet': 'Player Total Steals',
                'Fliff': 'Player Steals', // Adding Fliff mapping
                'HardRock': 'Steals',
                'Hotstreak': 'steals',
                'OaklawnSports': 'Steals',
                'ParlayPlay': 'Steals',
                'PrizePicks': 'Steals',
                'Sleeper': 'steals',
                'SportsBattle': 'Steals',
                'Underdog': 'Steals',
                'VividPicks': 'Steals',
                'JockMKT': 'steals',
                'Pick6': 'Steals',
                // 'Epick': 'steals', // Add if Epick supports NCAAB
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        // Add more NBA mappings as needed
    },
    'EURO': {
        'Points': {
            mapping: {
                'DraftKings': 'Points',
                'PrizePicks': 'Points',
                'Underdog': 'Points',
                'SportsBattle': 'Points',
                'BetOnline': 'Points',
                 // 'Epick': 'points', // Add if Epick supports EURO
            },
            modelInfo: { model: "negbin", p: 0.25 },
            preferredSharp: 'DraftKings'
        },
        'Rebounds': {
            mapping: {
                'DraftKings': 'Rebounds',
                'PrizePicks': 'Rebounds',
                'Underdog': 'Rebounds',
                'SportsBattle': 'Rebounds',
                'BetOnline': 'Total Rebounds',
                // 'Epick': 'rebounds', // Add if Epick supports EURO
            },
            modelInfo: { model: "negbin", p: 0.7 },
            preferredSharp: 'DraftKings'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists',
                'PrizePicks': 'Assists',
                'Underdog': 'Assists',
                'SportsBattle': 'Assists',
                'BetOnline': 'Assists',
                // 'Epick': 'assists', // Add if Epick supports EURO
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        }
    },
    'NFL': { // Placeholder for potential future mapping
        'Pass TDs': {
            mapping: {
                'DraftKings': 'Passing Touchdowns',
                'Circa': 'TD PASSES',
                'Caesars': 'Total Passing Touchdowns',
                'BetOnline': 'Passing TDs',
                'PropBuilder': 'Passing TDs', // Added mapping for PropBuilder
                'BetMGM': 'Passing touchdowns',
                'Fanduel': 'Passing TDs',
                'Fanatics': 'Passing Touchdowns',
                'Betr': 'PASSING_TOUCHDOWNS',
                'Boom': 'PASSING_TOUCHDOWNS',
                'Bovada': 'Total Passing Touchdowns',
                'Chalkboard': 'Pass TD',
                'Dabble': 'passing-touchdowns',
                'ESPNBet': 'Player Total Passing Touchdowns',
                'Fliff': 'Passing Touchdowns',
                'HardRock': 'Passing TDs',
                'Hotstreak': 'passing_touchdowns',
                'ParlayPlay': 'Passing TDs',
                'PrizePicks': 'Pass TDs',
                'Sleeper': 'passing_touchdowns',
                'SportsBattle': 'Pass TD',
                'Underdog': 'Passing TDs',
                'VividPicks': 'PassingTouchdowns',
                'JockMKT': 'passing_touchdowns',
                'Rebet': 'total passing touchdowns',
                'Pinnacle': 'TD Passes'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Passing Yards': {
            mapping: {
                'DraftKings': 'Passing Yards',
                'Caesars': 'Total Passing Yards',
                'BetOnline': 'Passing Yards',
                'PropBuilder': 'Passing Yards', // Added mapping for PropBuilder
                'BetMGM': 'Passing yards',
                'Fanduel': 'Passing Yds',
                'Betr': 'PASSING_YARDS',
                'Boom': 'PASSING_YARDS',
                'Bovada': 'Total Passing Yards',
                'Chalkboard': 'Pass Yards',
                'Dabble': 'passing-yards',
                'ESPNBet': 'Player Total Passing Yards',
                'Fliff': 'Passing Yards',
                'HardRock': 'Passing Yards',
                'Hotstreak': 'passing_yards',
                'ParlayPlay': 'Passing Yards',
                'PrizePicks': 'Pass Yards',
                'Sleeper': 'passing_yards',
                'SportsBattle': 'Pass Yards',
                'Underdog': 'Passing Yards',
                'VividPicks': 'PassingYards',
                'JockMKT': 'passing_yards',
                'Fanatics': 'Passing Yards',
                'Rebet': 'total passing yards',
                'Novig': 'PASSING_YARDS',
                'Pinnacle': 'Passing Yards',
                'Pick6': 'Passing Yards',
            },
            modelInfo: { model: "negbin", p: 0.005 },
            preferredSharp: 'DraftKings'
        },
        '1Q Passing Yards': {
            mapping: {
                'Fanduel': '1st Qtr Passing Yds',
                'PrizePicks': '1Q Pass Yards',
                'Underdog': '1Q Passing Yards',
            },
            modelInfo: { model: "negbin", p: 0.005 },
            preferredSharp: 'Fanduel'
        },
        'Passing Attempts': {
            mapping: {
                'DraftKings': 'Passing Attempts',
                'Caesars': 'Total Passing Attempts',
                'BetOnline': 'Pass Attempts',
                'PropBuilder': 'Passing Attempts', // Added mapping for PropBuilder
                'BetMGM': 'Passing attempts',
                'Fanduel': 'Pass Attempts',
                'Betr': 'PASSING_ATTEMPTS',
                'Boom': 'PASSING_ATTEMPTS',
                'Bovada': 'Total Passing Attempts',
                'Chalkboard': 'Pass Att',
                'Dabble': 'passing-attempts',
                'ESPNBet': 'Player Total Passing Attempts',
                'Fliff': 'Passing Attempts',
                'HardRock': 'Pass Attempts',
                'Hotstreak': 'passing_attempts',
                'ParlayPlay': 'Pass Attempts',
                'PrizePicks': 'Pass Attempts',
                'SportsBattle': 'Pass Attempts',
                'Underdog': 'Passing Attempts',
                'VividPicks': 'Pass Attempts',
                'JockMKT': 'passing_attempts',
                'Pinnacle': 'Pass Attempts',
                'Pick6': 'Passing Attempts',
            },
            modelInfo: { model: "negbin", p: 0.05 },
            preferredSharp: 'DraftKings'
        },
        'Passing Completions': {
            mapping: {
                'DraftKings': 'Passing Completions',
                'Caesars': 'Total Passing Completions',
                'BetOnline': 'Pass Completions',
                'PropBuilder': 'Passing Completions', // Added mapping for PropBuilder
                'Fanduel': 'Pass Completions',
                'BetMGM': 'Pass completions',
                'Betr': 'PASSING_COMPLETIONS',
                'Boom': 'PASSING_COMPLETIONS',
                'Bovada': 'Total Completions',
                'Chalkboard': 'Pass Comp',
                'Dabble': 'passing-completions',
                'ESPNBet': 'Player Total Passing Completions',
                'Fliff': 'Passing Completions',
                'HardRock': 'Pass Completions',
                'Hotstreak': 'pass_completions',
                'ParlayPlay': 'Pass Completions',
                'PrizePicks': 'Pass Completions',
                'Sleeper': 'pass_completions',
                'SportsBattle': 'Pass Comp',
                'Underdog': 'Completions',
                'VividPicks': 'Completions',
                'JockMKT': 'completions',
                'Rebet': 'total pass completions',
                'Novig': 'PASSING_COMPLETIONS',
                'Pinnacle': 'Completions'
            },
            modelInfo: { model: "negbin", p: 0.05 },
            preferredSharp: 'DraftKings'
        },
        'Interceptions Thrown': {
            mapping: {
                'DraftKings': 'Interceptions Thrown',
                'Caesars': 'Total Interceptions',
                'BetOnline': 'Pass Interceptions',
                'PropBuilder': 'Pass Interceptions', // Added mapping for PropBuilder
                'BetMGM': 'Interceptions thrown',
                'Betr': 'PASSING_INTERCEPTIONS',
                'Boom': 'INTERCEPTIONS_THROWN',
                'Bovada': 'Total Interceptions Thrown',
                'Chalkboard': 'Pass INT',
                'Dabble': 'interceptions',
                'ESPNBet': 'Player Total Interceptions Thrown',
                'Fliff': 'Passing Interceptions',
                'HardRock': 'Interceptions',
                'Hotstreak': 'passing_interceptions',
                'ParlayPlay': 'Interception',
                'PrizePicks': 'INT',
                'Sleeper': 'interceptions',
                'SportsBattle': 'Interception',
                'Underdog': 'Interceptions',
                'VividPicks': 'Interceptions Thrown',
                'JockMKT': 'interceptions',
                'Pinnacle': 'Interceptions',
                'Pick6': 'Interceptions Thrown'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Pass + Rush Yards': {
            mapping: {
                'DraftKings': 'Passing + Rushing Yards',
                //'Caesars': '',
                'Fanduel': 'Passing + Rushing Yds',
                'BetMGM': 'Total passing and rushing yards',
                'Betr': 'PASSING_RUSHING_YARDS',
                'Fliff': 'Passing + Rushing Yards',
                'Chalkboard': 'Pass + Rush Yds',
                'Dabble': 'passing-yards_rushing-yards',
                'ESPNBet': 'Player Total Passing + Rushing Yards',
                'Hotstreak': 'passing_plus_rushing_yards',
                'PrizePicks': 'Pass+Rush Yds',
                'Sleeper': 'passing_and_rushing_yards',
                'SportsBattle': 'Pass + Rush Yards',
                'Underdog': 'Pass + Rush Yards',
                'VividPicks': 'Pass + Rush Yards',
                'JockMKT': 'passing_plus_rushing_yards',
            },
            modelInfo: { model: "negbin", p: 0.005 },
            preferredSharp: 'DraftKings'
        },
        'Rushing Yards': {
            mapping: {
                'DraftKings': 'Rushing Yards',
                'Circa': 'RUSHING YARDS',
                'Caesars': 'Total Rushing Yards',
                'BetOnline': 'Rushing Yards',
                'PropBuilder': 'Rushing Yards', // Added mapping for PropBuilder
                'BetMGM': 'Rushing yards',
                'Fanduel': 'Rushing Yds',
                'Betr': 'RUSHING_YARDS',
                'Boom': 'RUSHING_YARDS',
                'Bovada': 'Total Rushing Yards',
                'Chalkboard': 'Rush Yards',
                'Dabble': 'rushing-yards',
                'ESPNBet': 'Player Total Rushing Yards',
                'Fliff': 'Rushing Yards',
                'HardRock': 'Rushing Yards',
                'Hotstreak': 'rushing_yards',
                'ParlayPlay': 'Rushing Yards',
                'PrizePicks': 'Rush Yards',
                'Sleeper': 'rushing_yards',
                'SportsBattle': 'Rush Yards',
                'Underdog': 'Rushing Yards',
                'VividPicks': 'RushingYards',
                'JockMKT': 'rushing_yards',
                'Fanatics': 'Rushing Yards',
                'Rebet': 'total rushing yards',
                'Novig': 'RUSHING_YARDS',
                'Pinnacle': 'Rushing Yards',
                'Pick6': 'Rushing Yards',
            },
            modelInfo: { model: "negbin", p: 0.01 },
            preferredSharp: 'DraftKings'
        },
        '1Q Rushing Yards': {
            mapping: {
                'Fanduel': '1st Qtr Rushing Yds',
                'PrizePicks': '1Q Rush Yards',
                'Underdog': '1Q Rushing Yards',
            },
            modelInfo: { model: "negbin", p: 0.01 },
            preferredSharp: 'Fanduel'
        },
        'Rushing Attempts': {
            mapping: {
                'DraftKings': 'Rushing Attempts',
                'Caesars': 'Total Rushing Attempts',
                'BetOnline': 'Carries',
                'PropBuilder': 'Carries', // Added mapping for PropBuilder
                'ESPNBet': 'Player Total Rushing Attempts',
                'Fanduel': 'Rush Attempts',
                'BetMGM': 'Rushing attempts',
                'Betr': 'RUSHING_ATTEMPTS',
                'Boom': 'RUSHING_ATTEMPTS',
                'Bovada': 'Total Rush Attempts',
                'Chalkboard': 'Rush Att',
                'Dabble': 'rushing-attempts',
                'Fliff': 'Rushing Attempts',
                'ParlayPlay': 'Rush Attempts',
                'PrizePicks': 'Rush Attempts',
                'SportsBattle': 'Rush Attempts',
                'Underdog': 'Rushing Attempts',
                'Hotstreak': 'carries',
                'VividPicks': 'Rush Attempts',
                'JockMKT': 'rushing_attempts',
                'Rebet': 'total carries',
                'Novig': 'RUSHING_ATTEMPTS',
                'Pinnacle': 'Rushing Attempts'
            },
            modelInfo: { model: "negbin", p: 0.1 },
            preferredSharp: 'DraftKings'
        },
        'Rush + Rec Yards': {
            mapping: {
                'DraftKings': 'Rushing + Receiving Yards',
                //'Caesars': '',
                'Fanduel': 'Rushing + Receiving Yds',
                'BetMGM': 'Total rushing and receiving yards',
                'Betr': 'RUSHING_RECEIVING_YARDS',
                'Bovada': 'Total Rushing & Receiving Yards',
                'Chalkboard': 'Rush + Rec Yds',
                'Dabble': 'receiving-yards_rushing-yards',
                'ESPNBet': 'Player Total Rushing + Receiving Yards',
                'Fliff': 'Rushing + Receiving Yards',
                'PrizePicks': 'Rush+Rec Yds',
                'Sleeper': 'rushing_and_receiving_yards',
                'Underdog': 'Rush + Rec Yards',
                'HardRock': 'Rushing + Receiving Yards',
                'Hotstreak': 'receiving_plus_rushing_yards',
                'SportsBattle': 'Rush + Rec Yards',
                'VividPicks': 'Rush + Rec Yards',
                'JockMKT': 'rushing_plus_receiving_yards',
                'Pick6': 'Rush + Rec Yards',
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        'Solo Tackles': {
            mapping: {
                'DraftKings': 'Tackles',
                //'Caesars': '',
                'BetMGM': 'Tackles',
                'Chalkboard': 'Tackles',
                'Dabble': 'tackles',
                'ESPNBet': 'Player Total Solo Tackles',
                'Fliff': 'Solo Tackles',
                'SportsBattle': 'Solo Tackles',
                'Underdog': 'Solo Tackles',
                'Betr': 'SOLO_TACKLES',
                'Pick6': 'Solo Tackles',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists',
                //'Caesars': '',
                'BetMGM': 'Assists',
                'Chalkboard': 'Assists',
                'Dabble': 'assists',
                'ESPNBet': 'Player Total Assisted Tackles',
                'Fliff': 'Assisted Tackles',
                'SportsBattle': 'Assists',
                'Underdog': 'Assists',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Receiving Yards': {
            mapping: {
                'DraftKings': 'Receiving Yards',
                'Caesars': 'Total Receiving Yards',
                'BetOnline': 'Receiving Yards',
                'PropBuilder': 'Receiving Yards', // Added mapping for PropBuilder
                'Fanduel': 'Receiving Yds',
                'BetMGM': 'Receiving yards',
                'Betr': 'RECEIVING_YARDS',
                'Boom': 'RECEIVING_YARDS',
                'Bovada': 'Total Receiving Yards',
                'Chalkboard': 'Rec Yards',
                'Dabble': 'receiving-yards',
                'ESPNBet': 'Player Total Receiving Yards',
                'Fliff': 'Receiving Yards',
                'HardRock': 'Receiving Yards',
                'Hotstreak': 'receiving_yards',
                'ParlayPlay': 'Receiving Yards',
                'PrizePicks': 'Receiving Yards',
                'Sleeper': 'receiving_yards',
                'SportsBattle': 'Rec Yards',
                'Underdog': 'Receiving Yards',
                'VividPicks': 'ReceivingYards',
                'JockMKT': 'receiving_yards',
                'Fanatics': 'Receiving Yards',
                'Rebet': 'total receiving yards',
                'Novig': 'RECEIVING_YARDS',
                'Pinnacle': 'Receiving Yards',
                'Pick6': 'Receiving Yards',
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        '1Q Receiving Yards': {
            mapping: {
                'Fanduel': '1st Qtr Receiving Yds',
                'PrizePicks': '1Q Receiving Yards',
                'Underdog': '1Q Receiving Yards',
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'Fanduel'
        },
        'Receptions': {
            mapping: {
                'DraftKings': 'Receptions',
                'Circa': 'RECEPTIONS',
                'Caesars': 'Total Receptions',
                'BetOnline': 'Receptions',
                'PropBuilder': 'Receptions', // Added mapping for PropBuilder
                'Fanduel': 'Total Receptions',
                'BetMGM': 'Receptions made',
                'Betr': 'CATCHES',
                'Boom': 'RECEPTIONS',
                'Bovada': 'Total Receptions',
                'Chalkboard': 'Rec',
                'Dabble': 'receptions',
                'ESPNBet': 'Player Total Receptions',
                'Fliff': 'Receptions',
                'HardRock': 'Receptions',
                'Hotstreak': 'receptions',
                'ParlayPlay': 'Receptions',
                'PrizePicks': 'Receptions',
                'Sleeper': 'receptions',
                'Underdog': 'Receptions',
                'VividPicks': 'Receptions',
                'Rebet': 'total receptions',
                'Novig': 'RECEPTIONS',
                'Pinnacle': 'Receptions',
                'Pick6': 'Receptions',

            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'FG Made': {
            mapping: {
                'DraftKings': 'Field Goals Made',
                'Caesars': 'Total Made Field Goals',
                'BetMGM': 'Field goals made',
                'Betr': 'FIELD_GOALS_MADE',
                'Chalkboard': 'FG Made',
                'Dabble': 'field-goals-made',
                'ESPNBet': 'Player Total Field Goals Made',
                'Fliff': 'Field Goal Made',
                'HardRock': 'Field Goals Made',
                'Hotstreak': 'field_goals',
                'ParlayPlay': 'FG Made',
                'PrizePicks': 'FG Made',
                'Sleeper': 'field_goal_made',
                'SportsBattle': 'FG Made',
                'Underdog': 'FG Made',
                'VividPicks': 'Field Goals Made',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Kicking Points': {
            mapping: {
                'DraftKings': 'Kicking Points',
                'Circa': 'POINTS',
                'Caesars': 'Total Kicking Points',
                //'Fanduel': 'Total Kicking Points',
                'BetMGM': 'Kicking Points',
                'Betr': 'KICKING_POINTS',
                'Bovada': 'Total Kicking Points',
                'Chalkboard': 'Kick Pts',
                'Dabble': 'kicking-points',
                'ESPNBet': 'Player Total Kicking Points',
                'Fliff': 'Kicking Points',
                'HardRock': 'Kicking Points',
                'Hotstreak': 'kicking_points',
                'ParlayPlay': 'Total Points',
                'PrizePicks': 'Kicking Points',
                'Sleeper': 'kicking_points',
                'Underdog': 'Kicking Points',
                'VividPicks': 'Kicking Points',
                'Pick6': 'Kicking Points',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'XP Made': {
            mapping: {
                'DraftKings': 'Extra Points Made',
                'Caesars': 'Total Made Extra Points',
                'BetMGM': 'Extra points made',
                'Chalkboard': 'XP Made',
                'Dabble': 'extra-points-made',
                'ESPNBet': 'Player Total Extra Points Made',
                'Fliff': 'Extra Point Made',
                'Underdog': 'XP Made',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Longest Pass Completion': {
            mapping: {
                'DraftKings': 'Longest Passing Completion',
                'Caesars': 'Longest Passing Completion',
                //'Fanduel': 'Longest Pass',
                'BetMGM': 'Longest passing completion',
                'Betr': 'LONGEST_PASS',
                'Bovada': 'Longest Pass Completion',
                'Chalkboard': 'Long Comp',
                'Dabble': 'longest-passing-completion',
                'ESPNBet': 'Player Longest Pass Completion',
                'Fliff': 'Longest Passing Completion',
                'HardRock': 'Longest Pass Completion',
                'Hotstreak': 'longest_pass',
                'ParlayPlay': 'Longest Pass',
                'SportsBattle': 'Long. Pass Yard',
                'Pinnacle': 'Longest Completion'
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        'Longest Rush': {
            mapping: {
                'DraftKings': 'Longest Rush',
                //'Fanduel': 'Longest Rush',
                'BetMGM': 'Longest rush',
                'Betr': 'LONGEST_RUSH',
                'Bovada': 'Longest Rushing Attempt',
                'Chalkboard': 'Long Rush',
                'Dabble': 'longest-rush',
                'ESPNBet': 'Player Longest Rush',
                'Fliff': 'Longest Rush',
                'Hotstreak': 'longest_rush',
                'ParlayPlay': 'Longest Rush',
                'SportsBattle': 'Long. Rush Yard',
                'VividPicks': 'Longest Rush',
                'Pinnacle': 'Longest Rush'
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        'Longest Reception': {
            mapping: {
                'DraftKings': 'Longest Reception',
                'Caesars': 'Longest Reception',
                'Fanduel': 'Longest Reception',
                'BetMGM': 'Longest reception',
                'Betr': 'LONGEST_CATCH',
                'Bovada': 'Longest Reception',
                'Chalkboard': 'Long Rec',
                'Dabble': 'longest-reception',
                'ESPNBet': 'Player Longest Reception',
                'Fliff': 'Longest Reception',
                'HardRock': 'Longest Reception',
                'Hotstreak': 'longest_reception',
                'ParlayPlay': 'Longest Reception',
                'SportsBattle': 'Long. Rec Yard',
                'VividPicks': 'Longest Reception',
                'JockMKT': 'longest_reception',
                'Pinnacle': 'Longest Reception'
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        '1st TD Scorer': {
            mapping: { 'Pinnacle': '1st TD Scorer' },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Anytime TD': {
            mapping: { 'Pinnacle': 'Anytime TD' },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        }
    },
    "NHL SERIES": {
        // Provide an entry so it doesn't fall back to "NHL".
        // If you want it to store multi-game lines, you can define the mapping below.
        // Or simply leave it empty to skip them in `processProjections`.
        "Shots On Goal": {
            mapping: {
                "PrizePicks": "Shots On Goal"
            },
            modelInfo: { model: "poisson" },
            preferredSharp: "DraftKings"
        },
    },
    'NHL': {
        'Blocked Shots': {
            mapping: {
                'DraftKings': 'Blocks',
                'Caesars': 'Blocked Shots',
                'ESPNBet': 'Player Total Blocked Shots',
                'Fliff': 'Player Blocked Shots',
                'ParlayPlay': 'Blocked Shots',
                'PrizePicks': 'Blocked Shots',
                'Sleeper': 'blocked_shots',
                'SportsBattle': 'Blocked Shots',
                'Underdog': 'Blocked Shots',
                'Pick6': 'Blocks',
                'Fanatics': 'Blocked Shots',
                'HardRock': 'Blocked Shots',
                'VividPicks': 'Blocked Shots',
                'Epick': 'blocks', // Corrected Epick mapping based on API
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Saves': {
            mapping: {
                'DraftKings': 'Saves',
                'BetOnline': 'Saves',
                'PropBuilder': 'Saves', // Added mapping for PropBuilder
                'Caesars': 'Total Saves',
                'Betr': 'SAVES',
                'BetSaracen': 'Saves',
                'Dabble': 'saves',
                'ESPNBet': 'Player Saves',
                'Fliff': 'Goaltender Saves',
                'ParlayPlay': 'Saves',
                'PrizePicks': 'Goalie Saves',
                'Sleeper': 'saves',
                'SportsBattle': 'Saves',
                'Underdog': 'Saves',
                'Fanatics': 'Saves',
                'HardRock': 'Saves',
                'VividPicks': 'GoaltendingSaves',
                'Rebet': 'total saves',
                'Pick6': 'Saves',
                'Epick': 'saves', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        'Points': {
            mapping: {
                'DraftKings': 'Points',
                'BetOnline': 'Points',
                'PropBuilder': 'Points', // Added mapping for PropBuilder
                //'Caesars': '',
                'BetMGM': 'Points',
                'Betr': 'POINTS',
                'BetSaracen': 'Points',
                'Dabble': 'points',
                'ESPNBet': 'Player Points',
                'Fliff': 'Player Points',
                'HardRock': 'Points',
                'Hotstreak': 'points',
                'ParlayPlay': 'Points',
                'PrizePicks': 'Points',
                'Sleeper': 'points',
                'Underdog': 'Points',
                'VividPicks': 'Points',
                'Pick6': 'Points',
                'Fanatics': 'Points',
                'Rebet': 'total points',
                'Epick': 'points', // Verified Epick mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Shots On Goal': {
            mapping: {
                'DraftKings': 'Shots on Goal',
                'Caesars': 'Player Total Shots On Goal',
                'BetOnline': 'Shots on goal',
                'PropBuilder': 'Shots on Goal', // Added mapping for PropBuilder
                //'Fanduel': 'PLAYER_TOTAL_SHOTS',
                'BetRivers': 'SHOTS',
                'BetSaracen': 'Shots',
                'BetMGM': 'Shots',
                'Betr': 'SHOTS_ON_GOAL',
                'Boom': 'SHOTS_ON_GOAL',
                'Dabble': 'shots',
                'ESPNBet': 'Player Total Shots On Goal',
                'Fliff': 'Player Shots',
                'HardRock': 'Shots',
                'Hotstreak': 'shots',
                'ParlayPlay': 'Shots on Goal',
                'PrizePicks': 'Shots On Goal',
                'Sleeper': 'shots',
                'Underdog': 'Shots',
                'VividPicks': 'ShotsOnGoal',
                'Pick6': 'Shots on Goal',
                'Fanatics': 'Shots',
                'OaklawnSports': 'Shots on goal by the player - Inc. OT',
                'Rebet': 'total shots on goal',
                'OwnersBox': 'Shots On Goal',
                'Epick': 'shots_on_goal', // Verified Epick mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists',
                //'Caesars': '',
                'BetMGM': 'Assists',
                'Betr': 'ASSISTS',
                'BetSaracen': 'Assists',
                'Dabble': 'assists',
                'ESPNBet': 'Player Total Assists',
                'Fliff': 'Player Assists',
                'HardRock': 'Assists',
                'ParlayPlay': 'Assists',
                'PrizePicks': 'Assists',
                'Sleeper': 'assists',
                'SportsBattle': 'Assists',
                'Underdog': 'Assists',
                'JockMKT': 'assists',
                'Pick6': 'Assists',
                'Fanatics': 'Assists',
                'VividPicks': 'Assists',
                'Epick': 'assists', // Verified Epick mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Power Play Points': {
            mapping: {
                'DraftKings': 'Power Play Points',
                //'Caesars': '',
                'BetMGM': 'Powerplay points',
                'BetSaracen': 'Powerplay Points',
                'ESPNBet': 'Player Power Play Points',
                'Fliff': 'Player Powerplay Points',
                'HardRock': 'Powerplay Points',
                'Hotstreak': 'power_play_points',
                'Sleeper': 'powerplay_points',
                'Underdog': 'Power Play Points',
                'Fanatics': 'Powerplay Points',
                'Epick': 'power_play_points', // Added Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Goals Against': {
            mapping: {
                'DraftKings': 'Goals Against',
                //'Caesars': '',
                'BetSaracen': 'Goals Against',
                'Fliff': 'Goaltender Goals Against',
                'Sleeper': 'goals_against',
                'Dabble': 'goals-against',
                'HardRock': 'Goals Against',
                'Epick': 'goals_allowed', // Corrected Epick mapping based on API
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
            // Add more mappings as needed
        },
        'Goals': {
            mapping: {
                'DraftKings': 'Goals',
                'BetOnline': 'Goals',
                'PropBuilder': 'Goals',
                'Caesars': 'Total Goals',
                'Pinnacle': 'Goals',
                'Epick': 'goals', // Verified Epick mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Pinnacle'
        },
        'Faceoffs Won': {
            mapping: {
                'OwnersBox': 'Faceoffs Won',
                'Epick': 'faceoffs_won', // Added Epick mapping based on standard, add if data received
            },
             modelInfo: { model: "poisson" },
             preferredSharp: 'DraftKings'
        },
        'Hits': {
            mapping: {
                'OwnersBox': 'Hits',
                'Epick': 'hits', // Added Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Time On Ice': {
            mapping: {
                'OwnersBox': 'Time On Ice (In Reg)',
                'Epick': 'time_on_ice_seconds', // Added Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "poisson" }, // May need adjustment based on actual units
            preferredSharp: 'DraftKings'
        },
    },
    'DARTS': { // Placeholder for potential future mapping
        'Player 180s': {
            mapping: {
                'DraftKings': 'Player 180s',
                'Caesars': 'Total 180s',
                'PrizePicks': 'Player 180s',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
    },
    'SOCCER': { // Placeholder for potential future mapping
        'Shots on Target': {
            mapping: {
                'BetRivers': 'Shots on Target',
                'Fanduel': 'Shots On Target',
                'BetOnline': 'Shots on Goal',
                'PropBuilder': 'Shots on Goal', // Added mapping for PropBuilder
                'PrizePicks': 'Shots On Target',
                'Boom': 'SHOTS_ON_GOAL',
                'ParlayPlay': 'Shots on Goal',
                'VividPicks': 'Shots OnTarget',
                'Underdog': 'Shots on Target',
                'Dabble': 'oddsjam_shots_on_target',
                'ESPNBet': 'Player Shots on Target Over/Under',
                'Pick6': 'Shots on Target'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'BetOnline'
        },
        'Shots': {
            mapping: {
                'BetRivers': 'Shots',
                'Fanduel': 'Shots',
                'BetOnline': 'Shots',
                'PropBuilder': 'Shots', // Added mapping for PropBuilder
                'Boom': 'SHOTS',
                'PrizePicks': 'Shots',
                'ParlayPlay': 'Shots',
                'VividPicks': 'Shot Attempts',
                'Underdog': 'Shots Attempted',
                'Dabble': 'oddsjam_shots',
                'ESPNBet': 'Player Total Shots Over/Under',
                'Pick6': 'Shots'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'BetOnline'
        },
        'Tackles': {
            mapping: {
                'BetOnline': 'Tackles',
                'PropBuilder': 'Tackles', // Added mapping for PropBuilder
                'PrizePicks': 'Tackles',
                'Underdog': 'Tackles',
                'Dabble': 'oddsjam_tackles',
                'ESPNBet': 'Player Total Tackles Over/Under',
                'Pick6': 'Tackles'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'BetOnline'
        },
        'Passes Attempted': {
            mapping: {
                'BetOnline': 'Passes',
                'PropBuilder': 'Passes', // Added mapping for PropBuilder
                'PrizePicks': 'Passes Attempted',
                'Underdog': 'Passes',
                'Dabble': 'oddsjam_passes_attempted',
                'ESPNBet': 'Player Total Passes Over/Under',
                'Pick6': 'Passes Attempted'
            },
            modelInfo: { model: "negbin", p: 0.1 },
            preferredSharp: 'BetOnline'
        },
    },
    'MLB': {
        'Pitcher Strikeouts': {
            mapping: {
                'DraftKings': 'Strikeouts Thrown',
                'Caesars': 'Total Pitching Strikeouts',
                'Fanduel': 'Strikeouts',
                'Circa': 'STRIKEOUTS',
                'Pinnacle': 'Total Strikeouts',
                'PrizePicks': 'Pitcher Strikeouts',
                'Betr': 'STRIKEOUTS',
                'Boom': 'STRIKEOUTS',
                'Dabble': 'strikeouts',
                'Fliff': 'Pitcher Strikeouts',
                'ESPNBet': 'Pitcher Total Strikeouts',
                'JockMKT': 'strikeouts',
                'ParlayPlay': 'Strikeouts (K)',
                'Sleeper': 'strike_outs',
                'SportsBattle': 'Pitcher Ks',
                'Underdog': 'Strikeouts',
                'BetMGM': 'Strikeouts',
                'BetOnline': 'Strikeouts',
                'PropBuilder': 'Strikeouts',
                'BetRivers': 'Strikeouts', // Changed from 'K'
                'Bovada': 'Total Strikeouts',
                'VividPicks': 'PitchingStrikeouts',
                'HardRock': 'Strikeouts',
                'BetSaracen': 'Strikeouts',
                'Fanatics': 'Strikeouts',
                'Rebet': 'total pitcher strikeouts',
                'Thrillzz': 'Under/Over Pitcher Strikeouts',
                'Hotstreak': 'ks',
                'Novig': 'PITCHER_STRIKEOUTS',
                'Pick6': 'Strikeouts Thrown',
                'OwnersBox': 'Strikeouts', // Added mapping
                'ProphetX': 'Strikeouts',
                'Epick': 'pitching_strikeouts', // Corrected Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.75 },
            preferredSharp: 'Fanduel'
        },
        'Pitcher Outs': {
            mapping: {
                'DraftKings': 'Outs',
                'Caesars': 'Total Outs Recorded',
                'Pinnacle': 'Pitching Outs',
                'PrizePicks': 'Pitching Outs',
                'Underdog': 'Pitching Outs',
                'Betr': 'TOTAL_OUTS',
                'Fanduel': 'Outs Recorded',
                'Dabble': 'outs',
                'BetMGM': 'Pitcher outs',
                'Bovada': 'Total Pitcher Outs',
                'VividPicks': 'Outs',
                'HardRock': 'Outs',
                'BetSaracen': 'Outs',
                'JockMKT': 'total_outs',
                'ESPNBet': 'Pitcher Total Outs Recorded',
                'Sleeper': 'outs',
                'Fliff': 'Pitcher Outs Recorded',
                'Thrillzz': 'Under/Over Pitcher Outs',
                'Hotstreak': 'pitcher_outs',
                'Pick6': 'Outs',
                'OwnersBox': 'Outs Recorded', // Added mapping
                'ProphetX': 'Pitcher Outs',
                'Epick': 'pitching_outs', // Corrected Epick mapping
                'Novig': 'PITCHER_OUTS',
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'Circa'
        },
        'Runs': {
            mapping: {
                'DraftKings': 'Runs Scored',
                'Fanduel': 'Runs Scored',
                'Pinnacle': 'Runs',
                'Caesars': 'Total Runs Scored',
                'PrizePicks': 'Runs',
                'Betr': 'RUNS',
                'Boom': 'RUNS',
                'Dabble': 'runs',
                'Fliff': 'Player Runs',
                'JockMKT': 'runs',
                'ParlayPlay': 'Runs',
                'Sleeper': 'runs',
                'Underdog': 'Runs',
                'BetMGM': 'Runs',
                'BetRivers': 'Runs Scored', // Changed from 'RUNS'
                'VividPicks': 'Runs',
                'HardRock': 'Runs',
                'BetSaracen': 'Runs',
                'Hotstreak': 'runs',
                'Pick6': 'Runs',
                'OwnersBox': 'Runs Scored', // Added mapping
                'ProphetX': 'Runs',
                'Epick': 'runs', // Verified Epick mapping
                'Novig': 'RUNS',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Total Bases': {
            mapping: {
                'Circa': 'TOTAL BASES',
                'DraftKings': 'Total Bases',
                'Fanduel': 'Total Bases',
                'Pinnacle': 'Total Bases',
                'Caesars': 'Total Bases',
                'PrizePicks': 'Total Bases',
                'Betr': 'TOTAL_BASES',
                'Boom': 'TOTAL_BASES',
                'Dabble': 'total-bases',
                'Fliff': 'Player Total Bases',
                'ESPNBet': 'Player Total Bases',
                'JockMKT': 'total_bases',
                'ParlayPlay': 'Total Bases',
                'Sleeper': 'total_bases',
                'Underdog': 'Total Bases',
                'BetMGM': 'Bases',
                'BetOnline': 'Total bases',
                'PropBuilder': 'Total Bases', // Added mapping
                'BetRivers': 'Total Bases', // Changed from 'BASES'
                'Bovada': 'Total Bases',
                'VividPicks': 'TotalBases',
                'HardRock': 'Total Bases',
                'BetSaracen': 'Total Bases',
                'Fanatics': 'Total Bases',
                'Rebet': 'total bases',
                'Thrillzz': 'Under/Over Player Total Bases',
                'Hotstreak': 'bases',
                'Novig': 'TOTAL_BASES',
                'Pick6': 'Total Bases (From Hits)',
                'OwnersBox': 'Total Bases', // Added mapping
                'ProphetX': 'Total Bases',
                'Epick': 'total_bases', // Verified Epick mapping
            },
            modelInfo: { model: "negbin", p: 0.25 },
            preferredSharp: 'Circa'
        },
        'Batter Strikeouts': {
            mapping: {
                'DraftKings': 'Strikeouts',
                'PrizePicks': 'Hitter Strikeouts',
                'Betr': 'HITTER_STRIKEOUTS',
                'Fliff': 'Batter Strikeouts',
                'JockMKT': 'total_strikeouts',
                'ParlayPlay': 'Strikeouts',
                'Sleeper': 'bat_strike_outs',
                'Underdog': 'Batter Strikeouts',
                'BetMGM': 'Batter strikeouts',
                'VividPicks': 'Batter Strikeouts',
                'HardRock': 'Times Struck Out',
                'Hotstreak': 'strike_out',
                'OwnersBox': 'Batter Strikeouts', // Added mapping
                'ProphetX': 'Strikeouts',
                'Epick': 'batting_strikeouts', // Corrected Epick mapping
                'Novig': 'BATTING_STRIKEOUTS',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Pitcher Earned Runs Allowed': {
            mapping: {
                'DraftKings': 'Earned Runs Allowed',
                'Caesars': 'Total Earned Runs Allowed',
                'Pinnacle': 'Earned Runs',
                'PrizePicks': 'Earned Runs Allowed',
                'Betr': 'EARNED_RUNS',
                'Dabble': 'earned-runs',
                'Fliff': 'Pitcher Earned Runs',
                'JockMKT': 'earned_runs',
                'ParlayPlay': 'Earned Runs',
                'Sleeper': 'earned_runs',
                'Underdog': 'Earned Runs Allowed',
                'BetMGM': 'Pitcher earned runs',
                'BetOnline': 'Earned runs',
                'PropBuilder': 'Earned Runs', // Added mapping
                'Bovada': 'Total Earned Runs',
                'VividPicks': 'Earned Runs',
                'HardRock': 'Earned Runs',
                'BetSaracen': 'Earned Runs',
                'ESPNBet': 'Pitcher Earned Runs Allowed',
                'Thrillzz': 'Under/Over Pitcher Runs Earned',
                'Hotstreak': 'earned_runs',
                'Pick6': 'Earned Runs Allowed',
                'OwnersBox': 'Earned Runs Allowed', // Added mapping
                'ProphetX': 'Runs Allowed',
                'Epick': 'pitching_earned_runs', // Verified Epick mapping
                'Novig': 'EARNED_RUNS',
            },
            modelInfo: { model: "negbin", p: 0.75 },
            preferredSharp: 'Circa'
        },
        'Hits + Runs + RBIs': {
            mapping: {
                'DraftKings': 'Hits + Runs + RBIs',
                'PrizePicks': 'Hits+Runs+RBIS',
                'Pinnacle': 'Hits + Runs + RBIs',
                'Betr': 'HITS_RUNS_RUNS_BATTED_IN',
                'Fliff': 'Player Hits And Runs And RBIs',
                'JockMKT': 'hits_plus_runs_plus_RBIs',
                'ParlayPlay': 'Hit + Run + RBI',
                'Sleeper': 'hits_runs_rbis',
                'Underdog': 'Hits + Runs + RBIs',
                'BetMGM': 'Total hits, runs and RBIs',
                'VividPicks': 'Hits + Runs + RBIs',
                'ESPNBet': 'Player Total Hits + Runs + RBIs',
                'Hotstreak': 'hits_runs_rbis',
                'Pick6': 'Hits + Runs + RBIs',
                'OwnersBox': 'Hits + Runs + RBIs', // Added mapping
                'ProphetX': 'Hits + Runs + RBIs',
                'Epick': 'hits+runs+rbis', // Verified Epick mapping
                'Novig': 'HITS_RUNS_RBIS',
            },
            modelInfo: { model: "negbin", p: 0.25 },
            preferredSharp: 'Circa'
        },
        'Pitcher Walks Allowed': {
            mapping: {
                'DraftKings': 'Walks Allowed',
                'Caesars': 'Pitcher Total Walks Allowed',
                'PrizePicks': 'Walks Allowed',
                'Betr': 'PITCHING_WALKS',
                'Fliff': 'Pitcher Walks',
                'ParlayPlay': 'Walks Allowed',
                'Underdog': 'Walks Allowed',
                'BetMGM': 'Pitcher walks',
                'Bovada': 'Total Pitcher Walks',
                'VividPicks': 'Walks Allowed',
                'BetSaracen': 'Walks',
                'ESPNBet': 'Player Total Walks Allowed',
                'Sleeper': 'walks',
                'Hotstreak': 'walks_allowed',
                'Pick6': 'Walks Allowed',
                'OwnersBox': 'Walks Allowed', // Added mapping
                'ProphetX': 'Walks Allowed',
                'Epick': 'pitching_walks', // Corrected Epick mapping
                'Novig': 'PITCHING_WALKS',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Batter Walks': {
            mapping: {
                'DraftKings': 'Walks',
                'PrizePicks': 'Walks',
                'Betr': 'WALKS',
                'Dabble': 'walks',
                'Fliff': 'Batter Walks',
                'ParlayPlay': 'Walks',
                'Sleeper': 'bat_walks',
                'Underdog': 'Batter Walks',
                'BetMGM': 'Batter walks',
                'VividPicks': 'Batter Walks',
                'Pick6': 'Walks',
                'OwnersBox': 'Batter Walks', // Added mapping
                'ProphetX': 'Walks',
                'Caesars': 'Batter Total Walks',
                'Epick': 'batting_walks', // Corrected Epick mapping
                'Novig': 'WALKS',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Pitcher Hits Allowed': {
            mapping: {
                'DraftKings': 'Hits Allowed',
                'BetOnline': 'Hits Allowed',
                'PropBuilder': 'Hits Allowed', // Added mapping
                'Pinnacle': 'Hits Allowed',
                'Caesars': 'Total Hits Allowed',
                'PrizePicks': 'Hits Allowed',
                'Betr': 'HITS_ALLOWED',
                'Fliff': 'Pitcher Hits Allowed',
                'JockMKT': 'hits_allowed',
                'ParlayPlay': 'Hits Allowed',
                'Sleeper': 'hits_allowed',
                'Underdog': 'Hits Allowed',
                'BetMGM': 'Pitcher hits allowed',
                'Bovada': 'Total Hits Allowed',
                'VividPicks': 'Hits Allowed',
                'HardRock': 'Hits Allowed',
                'BetSaracen': 'Hits Allowed',
                'ESPNBet': 'Player Total Hits Allowed',
                'Thrillzz': 'Under/Over Pitcher Hits Allowed',
                'Hotstreak': 'hits_allowed',
                'Pick6': 'Hits Against',
                'OwnersBox': 'Hits Allowed', // Added mapping
                'ProphetX': 'Hits Allowed',
                'Epick': 'pitching_hits_allowed', // Corrected Epick mapping
                'Novig': 'HITS_ALLOWED',
            },
            modelInfo: { model: "negbin", p: 0.5 },
            preferredSharp: 'Circa'
        },
        'Home Runs': {
            mapping: {
                'BetOnline': 'Home Runs',
                'PropBuilder': 'Home Runs', // Added mapping
                'BetRivers': 'Home Runs',
                'Circa': 'HOME RUNS',
                'DraftKings': 'Home Runs',
                'Fanduel': 'Home Runs',
                'Pinnacle': 'Home Runs',
                'PrizePicks': 'Home Runs', // Added mapping
                'Fliff': 'BATTER HOME RUNS', // Updated value
                'ESPNBet': 'Player Total Home Runs Hit',
                'ParlayPlay': 'Homeruns',
                'Sleeper': 'home_runs',
                'Underdog': 'Home Runs',
                'BetMGM': 'Home Runs',
                'HardRock': 'Home Runs',
                'BetSaracen': 'Home Runs',
                'Fanatics': 'Home Runs',
                'Thrillzz': 'Under/Over Player Home Runs',
                'Hotstreak': 'home_run',
                'Novig': 'HOME_RUNS',
                'Pick6': 'Home Runs',
                'VividPicks': 'Home Runs', // Added mapping
                'OwnersBox': 'Home Runs', // Added mapping
                'ProphetX': 'Home Runs',
                'Epick': 'home_runs', // Added Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Hits': {
            mapping: {
                'Circa': 'HITS',
                'DraftKings': 'Hits',
                'Caesars': 'Total Hits',
                'Pinnacle': 'Hits',
                'Fanduel': 'Hits',
                'PrizePicks': 'Hits',
                'Betr': 'HITS',
                'Boom': 'TOTAL_HITS',
                'Fliff': 'BATTER HITS', // Updated value
                'ESPNBet': 'Player Total Hits',
                'JockMKT': 'hits',
                'Sleeper': 'hits',
                'Underdog': 'Hits', // Added mapping
                'BetMGM': 'Hits',
                'BetOnline': 'Hits',
                'PropBuilder': 'Hits', // Added mapping
                'BetRivers': 'Hits', // Changed from 'HITS'
                'Bovada': 'Total Hits',
                'VividPicks': 'Hits',
                'HardRock': 'Hits',
                'BetSaracen': 'Hits',
                'Fanatics': 'Hits',
                'Thrillzz': 'Under/Over Player Hits',
                'Hotstreak': 'hit',
                'Pick6': 'Hits',
                'OwnersBox': 'Hits', // Added mapping
                'ProphetX': 'Hits',
                'Epick': 'hits', // Verified Epick mapping
                'Novig': 'HITS',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'RBIs': {
            mapping: {
                'BetRivers': 'RBIs',
                'Circa': 'RBI',
                'DraftKings': 'RBIs',
                'Fanduel': 'RBIs',
                'Pinnacle': 'RBIs',
                'Caesars': 'Total RBIs',
                'PrizePicks': 'RBIs',
                'Fliff': 'BATTER RBIS', // Updated value
                'ESPNBet': 'Player Total RBIs',
                'ParlayPlay': 'RBIs',
                'Sleeper': 'rbis',
                'Underdog': 'RBIs',
                'BetMGM': 'Runs batted in',
                'HardRock': 'RBIs',
                'BetSaracen': 'RBIs',
                'Fanatics': 'RBIs',
                'Boom': 'RBI',
                'Betr': 'RUNS_BATTED_IN',
                'Dabble': 'rbis',
                'Hotstreak': 'rbis',
                'Pick6': 'Runs Batted In',
                'VividPicks': 'RBIs', // Added mapping
                'OwnersBox': 'RBIs', // Added mapping
                'ProphetX': 'RBIs',
                'Epick': 'rbis', // Verified Epick mapping
                'Novig': 'RBIS',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Stolen Bases': {
            mapping: {
                'BetRivers': 'Stolen Bases',
                'DraftKings': 'Stolen Bases',
                'BetOnline': 'Stolen bases',
                'PropBuilder': 'Stolen Bases', // Added mapping
                'Pinnacle': 'Stolen Bases',
                'Fanduel': 'Stolen Bases',
                'PrizePicks': 'Stolen Bases',
                'Sleeper': 'stolen_bases',
                'Underdog': 'Stolen Bases',
                'BetMGM': 'Stolen bases',
                'HardRock': 'Stolen Bases',
                'BetSaracen': 'Stolen Bases',
                'Fanatics': 'Stolen Bases',
                'ESPNBet': 'Player Total Stolen Bases',
                'Circa': 'STOLEN BASES',
                'Fliff': 'BATTER STOLEN BASES',
                'Hotstreak': 'stolen_bases',
                'VividPicks': 'Stolen Bases', // Added mapping
                'OwnersBox': 'Stolen Bases', // Added mapping
                'ProphetX': 'Stolen Bases',
                'Epick': 'stolen_bases', // Added Epick mapping based on standard, add if data received
                'Novig': 'STOLEN_BASES',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Singles': {
            mapping: {
                'BetRivers': 'Singles',
                'DraftKings': 'Singles',
                'Fanduel': 'Singles',
                'Caesars': 'Batter Total Singles',
                'Betr': 'SINGLES',
                'Boom': 'SINGLES',
                'PrizePicks': 'Singles',
                'Dabble': 'singles',
                'Fliff': 'BATTER SINGLES', // Updated value
                'JockMKT': 'singles',
                'ParlayPlay': 'Singles',
                'Sleeper': 'singles',
                'Underdog': 'Singles',
                'BetMGM': 'Singles',
                'VividPicks': 'Singles',
                'HardRock': 'Singles',
                'BetSaracen': 'Singles',
                'ESPNBet': 'Player Total Singles Hit',
                'Fanatics': 'Singles',
                'Thrillzz': 'Under/Over Player Singles',
                'Hotstreak': 'single',
                'Pick6': 'Singles',
                'OwnersBox': 'Singles', // Added mapping
                'ProphetX': 'Singles',
                'Epick': 'singles', // Verified Epick mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Doubles': {
            mapping: {
                'BetRivers': 'Doubles',
                'DraftKings': 'Doubles',
                'Fanduel': 'Doubles',
                'Caesars': 'Batter Total Doubles',
                'Betr': 'DOUBLES',
                'PrizePicks': 'Doubles',
                'Fliff': 'BATTER DOUBLES', // Updated value
                'ParlayPlay': 'Doubles',
                'Sleeper': 'doubles',
                'Underdog': 'Doubles',
                'BetMGM': 'Doubles',
                'HardRock': 'Doubles',
                'BetSaracen': 'Doubles',
                'ESPNBet': 'Player Total Doubles Hit',
                'Thrillzz': 'Under/Over Player Doubles',
                'Hotstreak': 'double',
                'VividPicks': 'Doubles', // Added mapping
                'OwnersBox': 'Doubles', // Added mapping
                'ProphetX': 'Doubles',
                'Epick': 'doubles', // Added Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Triples': {
            mapping: {
                'BetRivers': 'Triples',
                'DraftKings': 'Triples',
                'Fanduel': 'Triples',
                'Caesars': 'Total Triples',
                'PrizePicks': 'Triples', // Added mapping
                'ParlayPlay': 'Triples',
                'Underdog': 'Triples', // Added mapping
                'BetMGM': 'Triples',
                'BetSaracen': 'Triples',
                'Hotstreak': 'triple',
                'HardRock': 'Triples',
                'VividPicks': 'Triples', // Added mapping
                'OwnersBox': 'Triples', // Added mapping
                'ProphetX': 'Triples',
                'Epick': 'triples', // Added Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Runs + RBIs': {
            mapping: {
                'BetRivers': 'Runs + RBIs',
                'BetOnline': 'Runs + RBIs',
                'PropBuilder': 'Runs + RBIs',
                'PrizePicks': 'Runs+RBIs',
                'Underdog': 'Runs + RBIs',
                'VividPicks': 'Runs + RBIs',
                'Rebet': 'total runs + runs batted in',
                'OwnersBox': 'Runs + RBIs', // Added mapping
                'ProphetX': 'Runs + RBIs',
                'Epick': 'runs+rbis', // Verified Epick mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Circa'
        },
        'Fantasy Points': {
            mapping: {
                'PrizePicks': 'Fantasy Points', // Added mapping
                'Underdog': 'Fantasy Points',
                'VividPicks': 'Fantasy Points', // Added mapping
                'OwnersBox': 'Fantasy Score', // Added mapping
                'Epick': 'fantasy_score', // Added Epick mapping based on standard, add if data received
            },
            modelInfo: { model: "negbin", p: 0.1 },
            preferredSharp: 'DraftKings'
        },
        '1st Inn. Strikeouts': {
            mapping: {
                'PrizePicks': '1st Inn. Strikeouts', // Added mapping
                'Underdog': '1st Inn. Strikeouts',
                'VividPicks': '1st Inn. Strikeouts', // Added mapping
                'OwnersBox': '1st Inn. Strikeouts' // Added mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        '1st Inn. Runs Allowed': {
            mapping: {
                'PrizePicks': '1st Inn. Runs Allowed', // Added mapping
                'Underdog': '1st Inn. Runs Allowed',
                'VividPicks': '1st Inn. Runs Allowed', // Added mapping
                'OwnersBox': '1st Inn. Runs Allowed' // Added mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        '1st Inn. Hits Allowed': {
            mapping: {
                'PrizePicks': '1st Inn. Hits Allowed', // Added mapping
                'Underdog': '1st Inn. Hits Allowed',
                'VividPicks': '1st Inn. Hits Allowed', // Added mapping
                'OwnersBox': '1st Inn. Hits Allowed' // Added mapping
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
    },
    'HRDERBY': { // Placeholder for potential future mapping
        'Round 1 HRs Hit': {
            mapping: {
                'DraftKings': 'TOTAL_HR_R1',
                'PrizePicks': 'Home Runs Round 1',
                //'ParlayPlay': 'Homeruns',
                'Dabble': 'total-home-runs-r1',
                'Underdog': 'Round 1 HRs Hit',
                'VividPicks': 'Round 1 Hrs Hit'
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        'Round 2 HRs Hit': {
            mapping: {
                'DraftKings': 'TOTAL_HR_R2',
                'PrizePicks': 'Home Runs Round 2',
                //'ParlayPlay': 'Homeruns',
                'Dabble': 'total-home-runs-r2',
                'Underdog': 'Round 2 HRs Hit',
                'VividPicks': 'Round 2 Hrs Hit'
                //'Underdog': '',
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        'Round 3 HRs Hit': {
            mapping: {
                'DraftKings': 'TOTAL_HR_R3',
                'PrizePicks': 'Home Runs Round 3',
                //'ParlayPlay': 'Homeruns',
                'Dabble': 'total-home-runs-r3',
                'Underdog': 'Round 3 HRs Hit',
                'VividPicks': 'Round 3 Hrs Hit'
                //'Underdog': '',
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        'Longest HR Distance (ft)': {
            mapping: {
                'DraftKings': 'DISTANCE',
                'PrizePicks': 'Longest Home Run (FT)',
                'Underdog': 'Longest HR Distance (ft)',
                'VividPicks': 'Longest Hr Distance (ft)'
            },
            modelInfo: { model: "negbin", p: 0.25 },
            preferredSharp: 'DraftKings'
        },
        'Max HR Exit Velocity (mph)': {
            mapping: {
                'DraftKings': 'EXIT_VELO',
                'PrizePicks': 'Exit Velocity (MPH)',
                'Underdog': 'Max HR Exit Velocity (mph)',
                'VividPicks': 'Max Hr Exit Velocity (mph)',
                'Bovada': 'Highest Recorded Exit Velocity HR'
            },
            modelInfo: { model: "negbin", p: 0.25 },
            preferredSharp: 'DraftKings'
        },
    },
    'LAX': { // Placeholder for potential future mapping
        'Points': {
            mapping: {
                'DraftKings': 'Points',
                'PrizePicks': 'Points',
                'Underdog': 'Points',
                'BetMGM': 'Points',
                'Fanduel': ' Total Points',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Saves': {
            mapping: {
                'DraftKings': 'Saves',
                'PrizePicks': 'Saves',
                'Underdog': 'Saves',
                'BetMGM': 'Saves',
                'Fanduel': ' Total Saves',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
    },
    'TENNIS': { // Placeholder for potential future mapping
        'Games Won': {
            mapping: {
                'DraftKings': 'Player Games Won',
                'Underdog': 'Games Won',
                'Boom': 'GAMES_WON'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Aces': {
            mapping: {
                'DraftKings': 'Total Aces',
                'PrizePicks': 'Aces',
                'Underdog': 'Aces',
                'Boom': 'ACES',
                'Dabble': 'oddsjam_aces'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Double Faults': {
            mapping: {
                'DraftKings': 'Total Double Faults',
                'Underdog': 'Double Faults',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Break Points Won': {
            mapping: {
                'DraftKings': 'Player Service Breaks',
                'PrizePicks': 'Break Points Won',
                'Underdog': 'Breakpoints Won',
                'Boom': 'BREAKPOINTS_WON',
                'Dabble': 'oddsjam_break_points_won'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
    },
    'CRICKET': { // Placeholder for potential future mapping
        'Fours': {
            mapping: {
                'DraftKings': 'Total Batter Fours',
                'PrizePicks': 'Fours',
                'Underdog': 'Fours',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Sixes': {
            mapping: {
                'DraftKings': 'Total Batter Sixes',
                'PrizePicks': 'Sixes',
                'Underdog': 'Sixes',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
    },
    'MMA': { // Placeholder for potential future mapping
        'Significant Strikes': {
            mapping: {
                'DraftKings': 'Significant Strikes',
                'PrizePicks': 'Significant Strikes',
                'Underdog': 'Significant Strikes',
                'Betr': 'SIG_STRIKES',
                'Boom': 'SIGNIFICANT_STRIKES',
                'ParlayPlay': 'Significant Strikes',
                'Dabble': 'oddsjam_significant_strikes',
                'Pick6': 'Significant Strikes',
            },
            modelInfo: { model: "negbin", p: 0.01 },
            preferredSharp: 'DraftKings'
        },
        'Takedowns': {
            mapping: {
                'DraftKings': 'Takedowns Landed',
                'PrizePicks': 'Takedowns',
                'Underdog': 'Takedowns',
                'Betr': 'TAKEDOWNS',
                'Boom': 'TAKEDOWNS',
                'ParlayPlay': 'Takedown',
                'Dabble': 'oddsjam_takedowns',
                'Pick6': 'Takedowns',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
    },
    'GOLF': { // Placeholder for potential future mapping
        'Strokes': {
            mapping: {
                'DraftKings': 'Strokes',
                'PrizePicks': 'Strokes',
                'Underdog': 'Strokes',
                'ParlayPlay': 'Strokes',
                'Betr': 'STROKES',
                'Fanduel': 'Strokes'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Birdies Or Better': {
            mapping: {
                'PrizePicks': 'Birdies Or Better',
                'Underdog': 'Birdies or Better',
                'Betr': 'BIRDIES_OR_BETTER',
                'Fanduel': 'Birdies or Better'
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'Fanduel'
        },
    },
    'CFB': { // Placeholder for potential future mapping
        'Pass TDs': {
            mapping: {
                'DraftKings': 'Pass TDs',
                'Caesars': 'Total Passing Touchdowns',
                'BetOnline': 'Passing TDs',
                'PropBuilder': 'Passing TDs',
                'BetMGM': 'Passing touchdowns',
                'Fanduel': 'Passing TDs', // Uncommented
                'Betr': 'PASSING_TOUCHDOWNS',
                'Boom': 'PASSING_TOUCHDOWNS',
                'Bovada': 'Total Passing Touchdowns',
                'Chalkboard': 'Pass TD',
                'Dabble': 'passing-touchdowns',
                'ESPNBet': 'Player Total Passing Touchdowns',
                'Fliff': 'Passing Touchdowns',
                'HardRock': 'Passing TDs',
                'Hotstreak': 'passing_touchdowns',
                'ParlayPlay': 'Passing TDs',
                'PrizePicks': 'Pass TDs',
                'Sleeper': 'passing_touchdowns',
                'SportsBattle': 'Pass TD',
                'Underdog': 'Passing TDs',
                'VividPicks': 'PassingTouchdowns',
                'JockMKT': 'passing_touchdowns',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Passing Yards': {
            mapping: {
                'DraftKings': 'Pass Yards',
                'Caesars': 'Total Passing Yards',
                'BetOnline': 'Passing Yards',
                'PropBuilder': 'Passing Yards',
                'BetMGM': 'Passing yards',
                'Fanduel': 'Passing Yds',
                'Betr': 'PASSING_YARDS',
                'Boom': 'PASSING_YARDS',
                'Bovada': 'Total Passing Yards',
                'Chalkboard': 'Pass Yards',
                'Dabble': 'passing-yards',
                'ESPNBet': 'Player Total Passing Yards',
                'Fliff': 'Passing Yards',
                'HardRock': 'Passing Yards',
                'Hotstreak': 'passing_yards',
                'ParlayPlay': 'Passing Yards',
                'PrizePicks': 'Pass Yards',
                'Sleeper': 'passing_yards',
                'SportsBattle': 'Pass Yards',
                'Underdog': 'Passing Yards',
                'VividPicks': 'PassingYards',
                'JockMKT': 'passing_yards',
            },
            modelInfo: { model: "negbin", p: 0.005 },
            preferredSharp: 'DraftKings'
        },
        'Rushing Yards': {
            mapping: {
                'DraftKings': 'Rush Yards',
                'Caesars': 'Total Rushing Yards',
                'BetOnline': 'Rushing Yards',
                'PropBuilder': 'Rushing Yards',
                'BetMGM': 'Rushing yards',
                'Fanduel': 'Rushing Yds',
                'Betr': 'RUSHING_YARDS',
                'Boom': 'RUSHING_YARDS',
                'Bovada': 'Total Rushing Yards',
                'Chalkboard': 'Rush Yards',
                'Dabble': 'rushing-yards',
                'ESPNBet': 'Player Total Rushing Yards',
                'Fliff': 'Rushing Yards',
                'HardRock': 'Rushing Yards',
                'Hotstreak': 'rushing_yards',
                'ParlayPlay': 'Rushing Yards',
                'PrizePicks': 'Rush Yards',
                'Sleeper': 'rushing_yards',
                'Underdog': 'Rushing Yards',
                'VividPicks': 'RushingYards',
            },
            modelInfo: { model: "negbin", p: 0.01 },
            preferredSharp: 'DraftKings'
        },
        'Passing Attempts': {
            mapping: {
                'DraftKings': 'Pass Attempts',
                'Caesars': 'Total Passing Attempts',
                'BetOnline': 'Pass Attempts',
                'PropBuilder': 'Passing Attempts',
                'BetMGM': 'Passing attempts',
                'Fanduel': 'Pass Attempts',
                'Betr': 'PASSING_ATTEMPTS',
                'Boom': 'PASSING_ATTEMPTS',
                'Bovada': 'Total Passing Attempts',
                'Chalkboard': 'Pass Att',
                'Dabble': 'passing-attempts',
                'ESPNBet': 'Player Total Passing Attempts',
                'Fliff': 'Passing Attempts',
                'HardRock': 'Passing Attempts',
                'Hotstreak': 'passing_attempts',
                'ParlayPlay': 'Pass Attempts',
                'PrizePicks': 'Pass Attempts',
                'SportsBattle': 'Pass Attempts',
                'Underdog': 'Passing Attempts',
                'VividPicks': 'Pass Attempts',
                'JockMKT': 'passing_attempts',
            },
            modelInfo: { model: "negbin", p: 0.05 },
            preferredSharp: 'DraftKings'
        },
        'Passing Completions': {
            mapping: {
                'DraftKings': 'Pass Completions',
                'Caesars': 'Total Passing Completions',
                'BetOnline': 'Pass Completions',
                'PropBuilder': 'Passing Completions',
                'Fanduel': 'Pass Completions',
                'BetMGM': 'Pass completions',
                'Betr': 'PASSING_COMPLETIONS',
                'Boom': 'PASSING_COMPLETIONS',
                'Bovada': 'Total Completions',
                'Chalkboard': 'Pass Comp',
                'Dabble': 'passing-completions',
                'ESPNBet': 'Player Total Passing Completions',
                'Fliff': 'Passing Completions',
                'HardRock': 'Passing Completions',
                'Hotstreak': 'pass_completions',
                'ParlayPlay': 'Pass Completions',
                'PrizePicks': 'Pass Completions',
                'Sleeper': 'pass_completions',
                'SportsBattle': 'Pass Comp',
                'Underdog': 'Completions',
                'VividPicks': 'Completions',
                'JockMKT': 'completions',
            },
            modelInfo: { model: "negbin", p: 0.05 },
            preferredSharp: 'DraftKings'
        },
        /*
        'Completion Percentage': {
            mapping: {
                //'DraftKings': '',
                //'Caesars': '',
                'PrizePicks': 'Completion Percentage',
                'Underdog': 'Completion Percentage',
            },
            modelInfo: { model: "negbin", p: 0.005 },
            preferredSharp: 'DraftKings'
        },
        */
       /*
        'Sacks': {
            mapping: {
                'DraftKings': 'Sacks',
                'BetOnline': 'Sacks',
                //'Caesars': '',
                'Chalkboard': 'Sacks',
                'Dabble': 'sacks',
                'ESPNBet': 'Player Total Sacks',
                'Fliff': 'Sacks',
                'HardRock': 'Sacks',
                'PrizePicks': 'Sacks',
                'Sleeper': 'sacks',
                'Underdog': 'Sacks',
                'VividPicks': 'Sacks',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        */
        'Interceptions Thrown': {
            mapping: {
                'DraftKings': 'Interceptions',
                'Caesars': 'Total Interceptions',
                'BetOnline': 'Pass Interceptions',
                'PropBuilder': 'Pass Interceptions',
                'BetMGM': 'Interceptions thrown',
                'Betr': 'PASSING_INTERCEPTIONS',
                'Boom': 'INTERCEPTIONS_THROWN',
                'Bovada': 'Total Interceptions Thrown',
                'Chalkboard': 'Pass INT',
                'Dabble': 'interceptions',
                'ESPNBet': 'Player Total Interceptions Thrown',
                'Fliff': 'Passing Interceptions',
                'HardRock': 'Interceptions',
                'Hotstreak': 'passing_interceptions',
                'ParlayPlay': 'Interception',
                'PrizePicks': 'INT',
                'Sleeper': 'interceptions',
                'SportsBattle': 'Interception',
                'Underdog': 'Interceptions',
                'VividPicks': 'Interceptions Thrown',
                'JockMKT': 'interceptions',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        /*
        'Receiving Targets': {
            mapping: {
                //'DraftKings': '',
                //'Caesars': '',
                'ParlayPlay': 'Receiving Targets',
                'PrizePicks': 'Rec Targets',
                'Underdog': 'Targets',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        */
        'Pass + Rush Yards': {
            mapping: {
                'DraftKings': 'Pass + Rush Yds',
                //'Caesars': '',
                'Fanduel': 'Passing + Rushing Yds',
                'BetMGM': 'Total passing and rushing yards',
                'Fliff': 'Passing + Rushing Yards',
                'Chalkboard': 'Pass + Rush Yds',
                'Dabble': 'passing-yards_rushing-yards',
                'ESPNBet': 'Player Total Passing + Rushing  Yards',
                'Hotstreak': 'passing_plus_rushing_yards',
                'PrizePicks': 'Pass+Rush Yds',
                'Sleeper': 'passing_and_rushing_yards',
                'SportsBattle': 'Pass + Rush Yards',
                'Underdog': 'Pass + Rush Yards',
                'VividPicks': 'Pass + Rush Yards',
                'JockMKT': 'passing_plus_rushing_yards',
            },
            modelInfo: { model: "negbin", p: 0.005 },
            preferredSharp: 'DraftKings'
        },
        'Rushing Attempts': {
            mapping: {
                'DraftKings': 'Rush Attempts',
                'Caesars': 'Total Rushing Attempts',
                'ESPNBet': 'Player Total Rushing Attempts',
                'BetOnline': 'Carries',
                'PropBuilder': 'Carries',
                'Fanduel': 'Rush Attempts',
                'BetMGM': 'Rushing attempts',
                'Betr': 'RUSHING_ATTEMPTS',
                'Boom': 'RUSHING_ATTEMPTS',
                'Bovada': 'Total Rush Attempts',
                'Chalkboard': 'Rush Att',
                'Dabble': 'rushing-attempts',
                'Fliff': 'Rushing Attempts',
                'ParlayPlay': 'Rush Attempts',
                'PrizePicks': 'Rush Attempts',
                'SportsBattle': 'Rush Attempts',
                'Underdog': 'Rushing Attempts',
                'Hotstreak': 'carries',
                'VividPicks': 'Rush Attempts',
                'JockMKT': 'rushing_attempts',
            },
            modelInfo: { model: "negbin", p: 0.1 },
            preferredSharp: 'DraftKings'
        },
        'Solo Tackles': {
            mapping: {
                'DraftKings': 'Solo Tackles',
                //'Caesars': '',
                'BetMGM': 'Tackles',
                'Chalkboard': 'Tackles',
                'Dabble': 'tackles',
                'ESPNBet': 'Player Total Solo Tackles',
                'Fliff': 'Solo Tackles',
                'SportsBattle': 'Solo Tackles',
                'Underdog': 'Solo Tackles',
                'JockMKT': 'tackles',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Assists': {
            mapping: {
                'DraftKings': 'Assists',
                //'Caesars': '',
                'BetMGM': 'Assists',
                'Chalkboard': 'Assists',
                'Dabble': 'assists',
                'ESPNBet': 'Player Total Assisted Tackles',
                'Fliff': 'Assisted Tackles',
                'SportsBattle': 'Assists',
                'Underdog': 'Assists',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        /*
        'Tackles + Assists': {
            mapping: {
                'DraftKings': 'Tackles + Ast',
                'Caesars': 'Total Defensive Tackles + Assists',
                'BetOnline': 'Tackles',
                //'Fanduel': 'Tackles + Assists',
                'BetMGM': 'Total tackles and assists',
                'Chalkboard': 'Tackles + Ast',
                'Dabble': 'assists_tackles',
                'ESPNBet': 'Player Total Defensive Tackles + Assists',
                'Fliff': 'Tackles + Assists',
                'HardRock': 'Tackles + Assists',
                'Hotstreak': 'tackles',
                'ParlayPlay': 'Tackles S+A',
                'PrizePicks': 'Tackles+Ast',
                'SportsBattle': 'Tackles + Assists',
                'Underdog': 'Tackles + Assists',
                'VividPicks': 'Total Tackles',
                'JockMKT': 'tackles_plus_defensive_assists',
            },
            modelInfo: { model: "negbin", p: 0.2 },
            preferredSharp: 'DraftKings'
        },
        */
        'Rush + Rec Yards': {
            mapping: {
                'DraftKings': 'Rush + Rec Yards',
                //'Caesars': '',
                //'Fanduel': 'Rushing + Receiving Yds',
                'BetMGM': 'Total rushing and receiving yards',
                'Betr': 'RUSHING_RECEIVING_YARDS',
                'Bovada': 'Total Rushing & Receiving Yards',
                'Chalkboard': 'Rush + Rec Yds',
                'Dabble': 'receiving-yards_rushing-yards',
                'ESPNBet': 'Player Total Rushing + Receiving Yards',
                'Fanduel': 'Rushing + Receiving Yds',
                'Fliff': 'Rushing + Receiving Yards',
                'PrizePicks': 'Rush+Rec Yds',
                'Sleeper': 'rushing_and_receiving_yards',
                'Underdog': 'Rush + Rec Yards',
                'HardRock': 'Rushing + Receiving Yards',
                'Hotstreak': 'receiving_plus_rushing_yards',
                'SportsBattle': 'Rush + Rec Yards',
                'VividPicks': 'Rush + Rec Yards',
                'JockMKT': 'rushing_plus_receiving_yards',
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        'Receiving Yards': {
            mapping: {
                'DraftKings': 'Receiving Yards',
                'Caesars': 'Total Receiving Yards',
                'BetOnline': 'Receiving Yards',
                'PropBuilder': 'Receiving Yards',
                'Fanduel': 'Receiving Yds',
                'BetMGM': 'Receiving yards',
                'Betr': 'RECEIVING_YARDS',
                'Boom': 'RECEIVING_YARDS',
                'Bovada': 'Total Receiving Yards',
                'Chalkboard': 'Rec Yards',
                'Dabble': 'receiving-yards',
                'ESPNBet': 'Player Total Receiving Yards',
                'Fliff': 'Receiving Yards',
                'HardRock': 'Receiving Yards',
                'Hotstreak': 'receiving_yards',
                'ParlayPlay': 'Receiving Yards',
                'PrizePicks': 'Receiving Yards',
                'Sleeper': 'receiving_yards',
                'SportsBattle': 'Rec Yards',
                'Underdog': 'Receiving Yards',
                'VividPicks': 'ReceivingYards',
                'JockMKT': 'receiving_yards',
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        'Receptions': {
            mapping: {
                'DraftKings': 'Receptions',
                'Caesars': 'Total Receptions',
                'BetOnline': 'Receptions',
                'PropBuilder': 'Receptions',
                'Fanduel': 'Total Receptions',
                'BetMGM': 'Receptions made',
                'Betr': 'CATCHES',
                'Boom': 'RECEPTIONS',
                'Bovada': 'Total Receptions',
                'Chalkboard': 'Rec',
                'Dabble': 'receptions',
                'ESPNBet': 'Player Total Receptions',
                'Fliff': 'Receptions',
                'HardRock': 'Receptions',
                'Hotstreak': 'receptions',
                'ParlayPlay': 'Receptions',
                'PrizePicks': 'Receptions',
                'Sleeper': 'receptions',
                'SportsBattle': 'Reception',
                'Underdog': 'Receptions',
                'VividPicks': 'Receptions',
                'JockMKT': 'receiving_receptions',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },/*
        'Rush + Rec TDs': {
            mapping: {
                //'DraftKings': '',
                //'Caesars': '',
                'Betr': 'RUSHING_RECEIVING_TOUCHDOWNS',
                'PrizePicks': 'Rush+Rec TDs',
                'Underdog': 'Rush + Rec TDs',
            },
            modelInfo: { model: "poisson"},
            preferredSharp: 'DraftKings'
        },
        */
        'FG Made': {
            mapping: {
                'DraftKings': 'FG Made',
                'Caesars': 'Total Made Field Goals',
                'BetMGM': 'Field goals made',
                'Betr': 'FIELD_GOALS_MADE',
                'Chalkboard': 'FG Made',
                'Dabble': 'field-goals-made',
                'ESPNBet': 'Player Total Field Goals Made',
                'Fliff': 'Field Goal Made',
                'HardRock': 'Field Goal Made',
                'Hotstreak': 'field_goals',
                'ParlayPlay': 'FG Made',
                'PrizePicks': 'FG Made',
                'Sleeper': 'field_goal_made',
                'SportsBattle': 'FG Made',
                'Underdog': 'FG Made',
                'VividPicks': 'Field Goals Made',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Kicking Points': {
            mapping: {
                'DraftKings': 'Kicking Pts',
                'Caesars': 'Total Kicking Points',
                'Fanduel': 'Total Kicking Points',
                'BetMGM': 'Kicking points',
                'Bovada': 'Total Kicking Points',
                'Chalkboard': 'Kick Pts',
                'Dabble': 'kicking-points',
                'ESPNBet': 'Player Total Kicking Points',
                'Fliff': 'Kicking Points',
                'HardRock': 'Kicking Points',
                'Hotstreak': 'kicking_points',
                'ParlayPlay': 'Total Points',
                'PrizePicks': 'Kicking Points',
                'Sleeper': 'kicking_points',
                'Underdog': 'Kicking Points',
                'VividPicks': 'Kicking Points',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'XP Made': {
            mapping: {
                'DraftKings': 'PAT Made',
                'Caesars': 'Total Made Extra Points',
                'BetMGM': 'Extra points made',
                'Chalkboard': 'XP Made',
                'Dabble': 'extra-point-made',
                'ESPNBet': 'Player Total Extra Points Made',
                'Fliff': 'Extra Point Made',
                'Underdog': 'XP Made',
            },
            modelInfo: { model: "poisson" },
            preferredSharp: 'DraftKings'
        },
        'Longest Pass Completion': {
            mapping: {
                'DraftKings': 'Longest Completion',
                'Caesars': 'Longest Passing Completion',
                'Fanduel': 'Longest Pass',
                'BetMGM': 'Longest passing completion',
                'Betr': 'LONGEST_PASS',
                'Bovada': 'Longest Pass Completion',
                'Chalkboard': 'Long Comp',
                'Dabble': 'longest-passing-completion',
                'ESPNBet': 'Player Longest Pass Completion',
                'Fliff': 'Longest Passing Completion',
                'HardRock': 'Longest Pass Completion',
                'Hotstreak': 'longest_pass',
                'ParlayPlay': 'Longest Pass',
                'SportsBattle': 'Long. Pass Yard',
                'Pinnacle': 'Longest Completion'
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        'Longest Rush': {
            mapping: {
                'DraftKings': 'Longest Rush',
                'Fanduel': 'Longest Rush',
                'BetMGM': 'Longest rush',
                'Betr': 'LONGEST_RUSH',
                'Bovada': 'Longest Rushing Attempt',
                'Chalkboard': 'Long Rush',
                'Dabble': 'longest-rush',
                'ESPNBet': 'Player Longest Rush',
                'Fliff': 'Longest Rush',
                'Hotstreak': 'longest_rush',
                'ParlayPlay': 'Longest Rush',
                'SportsBattle': 'Long. Rush Yard',
                'VividPicks': 'Longest Rush',
                'Pinnacle': 'Longest Rush'
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        'Longest Reception': {
            mapping: {
                'DraftKings': 'Longest Reception',
                'Caesars': 'Longest Reception',
                'Fanduel': 'Longest Reception',
                'BetMGM': 'Longest reception',
                'Betr': 'LONGEST_CATCH',
                'Bovada': 'Longest Reception',
                'Chalkboard': 'Long Rec',
                'Dabble': 'longest-reception',
                'ESPNBet': 'Player Longest Reception',
                'Fliff': 'Longest Reception',
                'HardRock': 'Longest Reception',
                'Hotstreak': 'longest_reception',
                'ParlayPlay': 'Longest Reception',
                'SportsBattle': 'Long. Rec Yard',
                'VividPicks': 'Longest Reception',
                'JockMKT': 'longest_reception',
                'Pinnacle': 'Longest Reception'
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'DraftKings'
        },
        // Added new 1Q stat blocks
        '1Q Passing Yards': {
            mapping: {
                'Fanduel': '1st Qtr Passing Yds',
                'PrizePicks': '1Q Pass Yards',
                'Underdog': '1Q Passing Yards',
            },
            modelInfo: { model: "negbin", p: 0.005 },
            preferredSharp: 'Fanduel'
        },
        '1Q Rushing Yards': {
            mapping: {
                'Fanduel': '1st Qtr Rushing Yds',
                'PrizePicks': '1Q Rush Yards',
                'Underdog': '1Q Rushing Yards',
            },
            modelInfo: { model: "negbin", p: 0.01 },
            preferredSharp: 'Fanduel'
        },
        '1Q Receiving Yards': {
            mapping: {
                'Fanduel': '1st Qtr Receiving Yds',
                'PrizePicks': '1Q Receiving Yards',
                'Underdog': '1Q Receiving Yards',
            },
            modelInfo: { model: "negbin", p: 0.0075 },
            preferredSharp: 'Fanduel'
        },
        // Add more NFL mappings as needed
    },
};