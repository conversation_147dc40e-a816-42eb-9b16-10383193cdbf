// extractVividStats.js
// Purpose: Fetches VividPicks projections and lists unique stat types per league.

// Adjust the path if this script is not in the project root directory
const { fetchVivid } = require('../providers/VividPicksProvider');

async function getUniqueStatsByLeague() {
    console.log("Starting script: Fetching VividPicks projections...");

    let projections;
    try {
        projections = await fetchVivid();
    } catch (error) {
        console.error("Fatal error fetching projections:", error);
        return; // Exit if fetch fails fundamentally
    }

    if (!projections || !Array.isArray(projections) || projections.length === 0) {
        console.log("No projections were returned from fetchVivid. Exiting.");
        return;
    }

    console.log(`Successfully fetched ${projections.length} projections. Processing...`);

    // Use an object to hold Sets of stat types for each league
    const statsByLeague = {};

    for (const proj of projections) {
        // Basic validation for required fields
        if (!proj || !proj.league || typeof proj.league !== 'string' || !proj.stat_type || typeof proj.stat_type !== 'string') {
            console.warn("Skipping projection with missing/invalid league or stat_type:", proj);
            continue;
        }

        const league = proj.league.toUpperCase(); // Normalize league names to uppercase
        const statType = proj.stat_type;

        // If the league hasn't been seen yet, initialize it with a new Set
        if (!statsByLeague[league]) {
            statsByLeague[league] = new Set();
        }

        // Add the stat type to the Set for this league (duplicates are automatically handled)
        statsByLeague[league].add(statType);
    }

    // Convert the Sets to sorted arrays for cleaner output
    const sortedStatsByLeague = {};
    Object.keys(statsByLeague)
        .sort() // Sort leagues alphabetically
        .forEach(league => {
            sortedStatsByLeague[league] = Array.from(statsByLeague[league]).sort(); // Sort stat types alphabetically
        });

    console.log("\n--- Unique Stat Types Found (Grouped by League) ---");
    console.log(JSON.stringify(sortedStatsByLeague, null, 2)); // Pretty print the JSON
    console.log("\nScript finished.");
}

// Run the function
getUniqueStatsByLeague();
