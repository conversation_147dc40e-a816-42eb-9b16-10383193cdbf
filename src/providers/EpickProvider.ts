import { fetch } from 'undici';
import { Projection } from '../utils/types';
import logger from '../utils/logger';

// Base URL without league param
const EPICK_API_URL_BASE = 'https://sportsdata.dev.epickfantasy.com/api/projections/ui?state_or_territory=oregon&mode=50%2F50&sort_by_prop_aggregation=false&minimize=true&exclude_past_start_times=true';
const LEAGUES_TO_FETCH = ['nba', 'wnba', 'nhl', 'mlb']; // Leagues for concurrent fetch

// Helper function to extract the core stat type from the prop ID
function extractStatType(propId: string): string | null {
    const parts = propId.split(':');
    if (parts.length >= 4) {
        return parts.slice(3, -1).join(':');
    }
    return null;
}

// Helper function to map Epick league codes to standard uppercase codes
function mapLeague(league: string): string {
    return league.toUpperCase();
}

// Fetch function modified for concurrent league requests
export async function fetchEpick(): Promise<Projection[]> {
    logger("Fetching projections concurrently from Epick for leagues: " + LEAGUES_TO_FETCH.join(', ').toUpperCase());
    const allProjections: Projection[] = [];
    let totalProcessedCount = 0;

    // Create an array of API call promises
    const apiPromises = LEAGUES_TO_FETCH.map(leagueCode => {
        const apiUrl = `${EPICK_API_URL_BASE}&league=${leagueCode}`;
        logger(`Preparing Epick request for league: ${leagueCode.toUpperCase()}`);
        return fetch(apiUrl, {
            signal: AbortSignal.timeout(30000), // 30 second timeout
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        }).then(async response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        }).catch(error => {
            // Attach leagueCode to the error for easier identification
            error.leagueCode = leagueCode;
            throw error; // Re-throw to be caught by Promise.allSettled as rejected
        });
    });

    // Execute all promises concurrently and wait for all to settle
    const results = await Promise.allSettled(apiPromises);

    logger("Processing Epick concurrent fetch results...");

    // Process results
    results.forEach((result, index) => {
        const leagueCode = LEAGUES_TO_FETCH[index]; // Get league code corresponding to the result

        if (result.status === 'fulfilled') {
            const data = result.value;
            logger(`Epick: Successfully fetched data for league ${leagueCode.toUpperCase()}.`);

            // Process successful response data
            try {
                // Use data.projections based on last successful run's structure
                const responseData = data as any; // Type assertion for API response
                const epickProjections = responseData.projections;
                const events = responseData.events;
                const teams = responseData.teams;

                if (!epickProjections || typeof epickProjections !== 'object' || Object.keys(epickProjections).length === 0) {
                    logger(`Epick: No projections found or invalid format in response for league ${leagueCode.toUpperCase()}.`);
                    return; // Use return to skip processing this league's data
                }
                if (!events || typeof events !== 'object') {
                    logger(`Epick: No events found or invalid format for league ${leagueCode.toUpperCase()}.`);
                    return;
                }
                if (!teams || typeof teams !== 'object') {
                    logger(`Epick: No teams found or invalid format for league ${leagueCode.toUpperCase()}.`);
                    return;
                }

                let leagueProjectionCount = 0;
                for (const projId in epickProjections) {
                    const projection = epickProjections[projId];

                    // Basic validation (Copied from previous working version)
                    if (!projection || projection.status !== 'PROJECTED' || projection.expired || projection.invalidated) {
                         // logger(`Epick Skip [${leagueCode.toUpperCase()}]: Invalid projection status/state for ID ${projId}`);
                        continue;
                    }

                    const eventId = projection.event_id;
                    const event = events[eventId];
                    if (!event || event.status !== 'SCHEDULED' || event.expired || event.invalidated) {
                        // logger(`Epick Skip [${leagueCode.toUpperCase()}]: Invalid event status/state for ID ${eventId}`);
                        continue;
                    }

                    const homeTeam = teams[event.team_home];
                    const awayTeam = teams[event.team_away];
                    if (!homeTeam || !awayTeam) {
                        // logger(`Epick Skip [${leagueCode.toUpperCase()}]: Missing team data for event ${eventId}`);
                        continue;
                    }

                    const statTypeRaw = extractStatType(projection.prop);
                    if (!statTypeRaw) {
                        // logger(`Epick Skip [${leagueCode.toUpperCase()}]: Could not extract stat type from prop ${projection.prop}`);
                        continue;
                    }

                    const league = mapLeague(projection.league);
                    const matchup = `${awayTeam.name_std} vs. ${homeTeam.name_std}`;
                    const startTime = new Date(projection.iso_event_datetime);

                    if (startTime.getTime() < Date.now()) {
                        // logger(`Epick Skip [${leagueCode.toUpperCase()}]: Start time in past for ${projection.subject_std}`);
                        continue;
                    }

                    const formattedProjection: Projection = {
                        proj_id: projection.id, // Use projection.id
                        league: league,
                        player_name: projection.subject_std,
                        stat_type: statTypeRaw,
                        line: projection.line_value, // Use projection.line_value
                        matchup: matchup,
                        start_time: startTime,
                        source: 'Epick', // Ensure source matches expectation
                        over_odds_american: '-125',
                        under_odds_american: '-125',
                        over_odds_decimal: 1.8,
                        under_odds_decimal: 1.8,
                        is_one_way: false
                    };

                    allProjections.push(formattedProjection);
                    leagueProjectionCount++;
                }
                logger(`Epick: Successfully processed ${leagueProjectionCount} projections for ${leagueCode.toUpperCase()}.`);
                totalProcessedCount += leagueProjectionCount;
            } catch (processingError: any) {
                logger(`Epick: Error processing data for league ${leagueCode.toUpperCase()} - ${processingError.message}`);
            }

        } else { // result.status === 'rejected'
            const error = result.reason;
            const failedLeagueCode = error.leagueCode || leagueCode; // Get league code from attached property or index
            logger(`Epick: Error fetching data for league ${failedLeagueCode.toUpperCase()} - ${error.message}`);
            logger(`Epick [${failedLeagueCode.toUpperCase()}]: Error details: ${error}`);
        }
    });

    logger(`Epick: Successfully processed a total of ${totalProcessedCount} projections across all leagues.`);
    logger(`Epick: Returning ${allProjections.length} projections from fetchEpick.`);
    return allProjections;
}