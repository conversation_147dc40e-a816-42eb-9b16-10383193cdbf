// VividPicksProvider.js

const { fetch, ProxyAgent } = require('undici');
const { getProxy } = require('../utils/proxy/index');

// --- Helper Function (copied from UnderdogProvider.js) ---
function decimalToAmerican(decimal) {
    if (!decimal || decimal < 1) return null; // Handle null/invalid decimal odds
    if (decimal >= 2) {
        return `+${Math.round((decimal - 1) * 100)}`;
    } else {
        // Ensure we don't divide by zero or have issues with decimal === 1
        if (decimal === 1.0) return null; // Or handle as Even money if applicable
        return `-${Math.round(100 / (decimal - 1))}`;
    }
}
// --- End Helper Function ---

const fetchVivid = async () => {
    let projections = [];
    const BASE_DECIMAL_ODDS = 1.85; // Corresponds to -118 American

    console.log("[VividPicks DEBUG] Starting fetchVivid function.");

    try {
        const proxyUrl = await getProxy();
        const proxyAgent = proxyUrl ? new ProxyAgent(proxyUrl) : undefined;
        console.log(`[VividPicks DEBUG] Using proxy: ${proxyUrl ? proxyUrl.substring(0, 40)+'...' : 'No'}`);

        const payload = {
            "league": ["NHL", "SOCCER", "MLB", "WNBA", "NFL", "CFB", "NBA", "CBB", "CS2", "LOL"], // Request all relevant leagues
            "matchUp": false
        };

        const headers = {
            "host": "api.betcha.one",
            "accept": "application/json",
            "content-type": "application/json",
            "betcha-version": "ios(18.3.2)/264/bea3a9ab", // Keep reasonably updated
            "betcha-timezone": "America/New_York",
            "accept-language": "en-US,en;q=0.9",
            "user-agent": "VividPicks/264 CFNetwork/1496.0.7 Darwin/23.5.0", // Example: Use a recent valid user agent
            "x-px-mobile-sdk-version": "3.0.3",
            "x-px-os-version": "17.5.1",
            "x-px-os": "iOS",
            "accept-encoding": "gzip, deflate, br",
            "cache-control": "no-cache",
            // --- Dynamic headers are CRUCIAL and must be valid ---
            // "authorization": "Bearer <VALID_JWT_TOKEN>",
            // "x-px-authorization": "<VALID_PERIMETERX_TOKEN>",
            // "cookie": "<VALID_SESSION_COOKIES>",
            // ... other required dynamic headers ...
        };
        // console.log("[VividPicks DEBUG] Request Headers:", JSON.stringify(headers, null, 2)); // Sensitive - uncomment only locally if needed

        console.log("[VividPicks DEBUG] Sending POST request to API endpoint...");
        const response = await fetch('https://api.betcha.one/v1/game/activePlayersForLeagueBoard', {
            method: 'POST',
            body: JSON.stringify(payload),
            headers: headers,
            dispatcher: proxyAgent,
            // Increase timeout if necessary, e.g., 30 seconds
            // signal: AbortSignal.timeout(30000)
        });

        console.log(`[VividPicks DEBUG] API Response Status: ${response.status} ${response.statusText}`);

        if (!response.ok) {
            console.error(`[VividPicks ERROR] API request failed. Status: ${response.status}`);
            try {
                const errorBody = await response.text();
                console.error("[VividPicks ERROR] Response Body:", errorBody.substring(0, 1000) + (errorBody.length > 1000 ? '...' : ''));
            } catch (bodyError) {
                console.error("[VividPicks ERROR] Could not read error response body:", bodyError);
            }
            return [];
        }

        let responseBody;
        try {
            responseBody = await response.json();
            console.log("[VividPicks DEBUG] Successfully parsed JSON response.");
        } catch (parseError) {
            console.error("[VividPicks ERROR] Failed to parse JSON response:", parseError);
            // Attempt to log raw text if parsing fails
            try {
                // Note: response body might be consumed. Need to handle this properly if body needed elsewhere.
                // For debugging, getting text after json fail might work if cloned or handled carefully.
                // Let's assume for now we just log the parse error. If needed, add response cloning.
                // const textBody = await response.text(); // Re-reading might fail here
                // console.error("[VividPicks ERROR] Raw response body (on JSON parse fail):", textBody.substring(0, 500) + '...');
            } catch (textError) {
                console.error("[VividPicks ERROR] Could not read text response body after JSON parse fail:", textError);
            }
            return [];
        }

        // --- Start of MODIFIED processing logic ---
        // Check if the response body and playerData array exist and are valid
        if (!responseBody || !responseBody.playerData || !Array.isArray(responseBody.playerData)) {
             console.error('[VividPicks ERROR] Response body is missing or "playerData" is not a valid array.');
             console.log('[VividPicks DEBUG] Received Response Body Structure:', JSON.stringify(responseBody, null, 2)); // Log structure
             return [];
        }

        console.log(`[VividPicks DEBUG] Found 'playerData' array with ${responseBody.playerData.length} players.`);

        // Iterate directly over the playerData array
        projections = responseBody.playerData.flatMap((player, playerIndex) => {
            // Add null checks for player properties
            if (!player || typeof player !== 'object') {
                console.warn(`[VividPicks WARN] Skipping invalid player object at index ${playerIndex}.`);
                return []; // Skip null or invalid player object
            }
             if (!player.name) {
                 console.warn(`[VividPicks WARN] Skipping player at index ${playerIndex} due to missing name.`);
                 return [];
             }
             // Use player._id or index for logging
             const playerId = player._id || `player_${playerIndex}`;
             // console.log(`[VividPicks DEBUG] Processing Player ${playerIndex + 1}/${responseBody.playerData.length}: ${player.name} (ID: ${playerId})`); // Verbose

             if (!player.visiblePlayerProps || !Array.isArray(player.visiblePlayerProps)) {
                 console.log(`[VividPicks DEBUG] Player ${player.name} (ID: ${playerId}) has no 'visiblePlayerProps' array. Skipping.`);
                 return []; // Skip player if visible props are missing
             }
             // Optionally check for playerProps if needed for the line value (depends on final data structure)
             if (!player.playerProps || typeof player.playerProps !== 'object') {
                  console.log(`[VividPicks DEBUG] Player ${player.name} (ID: ${playerId}) has no 'playerProps' object. Skipping.`);
                  return []; // Skip if playerProps object is missing (if you rely on it for line values)
             }


            // Extract player-level info
            const playerName = player.name;
            const teamAbv = player.abvTeamName || 'UNKNOWN';
            let league = player.league || 'UNKNOWN'; // Get league from player
            const matchup = player.gameInfo || 'Unknown Matchup'; // Get matchup from player
            const startTime = player.gameDate ? new Date(player.gameDate) : new Date(0); // Get start time from player


            // Iterate through the visible props for this player
            return player.visiblePlayerProps
                 .map((prop, propIndex) => {
                     if (!prop || typeof prop !== 'object') {
                         console.warn(`[VividPicks WARN] Skipping invalid prop object at index ${propIndex} for player ${playerName} (ID: ${playerId}).`);
                         return null;
                     }

                     const statName = prop.p;
                     const isNotRated = prop.nr === true;
                      // Check visibility if necessary: const isVisible = prop.v === true;

                     if (!statName) {
                         console.warn(`[VividPicks WARN] Skipping prop at index ${propIndex} for player ${playerName} due to missing 'p' (stat name).`);
                         return null;
                     }
                     if (isNotRated) {
                         // console.log(`[VividPicks DEBUG]     Skipping 'Not Rated' prop '${statName}' for ${playerName}.`); // Verbose
                         return null;
                     }

                     // --- Check for multiplier and calculate odds ---
                     let over_odds_decimal = BASE_DECIMAL_ODDS;
                     let under_odds_decimal = BASE_DECIMAL_ODDS;
                     let over_odds_american = decimalToAmerican(over_odds_decimal);
                     let under_odds_american = decimalToAmerican(under_odds_decimal);
                     let is_over_only = false; // Flag for over-only bets

                     const configProp = player.configPlayerProps ? player.configPlayerProps[statName] : null;

                     if (configProp && configProp.multiplier !== undefined && configProp.multiplier !== null) {
                         is_over_only = true; // Assumption: multiplier means over-only
                         const multiplier = parseFloat(configProp.multiplier) || 1.0; // Default to 1 if parsing fails
                         over_odds_decimal = multiplier * BASE_DECIMAL_ODDS;
                         over_odds_american = decimalToAmerican(over_odds_decimal);

                         // Set under odds to null for over-only props
                         under_odds_decimal = null;
                         under_odds_american = null;

                         // Optional: Log the multiplier application
                         // console.log(`[VividPicks DEBUG] Applied multiplier ${multiplier} to ${playerName} - ${statName}. New Over Odds: ${over_odds_decimal.toFixed(2)} (${over_odds_american})`);
                     }
                     // --- End odds calculation ---

                     let lineValue = player.playerProps[statName];

                     // Apply Free Square override if applicable - Check if freeSquareProp exists on player object
                     if (player.freeSquareProp && player.freeSquareProp.propName === statName && player.freeSquareProp.propValue !== undefined) {
                         const originalLine = lineValue;
                         lineValue = player.freeSquareProp.propValue;
                         console.log(`[VividPicks INFO] Applied free square value ${lineValue} (original: ${originalLine}) for ${playerName} - ${statName}`);
                     }

                     if (lineValue === undefined || lineValue === null) {
                          console.warn(`[VividPicks WARN] Skipping prop '${statName}' for player ${playerName} (ID: ${playerId}) because line value is missing/null in playerProps.`);
                          // If using prop.val (Method 1), adjust log message accordingly
                         return null;
                     }

                     // --- League adjustments (Keep this logic) ---
                    if (league === 'MLB') {
                           if (statName && (statName.includes('Hrs Hit') || statName === "Longest Hr Distance (ft)" || statName === "Max Hr Exit Velocity (mph)")) {
                           league = 'HRDERBY';
                           }
                    }
                    if (league === 'CBB') {
                        league = 'NCAAB';
                    }
                         if (matchup.includes('Futures')) {
                         league += 'SZN';
                     }
                     // --- End League adjustments ---

                     const playerNameClean = playerName.replace(/\s/g, '_');
                     const propNameClean = statName.replace(/\s/g, '_');
                     const projIdBase = `${playerNameClean}_${propNameClean}`;

                     const finalProjection = {
                         proj_id: projIdBase,
                         league: league,
                         team: teamAbv,
                         player_name: playerName,
                         stat_type: statName,
                         line: lineValue,
                         over_odds_american: over_odds_american,
                         under_odds_american: under_odds_american,
                         over_odds_decimal: over_odds_decimal,
                         under_odds_decimal: under_odds_decimal,
                         is_over_only: is_over_only,
                         matchup: matchup,
                         start_time: startTime,
                         source: 'VividPicks',
                     };
                     // console.log(`[VividPicks DEBUG]     Generated projection for ${playerName} - ${statName}: ${lineValue}`); // Verbose
                     return finalProjection;
                 })
                 .filter(p => p !== null); // Filter out null props
        }).flat(); // Flatten results from all players

        console.log(`[VividPicks DEBUG] Finished processing all players. Total projections extracted: ${projections.length}`);
        // --- End of MODIFIED processing logic ---

    } catch (error) {
        console.error('[VividPicks FATAL] An error occurred during the fetchVivid operation:', error);
        // Ensure projections is an empty array in case of a major error
        projections = [];
    }

    console.log(`[VividPicks DEBUG] Returning ${projections.length} projections from fetchVivid.`);
    return projections;
};

module.exports = { fetchVivid };