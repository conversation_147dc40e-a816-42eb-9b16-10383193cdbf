import os
import sys
import json
import time
import random
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Op<PERSON>, <PERSON><PERSON>, List

from camoufox.sync_api import Camoufox

# ---------------------------------------------------------------------------
# logging
# ---------------------------------------------------------------------------
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# ---------------------------------------------------------------------------
# constants & paths
# ---------------------------------------------------------------------------
PROXY_DIR = Path(__file__).resolve().parent.parent / "proxies"
PROXY_FILE_DC = PROXY_DIR / "proxies.txt"
PROXY_FILE_PP = PROXY_DIR / "prizepicks_proxies.txt"
FAILED_FILE = PROXY_DIR / ".failed_proxies.txt"
HISTORY_FILE = PROXY_DIR / ".subnet_history.json"
PROXY_DIR.mkdir(parents=True, exist_ok=True)

PERSIST_PATH = Path("/tmp/pp_ctx")  # chromium profile for persistent context
PERSIST_PATH.mkdir(parents=True, exist_ok=True)

URL = "https://api.prizepicks.com/projections?per_page=9999"

CAMOU_OPTS_BASE = {
    "headless": "virtual",   # fastest when running under xvfb
    "block_images": True,
    "block_webrtc": True,
    "humanize": False,
    "os": ["linux"],
    "persistent_context": True,
    "user_data_dir": str(PERSIST_PATH),
}

# ---------------------------------------------------------------------------
# util helpers
# ---------------------------------------------------------------------------

def _load_json(path: Path) -> dict:
    if path.exists():
        try:
            return json.loads(path.read_text())
        except Exception:
            pass
    return {}

def _save_json(path: Path, data: dict):
    try:
        path.write_text(json.dumps(data, indent=2))
    except Exception as exc:
        logging.error("Failed to write %s: %s", path, exc)

# ---------------------------------------------------------------------------
# global browser context (persistent between cycles)
# ---------------------------------------------------------------------------
_CTX = None  # type: Optional[object]
_CTX_PROXY_LINE = None  # type: Optional[str]


def _init_context(proxy_conf: dict, proxy_line: str):
    """(re)create persistent Camoufox context under given proxy."""
    global _CTX, _CTX_PROXY_LINE
    # tear down old context if any
    if _CTX is not None:
        try:
            _CTX.close()
        except Exception:
            pass
        _CTX = None
    opts = dict(CAMOU_OPTS_BASE, proxy=proxy_conf)
    # Camoufox is a context‑manager – manually enter to keep it alive
    _CTX = Camoufox(**opts).__enter__()
    _CTX_PROXY_LINE = proxy_line
    logging.info("Launched persistent Camoufox context with proxy %s", proxy_line.split("@")[-1])


def _get_context(proxy_conf: dict, proxy_line: str):
    global _CTX, _CTX_PROXY_LINE
    if _CTX is None or _CTX_PROXY_LINE != proxy_line:
        _init_context(proxy_conf, proxy_line)
    return _CTX

# ---------------------------------------------------------------------------
# proxy rotation (same logic as before – lightly refactored)
# ---------------------------------------------------------------------------

def get_subnet_based_proxy(prefer_residential: bool = True, residential_ratio: float = 0.3):
    """Return (host, port, user, pwd, full_line, type) or None."""
    proxy_file = PROXY_FILE_PP if PROXY_FILE_PP.exists() else PROXY_FILE_DC
    history = _load_json(HISTORY_FILE)
    failed = set(ln.strip() for ln in FAILED_FILE.read_text().splitlines()) if FAILED_FILE.exists() else set()

    resi: List[str] = []
    subnets: dict[str, List[str]] = {}

    for ln in proxy_file.read_text().splitlines():
        ln = ln.strip()
        if not ln or ln in failed:
            continue
        ip = ln.split(":")[0]
        if any(t in ln.lower() for t in ("resi.", "residential", "-session-")):
            resi.append(ln)
        else:
            subnets.setdefault(".".join(ip.split(".")[:3]), []).append(ln)

    now = datetime.utcnow()
    use_res = resi and prefer_residential and random.random() < residential_ratio
    if "perimeterx" in history.get("last_error", "").lower():
        use_res = True

    def pick_res() -> Tuple[str, str]:
        return random.choice(resi), "residential"

    if use_res and resi:
        line, ptype = pick_res()
    else:
        cool_ok = []
        for subnet, lines in subnets.items():
            last = history.get(subnet, {}).get("last_used")
            cool = history.get(subnet, {}).get("cooling_period", 300)
            if last and now < datetime.fromisoformat(last) + timedelta(seconds=cool):
                continue
            cool_ok.append(subnet)
        if not cool_ok and resi:
            line, ptype = pick_res()
        elif cool_ok:
            subnet = random.choice(cool_ok)
            line, ptype = random.choice(subnets[subnet]), "datacenter"
        else:
            logging.error("No proxies available after filtering")
            return None

    host, port, user, pwd = line.split(":")[:4]
    key = "residential" if ptype.startswith("res") else ".".join(host.split(".")[:3])
    entry = history.setdefault(key, {"use_count": 0, "cooling_period": 300, "failure_count": 0})
    entry["use_count"] += 1
    entry["last_used"] = now.isoformat()
    _save_json(HISTORY_FILE, history)
    return host, port, user, pwd, line, ptype


def mark_proxy_as_failed(line: str, err: str = ""):
    if not line:
        return
    FAILED_FILE.write_text((FAILED_FILE.read_text() if FAILED_FILE.exists() else "") + f"{line}\n")
    hist = _load_json(HISTORY_FILE)
    key = "residential" if any(t in line.lower() for t in ("resi.", "residential", "-session-")) else ".".join(line.split(":")[0].split(".")[:3])
    ent = hist.setdefault(key, {"cooling_period": 300, "failure_count": 0})
    ent["failure_count"] += 1
    ent["cooling_period"] = min(ent["cooling_period"] * 2, 6 * 3600)
    hist["last_error"] = err
    _save_json(HISTORY_FILE, hist)

# ---------------------------------------------------------------------------
# fetch helper
# ---------------------------------------------------------------------------

def fetch_with_context(proxy_conf: dict, proxy_line: str) -> dict:
    ctx = _get_context(proxy_conf, proxy_line)
    page = ctx.new_page()
    try:
        resp = page.goto(URL, wait_until="domcontentloaded", timeout=20000)
        if not resp.ok:
            raise RuntimeError(f"HTTP {resp.status}")
        txt = page.content()
        s, e = txt.find("{"), txt.rfind("}") + 1
        if s < 0 or e <= s:
            raise ValueError("No JSON found in response")
        return json.loads(txt[s:e])
    finally:
        page.close()

# ---------------------------------------------------------------------------
# public API – retry w/proxy rotation
# ---------------------------------------------------------------------------

def get_prizepicks_data(max_attempts: int = 3):
    for attempt in range(1, max_attempts + 1):
        res_ratio = 0.0 if attempt == 1 else 1.0
        info = get_subnet_based_proxy(True, res_ratio)
        if not info:
            time.sleep(2)
            continue
        host, port, user, pwd, line, ptype = info
        proxy_conf = {"server": f"http://{host}:{port}", "username": user, "password": pwd}
        logging.info("Attempt %d/%d using %s proxy", attempt, max_attempts, ptype)
        try:
            return fetch_with_context(proxy_conf, line)
        except Exception as exc:
            logging.warning("Fetch error: %s", exc)
            mark_proxy_as_failed(line, str(exc))
            time.sleep(2)
            continue
    logging.error("All attempts failed")
    return None

# ---------------------------------------------------------------------------
# CLI wrapper with 30‑second cache
# ---------------------------------------------------------------------------
if __name__ == "__main__":
    CACHE = Path(__file__).with_suffix(".cache.json")
    if CACHE.exists() and time.time() - CACHE.stat().st_mtime < 30:
        print("JSON_OUTPUT_START")
        print(CACHE.read_text())
        print("JSON_OUTPUT_END")
        sys.exit(0)

    logging.info("Fetching PrizePicks data…")
    data = get_prizepicks_data()
    if data:
        CACHE.write_text(json.dumps(data))
        print("JSON_OUTPUT_START")
        print(json.dumps(data))
        print("JSON_OUTPUT_END")
        sys.exit(0)
    sys.exit(1)
