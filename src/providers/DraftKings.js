/**
 * @file DraftKings.js
 * @description
 * This file defines the provider function for DraftKings. It uses the "sharp"
 * DraftKings projections already fetched (or fetched on-demand) by the main
 * system to produce a final array of projections suitable for storing.
 * 
 * Key responsibilities:
 * - Accept external sharp projections (optional).
 * - Filter them by "source === 'DraftKings'".
 * - Remap/augment source if needed.
 * - Return the final array of projections.
 * 
 * Usage/Notes:
 * - Similar to BetOnlineProvider.js, we rely on existing raw "sharp" data
 *   in the system. If none is passed in, we import and use fetchSharpProjections.
 * - We specifically filter for "DraftKings" among the "sharp" data.
 * - If you want to rename the source (like BetOnline -> "PropBuilder"), 
 *   update the `.map()` step accordingly.
 */

const { fetchSharpProjections } = require('../utils/ProjectionUtility');

/**
 * @function fetchDraftKings
 * @async
 * @param {Array} [sharpProjections] - Optional array of already fetched "sharp" projections
 * @returns {Promise<Array>} Final array of projections matching the "DraftKings" source
 * 
 * @description
 * - Fetches the "sharp" projections if none are provided.
 * - Filters for `source === 'DraftKings'`.
 * - Optionally remaps the source field if desired.
 * - Returns the final array of processed DraftKings projections.
 * 
 * @example
 * const projections = await fetchDraftKings();
 * console.log(projections);
 */
async function fetchDraftKings(sharpProjections) {
  // If no sharpProjections are provided, fetch them
  const projections = sharpProjections || await fetchSharpProjections();

  // Filter out any that are not from DraftKings
  const draftKingsProjections = projections.filter(
    (proj) => proj.source === 'DraftKings'
  );

  // Optionally rename or augment the source. Here we keep it the same:
  const finalProjections = draftKingsProjections.map((proj) => ({
    ...proj,
    // If you want to rename the source, uncomment the next line:
    // source: 'DraftKingsProvider',
    source: 'DraftKings'
  }));

  return finalProjections;
}

module.exports = {
  fetchDraftKings
};