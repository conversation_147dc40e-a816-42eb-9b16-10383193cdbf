// Import necessary modules
const ChalkboardAPI = require("../api/chalkboard-api/chalkboard-api");

class ChalkboardProvider {
  constructor() {
    this.chalkboardAPI = new ChalkboardAPI();
    // Define the game types you're interested in
    this.gameTypes = ["nba", "wnba", "nhl", "ncaamb"];
  }

  /**
   * Authenticates with the Chalkboard API.
   */
  async authenticate() {
    try {
      // Replace 'yourUsername' and 'yourPassword' with actual credentials
      await this.chalkboardAPI.authenticate('fatstacks2023', 'Testing@123');
      //console.log('Successfully authenticated with Chalkboard API.');
    } catch (error) {
      console.error('Authentication failed:', error);
      throw error;
    }
  }

  /**
   * Fetches games for specified types and extracts projections.
   */
  async fetchChalkboard() {
    let projections = [];
    try {
      // Ensure authenticated
      if (!this.chalkboardAPI.isAuthenticated()) {
        await this.authenticate();
      }
  
      for (const type of this.gameTypes) {
        // Fetch games for the current type
        const response = await this.chalkboardAPI.searchGames(type); // Assuming this returns an object
  
        if (response && Array.isArray(response.list) && response.list.length > 0) {
          //console.log(`Fetched ${response.list.length} games for type: ${type}`);
  
          // Now, iterate over the list of games in the response
          for (const game of response.list) {
            const projection = this.extractProjectionFromGame(game);
            if (projection) {
              projections.push(projection);
            }
          }
        } else {
          console.log(`No games found or invalid response for type: ${type}`);
        }
      }
    } catch (error) {
      console.error('Failed to fetch and extract projections:', error);
    }
    return projections;
  }

  /**
   * Extracts projection from a single game object using the provided logic.
   */
  extractProjectionFromGame(game) {
    const leagueNames = new Set(["NFL", "NBA", "WNBA", "NHL", "NCAAMB"]); // Adjust as necessary
    if (leagueNames.has(game.leagueName.toUpperCase())) {
      let formattedLeague = game.leagueName.toUpperCase();
      if (formattedLeague === 'NBA' && game.statisticName.includes('1Q')) {
        formattedLeague = 'NBA1Q';
      } else if (formattedLeague === 'NCAAMB') {
        formattedLeague = 'CBB';
      }

      const teamType = game.player.teamId === game.home.id ? 'home' : 'away';
      const opponentType = teamType === 'home' ? 'away' : 'home';
      const startTime = new Date(game.scheduled);

      const overOdds = parseFloat((game.markets.over.odds * 0.89).toFixed(2));
      const underOdds = parseFloat((game.markets.under.odds * 0.89).toFixed(2));

      const overMulti = (game.markets.over.odds * 0.89).toFixed(2) + 'x';
      const underMulti = (game.markets.under.odds * 0.89).toFixed(2) + 'x';

      const overOddsAmerican = this.decimalToAmerican(overOdds);
      const underOddsAmerican = this.decimalToAmerican(underOdds);

      return {
        proj_id: game.key,
        league: formattedLeague,
        player_name: game.player.full_name,
        stat_type: game.statisticName,
        line: game.line,
        over_odds_decimal: overOdds,
        under_odds_decimal: underOdds,
        over_multi: overMulti,
        under_multi: underMulti,
        over_odds_american: overOddsAmerican,
        under_odds_american: underOddsAmerican,
        matchup: game[teamType].team_alias + " vs. " + game[opponentType].team_alias,
        start_time: startTime,
        source: 'Chalkboard'
      };
    }
    return null;
  }

  /**
   * Converts decimal odds to American odds format.
   */
  decimalToAmerican(decimalOdds) {
    if (decimalOdds >= 2) {
      return '+' + Math.round((decimalOdds - 1) * 100);
    } else {
      return '-' + Math.round(100 / (decimalOdds - 1));
    }
  }
}

module.exports = ChalkboardProvider;
