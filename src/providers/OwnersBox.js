const axios = require('axios');
const HttpsProxyAgent = require('https-proxy-agent');
const { getProxy, reportProxySuccess, reportProxyFailure } = require('../utils/proxy');

// Define market type IDs for NBA
const NBA_MARKET_TYPES = [
  { id: 3019425960, name: "<PERSON>" },
  { id: 3038925011, name: "Assist<PERSON>" },
  { id: 3045965290, name: "<PERSON>bound<PERSON>" },
  { id: 3045965291, name: "<PERSON>ts + <PERSON><PERSON> + <PERSON><PERSON>" },
  { id: 3047165313, name: "Blocked Shots" },
  { id: 3047825842, name: "Steals + Blocks" },
  { id: 3060495301, name: "<PERSON><PERSON><PERSON>" },
  { id: 3069845185, name: "3-Pointers Made" },
  { id: 3072025164, name: "Turnovers" },
  { id: 4655746492923904, name: "<PERSON><PERSON> + <PERSON><PERSON>" },
  { id: 4877662218616832, name: "<PERSON><PERSON> + <PERSON><PERSON>" },
  { id: 5005164664586240, name: "<PERSON><PERSON> + As<PERSON>" }
];

// Define market type IDs for NHL
const NHL_MARKET_TYPES = [
  { id: 3064945167, name: "Points" },
  { id: 3072535163, name: "Goals Against" },
  { id: 3074085091, name: "Blocked Shots" },
  { id: 3077805109, name: "Assists" },
  { id: 3077905021, name: "Goalie Saves" },
  { id: 3083935076, name: "Shots On Goal" },
  { id: 3084105032, name: "Goals" },
  { id: 5105607434567680, name: "Power Play Points" },
  { id: 5127099937456128, name: "Faceoffs Won" },
  { id: 5210757092343808, name: "Hits" },
  { id: 5237769194438656, name: "Time On Ice (In Reg)" }
];

// DEFINE MLB MARKET TYPE IDs - YOU NEED TO FIND THESE ACTUAL IDs
const MLB_MARKET_TYPES = [
  { id: 3170065030, name: "Pitcher Strikeouts" },
  { id: 3170085013, name: "Total Bases" },
  { id: 3161705122, name: "Hits + Runs + RBIs" },
  { id: 3143465008, name: "Singles" },
  { id: 3123285640, name: "Pitcher Outs" },
  { id: 3137365010, name: "Walks" },
  { id: 3143465009, name: "Hits" },
  { id: 3129075577, name: "RBIs" },
  { id: 3160215008, name: "Runs" },
  { id: 3215185016, name: "Earned Runs" },
  { id: 3241745009, name: "Hits Allowed" },
  { id: 3170085014, name: "Walks Allowed" }
];

// Define sports and their market types
const SPORTS = {
  NBA: { url: 'https://app.ownersbox.com/fsp/v3/market?sport=NBA', marketTypes: NBA_MARKET_TYPES },
  WNBA: { url: 'https://app.ownersbox.com/fsp/v3/market?sport=WNBA', marketTypes: NBA_MARKET_TYPES },
  NFL: { url: 'https://app.ownersbox.com/fsp/v3/market?sport=NFL', marketTypes: [] },
  NHL: { url: 'https://app.ownersbox.com/fsp/v3/market?sport=NHL', marketTypes: NHL_MARKET_TYPES },
  MLB: { url: 'https://app.ownersbox.com/fsp/v3/market?sport=MLB', marketTypes: MLB_MARKET_TYPES },
  NCAAB: { url: 'https://app.ownersbox.com/fsp/v3/market?sport=NCAAB', marketTypes: [] },
  NCAAF: { url: 'https://app.ownersbox.com/fsp/v3/market?sport=NCAAF', marketTypes: [] }
};

async function fetchOwnersBox() {
  try {
    const proxy = await getProxy('OwnersBox');
    if (!proxy) {
      console.error('[OwnersBox] No proxy available');
      return [];
    }

    const httpsAgent = new HttpsProxyAgent(proxy);

    const headers = {
      'host': 'app.ownersbox.com',
      'content-type': 'application/json',
      'accept': 'application/json',
      'accept-encoding': 'gzip, deflate, br',
      'ownersbox-client-version': '9.5.0',
      'accept-language': 'en-US,en;q=0.9',
      'user-agent': 'OwnersBoxMobile/405 CFNetwork/3826.400.120 Darwin/24.3.0',
      'ownersbox-client-type': 'ios',
      'cookie': 'obauth=eyJhbGciOiJIUzUxMiJ9.eyJvYnRva2VuIjoiMFozQzdPMVgxRjdOIiwidXNlclN0YXRlIjoiQUNUSVZFIiwiaXNzIjoiT3duZXJzQm94IiwidmVyaWZpZWQiOmZhbHNlLCJ0b2tlbkV4cGlyeSI6MTc0MjU3MzUwNzA1MCwic2Vzc2lvbklkIjo2Mjk4NDUwOTg5Njc4NTkyfQ.0M_tT6R1CahmkMRJiuS1nNWT4vhxufm-KpU0zIDrLGgT64-TqQQ74X5QQj02-0Y44F3I6bF8SOKEEN8QKNprAA'
    };

    let allRequests = [];

    // Create requests for all sports and all market types at once
    Object.entries(SPORTS).forEach(([sportName, sportConfig]) => {
      const { url, marketTypes } = sportConfig;
      
      // If the sport has specific market types, create a request for each market type
      if (marketTypes && marketTypes.length > 0) {
        marketTypes.forEach(market => {
          const marketUrl = `${url}&marketTypeId=${market.id}`;
          allRequests.push({
            sportName,
            marketName: market.name,
            marketId: market.id,
            url: marketUrl
          });
        });
      } 
      // For sports without specific market types, create a single request
      else {
        allRequests.push({
          sportName, 
          marketName: 'General',
          url
        });
      }
    });

    // console.log(`Making ${allRequests.length} concurrent requests to OwnersBox API...`);
    
    // Execute all requests concurrently
    const responses = await Promise.all(
      allRequests.map(async (request) => {
        try {
          // console.log(`Fetching ${request.sportName} - ${request.marketName}...`);
          const response = await axios({
            method: 'get',
            url: request.url,
            headers,
            proxy: false,
            httpsAgent,
            timeout: 30000,
            maxRedirects: 5,
            validateStatus: null
          });

          if (response.status === 200 && response.data && response.data.markets) {
            // Report proxy success for successful requests
            reportProxySuccess(proxy);
            // console.log(`Successfully fetched ${request.sportName} - ${request.marketName}: ${response.data.markets.length} markets`);
            return {
              sportName: request.sportName,
              marketName: request.marketName,
              markets: response.data.markets
            };
          }
          // console.log(`No markets found for ${request.sportName} - ${request.marketName}`);
          return {
            sportName: request.sportName,
            marketName: request.marketName,
            markets: []
          };
        } catch (error) {
          console.error(`[OwnersBox] Error fetching ${request.sportName} - ${request.marketName} data:`, error.message);
          reportProxyFailure(proxy, error);
          return {
            sportName: request.sportName,
            marketName: request.marketName,
            markets: []
          };
        }
      })
    );

    // Process all responses
    let allProjections = [];
    for (const response of responses) {
      if (response.markets.length > 0) {
        const projections = processMarkets(response.markets, response.sportName);
        allProjections = [...allProjections, ...projections];
      }
    }

    return allProjections;
  } catch (error) {
    console.error(`[OwnersBox] Error fetching data:`, error.message);
    return [];
  }
}

function processMarkets(markets, league) {
  if (!Array.isArray(markets)) {
    console.error('[OwnersBox] Markets is not an array:', markets);
    return [];
  }

  return markets
    .filter(market => 
      market.state === 'ACTIVE' && 
      market.player &&
      market.line &&
      market.line.balancedLine &&
      market.game && 
      market.game.state === 'UPCOMING'
    )
    .map(market => {
      const player = market.player;
      const game = market.game;
      
      // Create matchup string
      const matchup = `${game.awayTeam.alias} @ ${game.homeTeam.alias}`;
      
      // Format player name
      const playerName = `${player.firstName} ${player.lastName}`;
      
      // Handle one-way props (MORE_ONLY) vs two-way props (MORE_OR_LESS)
      const isOneWay = market.pickOptions === 'MORE_ONLY';
      
      // Default odds (OwnersBox uses these fixed odds)
      const defaultDecimalOdds = 1.85; // Equivalent to -118 American odds
      
      // Map stat types properly
      let statType = market.marketType.name;
      
      // Map team based on player data
      const team = player.teamAlias;
      
      return {
        proj_id: market.id.toString(),
        league: league,
        player_name: playerName,
        stat_type: statType,
        line: market.line.balancedLine,
        over_odds_decimal: defaultDecimalOdds,
        under_odds_decimal: isOneWay ? undefined : defaultDecimalOdds,
        over_odds_american: '-118',
        under_odds_american: isOneWay ? undefined : '-118',
        matchup: matchup,
        start_time: new Date(game.date),
        source: 'OwnersBox',
        is_one_way: isOneWay,
        team: team
      };
    });
}

module.exports = { fetchOwnersBox };
