const { PrizePicksAPI } = require('./PrizePicks.js');
const { MongoClient } = require('mongodb');
require('dotenv').config();

// Function to fetch PrizePicks data from MongoDB
async function fetchPrizePicksFromMongo() {
  let client = null;
  try {
    // Connect directly to MongoDB
    const uri = process.env.MONGODB_CONNECTION_STRING;
    client = new MongoClient(uri);
    await client.connect();
    
    // Access the "prizepicks" database and "prizepicks" collection
    const db = client.db('prizepicks');
    const collection = db.collection('prizepicks');
    
    // Fetch only documents where modifier is 'standard'
    const projections = await collection.find({ modifier: 'standard' }).toArray();
    
    console.log(`Fetched ${projections.length} PrizePicks projections with standard modifier`);
    
    // Transform MongoDB documents to match the format expected by transformProjections
    const transformedData = transformMongoDataToApiFormat(projections);
    
    return transformedData;
  } catch (error) {
    console.error('Error fetching PrizePicks projections from MongoDB:', error);
    throw error;
  } finally {
    // Close the MongoDB connection if it was opened
    if (client) {
      await client.close();
    }
  }
}

// Function to transform MongoDB data to match the format expected by transformProjections
function transformMongoDataToApiFormat(mongoData) {
  if (!mongoData || !Array.isArray(mongoData) || mongoData.length === 0) {
    console.error('Invalid or empty MongoDB data');
    return { data: [], included: [] };
  }
  
  // Create the data structure expected by transformProjections
  const result = {
    data: [],
    included: []
  };
  
  // Map to keep track of unique players
  const uniquePlayers = new Map();
  
  // Process each MongoDB document
  mongoData.forEach(doc => {
    // Skip invalid documents
    if (!doc.selection || !doc.sourceprojectionid) return;
    
    // Create a league‑scoped player ID so NHL and NHL SERIES stay separate
    const leagueSlug  = (doc.league || '').toLowerCase().replace(/\s+/g, '-');
    const playerSlug  = doc.selection.toLowerCase().replace(/\s+/g, '-');
    const playerId    = `player-${leagueSlug}-${playerSlug}`;
    
    // Get values, handling potential MongoDB Extended JSON format
    const line = doc.line && doc.line.$numberDouble ? parseFloat(doc.line.$numberDouble) : doc.line;
    const startTime = doc.eventstarttime && doc.eventstarttime.$date && doc.eventstarttime.$date.$numberLong
      ? new Date(parseInt(doc.eventstarttime.$date.$numberLong))
      : (doc.eventstarttime instanceof Date ? doc.eventstarttime : new Date(doc.eventstarttime));
    const odds = doc.odds && doc.odds.$numberDouble ? parseFloat(doc.odds.$numberDouble) : doc.odds;
    
    // Debug output for NHL SERIES
    if (doc.league === 'NHL SERIES') {
      console.log(`Found NHL SERIES projection for ${doc.selection}: ${doc.market}, line=${line}`);
    }
    
    // Create the projection object
    const projection = {
      id: doc.sourceprojectionid,
      type: 'projection',
      attributes: {
        line_score: line,
        odds_type: 'standard',
        is_promo: false,
        stat_type: doc.market,
        description: doc.awayteam || '',
        start_time: startTime.toISOString()
      },
      relationships: {
        new_player: {
          data: {
            id: playerId,
            type: 'new_player'
          }
        }
      }
    };
    
    // Add projection to data array
    result.data.push(projection);
    
    // Add player to uniquePlayers map if not already added
    if (!uniquePlayers.has(playerId)) {
      uniquePlayers.set(playerId, {
        id: playerId,
        type: 'new_player',
        attributes: {
          // Explicitly preserve league - especially important for NHL SERIES
          league: doc.league,
          display_name: doc.selection,
          name: doc.selection,
          team: doc.hometeam,
          combo: false
        }
      });
    }
  });
  
  // Add all unique players to included array
  result.included = Array.from(uniquePlayers.values());
  
  return result;
}

async function fetchPrizePicksInternal(proxy = null, timeout = 75000) {
  return new Promise(async (resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error('PrizePicks fetch timeout'));
    }, timeout);

    try {
      // Fetch data from MongoDB instead of Python script
      const rawData = await fetchPrizePicksFromMongo();
      
      // Transform data using JS implementation
      const prizePicksApi = new PrizePicksAPI();
      const projections = prizePicksApi.transformProjections(rawData);
      
      // Extra check to ensure NHL SERIES is preserved
      if (rawData && rawData.included) {
        const nhlSeriesPlayers = rawData.included.filter(p =>
          p.attributes && p.attributes.league === 'NHL SERIES'
        );
        
        if (nhlSeriesPlayers.length > 0) {
          console.log(`Found ${nhlSeriesPlayers.length} NHL SERIES players in raw data`);
          
          // Check if they made it through transformation
          const nhlSeriesInOutput = projections.filter(p => p.league === 'NHL SERIES');
          console.log(`Preserved ${nhlSeriesInOutput.length} NHL SERIES projections after transformation`);
        }
      }
      
      clearTimeout(timeoutId);
      resolve(projections);
    } catch (error) {
      clearTimeout(timeoutId);
      console.error('Error fetching PrizePicks projections:', error);
      reject(error);
    }
  });
}

// Wrapper that matches the Provider type signature
async function fetchPrizePicks(sharpProjections) {
  return fetchPrizePicksInternal();
}

module.exports = { fetchPrizePicks };