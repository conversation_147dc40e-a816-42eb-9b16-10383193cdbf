const ThrillzzApi = require('../api/thrillzz');
//const fs = require('fs');

const fetchThrillzz = async () => {
    const thrillzzApi = new ThrillzzApi();
    const projections = await thrillzzApi.connect();
/*
    // Write projections to a JSON file
    fs.writeFileSync('thrillzz_projections.json', JSON.stringify(projections, null, 2));

    // Extract unique stat types for each league
    const leagueStatsMap = {};

    projections.forEach(projection => {
        const { league, stat_type } = projection;

        if (!leagueStatsMap[league]) {
            leagueStatsMap[league] = new Set();
        }

        leagueStatsMap[league].add(stat_type);
    });

    // Convert Sets to Arrays for JSON serialization
    const leagueStatsArray = Object.fromEntries(
        Object.entries(leagueStatsMap).map(([league, statTypes]) => [league, Array.from(statTypes)])
    );

    // Write unique stat types per league to a JSON file
    fs.writeFileSync('thrillzz_unique_stat_types.json', JSON.stringify(leagueStatsArray, null, 2));
*/
    // Return the array of projections
    return projections;
};

module.exports = { fetchThrillzz };
