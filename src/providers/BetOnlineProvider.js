const { fetchSharpProjections } = require('../utils/ProjectionUtility');

async function fetchBetOnline(sharpProjections) {
    // Use passed sharp projections if available, otherwise fetch new ones
    const projections = sharpProjections || await fetchSharpProjections();
    const betOnlineProjections = projections.filter(proj => proj.source === 'BetOnline');
    return betOnlineProjections.map(proj => ({
        ...proj,
        source: 'PropBuilder'
    }));
}

module.exports = {
    fetchBetOnline
};