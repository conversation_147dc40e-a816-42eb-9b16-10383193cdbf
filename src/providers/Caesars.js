const { MongoClient } = require('mongodb');

// Create a singleton connection pool
let clientPromise = null;

function getClient() {
  if (!clientPromise) {
    const client = new MongoClient('mongodb+srv://livehypeplus:<EMAIL>/?retryWrites=true&w=majority', {
      // Connection pool settings
      maxPoolSize: 20,
      minPoolSize: 10,
      maxIdleTimeMS: 300000, // 5 minutes
      
      // Timeouts
      connectTimeoutMS: 30000, // 30 seconds
      socketTimeoutMS: 45000,  // 45 seconds
      serverSelectionTimeoutMS: 30000, // 30 seconds
      
      // Read preference and concern
      readPreference: 'secondaryPreferred',
      readConcern: { level: 'local' },
      
      // Network optimizations
      compressors: ['zlib'],
      zlibCompressionLevel: 9,
      
      // Basic retry settings
      retryWrites: true,
      retryReads: true
    });
    clientPromise = client.connect();
  }
  return clientPromise;
}

async function fetchCaesars() {
  let retries = 5;
  let lastError = null;

  while (retries > 0) {
    try {
      // Get client from pool
      const client = await getClient();
      const db = client.db('Caesars');
      const collection = db.collection('player_projections');

      // Create compound index if it doesn't exist
      await collection.createIndex({ 
        league: 1,
        matchup: 1 
      }, { 
        background: true 
      });

      // Fetch all projections with optimized settings
      const projections = await collection.find({}, {
        maxTimeMS: 30000, // 30 second query timeout
        batchSize: 1000,  // Larger batch size for faster retrieval
      }).toArray();

      return projections.map(proj => ({
        ...proj,
        source: 'Caesars'
      }));

    } catch (error) {
      lastError = error;
      console.error(`[CAESARS] Attempt ${6-retries}/5 failed:`, error.message);
      
      if (retries === 1) {
        console.error('[CAESARS] All retries failed, returning empty array');
        return [];
      }

      // Exponential backoff with jitter
      const delay = Math.floor(1000 * Math.pow(2, 5-retries) * (0.5 + Math.random()));
      console.log(`[CAESARS] Retrying in ${Math.round(delay)}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      retries--;
    }
  }
}

module.exports = {
  fetchCaesars
};