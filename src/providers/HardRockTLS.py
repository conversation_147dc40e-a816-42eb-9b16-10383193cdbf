import tls_client
import json
import sys

def make_request(url, headers, data, proxy_str=None):
    try:
        session_params = {
            "client_identifier": "firefox_120",
            "random_tls_extension_order": True,
            # Add some custom TLS parameters
            "h2_settings": {
                "HEADER_TABLE_SIZE": 65536,
                "MAX_CONCURRENT_STREAMS": 1000,
                "INITIAL_WINDOW_SIZE": 6291456,
                "MAX_HEADER_LIST_SIZE": 262144
            },
            "h2_settings_order": [
                "HEADER_TABLE_SIZE",
                "MAX_CONCURRENT_STREAMS",
                "INITIAL_WINDOW_SIZE",
                "MAX_HEADER_LIST_SIZE"
            ],
            "supported_signature_algorithms": [
                "ECDSAWithP256AndSHA256",
                "PSSWithSHA256",
                "PKCS1WithSHA256",
                "ECDSAWithP384AndSHA384",
                "PSSWithSHA384",
                "P<PERSON>CS1<PERSON>ithSHA384",
                "PSSWithSHA512",
                "PKCS1WithSHA512",
            ],
            "supported_versions": ["GREASE", "1.3", "1.2"],
            "key_share_curves": ["GREASE", "X25519"],
        }

        session = tls_client.Session(**session_params)
        
        request_args = {
            "headers": headers,
            "json": data
        }
        if proxy_str:
            try:
                # Assuming proxy_str is in format host:port:user:pass
                parts = proxy_str.split(':')
                if len(parts) == 4:
                    host, port, user, pw = parts
                    formatted_proxy_url = f"http://{user}:{pw}@{host}:{port}"
                    request_args["proxy"] = formatted_proxy_url
                else:
                    # If not in the expected 4-part format, pass it as is,
                    # though it might still cause issues if not a valid URL.
                    # Or, one could raise an error here or log a warning.
                    request_args["proxy"] = proxy_str 
            except Exception:
                # In case of any error during parsing, pass original string
                # or handle error appropriately
                request_args["proxy"] = proxy_str

        response = session.post(url, **request_args)
        # Check status code directly
        if response.status_code >= 400:
            return json.dumps({"error": f"HTTP error {response.status_code}: {response.text}", "status": response.status_code})
        return json.dumps({"status": response.status_code, "data": response.json(), "headers": dict(response.headers)})
    except Exception as e:
        return json.dumps({"error": str(e), "status": 500}) # Adding a status for general errors

if __name__ == "__main__":
    url = sys.argv[1]
    headers = json.loads(sys.argv[2])
    data = json.loads(sys.argv[3])
    proxy_arg = None
    if len(sys.argv) > 4:
        proxy_arg = sys.argv[4]
        # Basic validation if proxy_arg is not an empty string or placeholder
        if proxy_arg.lower() == 'none' or proxy_arg.strip() == "":
            proxy_arg = None
            
    result = make_request(url, headers, data, proxy_str=proxy_arg)
    print(result)
