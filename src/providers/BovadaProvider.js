// ./providers/BovadaProvider.js

const { Agent } = require('undici');
const { getProxy } = require('../utils/proxy');

const leagueUrls = {
    NFL: 'https://www.bovada.lv/services/sports/event/coupon/events/A/description/football/nfl?marketFilterId=def&preMatchOnly=true&eventsLimit=5000&lang=en',
    CFB: 'https://www.bovada.lv/services/sports/event/coupon/events/A/description/football/college-football?marketFilterId=def&preMatchOnly=true&eventsLimit=5000&lang=en',
    NBA: 'https://www.bovada.lv/services/sports/event/coupon/events/A/description/basketball/nba?marketFilterId=def&preMatchOnly=true&eventsLimit=5000&lang=en',
    MLB: 'https://www.bovada.lv/services/sports/event/coupon/events/A/description/baseball?marketFilterId=def&preMatchOnly=true&eventsLimit=5000&lang=en',
    //HRDERBY: 'https://www.bovada.lv/services/sports/event/coupon/events/A/description/baseball/home-run-derby?marketFilterId=rank&preMatchOnly=true&eventsLimit=5000&lang=en',
    //NHL: 'https://www.bovada.lv/services/sports/event/coupon/events/A/description/hockey/nhl?marketFilterId=def&preMatchOnly=true&eventsLimit=5000&lang=en',
    //NCAAB: 'https://www.bovada.lv/services/sports/event/coupon/events/A/description/basketball/college-basketball?marketFilterId=def&preMatchOnly=true&eventsLimit=5000&lang=en'
};

// Map of leagues to their relevant displayGroups
const leagueDisplayGroups = {
    NFL: ["Quarterback Props", "Rushing Props", "Receiving Props", "Special Teams", "Defensive Player Props", "Combined Yards"],
    CFB: ["Quarterback Props", "Rushing Props", "Receiving Props", "Special Teams", "Defensive Player Props", "Combined Yards"],
    NBA: ["Player Combos", "Player Points", "Player Rebounds", "Assists & Threes", "Blocks & Steals", "Player Turnovers"],
    MLB: ["Pitcher Props", "Player Props"],
    //HRDERBY: ["Futures"],
};

const fetchEventDetails = async (eventLink, league, startTime) => {
    const url = `https://www.bovada.lv/services/sports/event/coupon/events/A/description${eventLink}`;
    try {
        const proxy = await getProxy('Bovada');
        const proxyAgent = new Agent({
            connect: {
                host: proxy.host,
                port: proxy.port,
                username: proxy.username,
                password: proxy.password,
            },
        });

        const response = await fetch(url, { dispatcher: proxyAgent });
        const eventData = await response.json();

        const validProjectionTypes = leagueDisplayGroups[league];
        let projections = [];

        if (JSON.stringify(eventData) === "{}") {
            return [];
        }

        const convertEvenOdds = (odds) => odds === "EVEN" ? "+100" : odds;

        eventData.forEach(eventDataItem => {
            eventDataItem.events.forEach(event => {
                event.displayGroups.forEach(group => {
                    if (validProjectionTypes.includes(group.description)) {
                        group.markets.forEach(market => {
                            let currentLeague = eventDataItem.path[0].description;
                            if (currentLeague === 'College Football') {
                                currentLeague = 'CFB';
                            }

                            if (currentLeague === 'NFL' || currentLeague === 'MLB' || currentLeague === 'CFB' || currentLeague === 'NBA') {
                                const overOutcome = market.outcomes.find(o => o.description === "Over");
                                const underOutcome = market.outcomes.find(o => o.description === "Under");

                                if (overOutcome && underOutcome) {
                                    const descriptionParts = market.description.split(" - ");
                                    let playerName = descriptionParts.length > 1 ? descriptionParts[1].trim() : "";
                                    const statType = descriptionParts.length > 0 ? descriptionParts[0].trim() : "";
                
                                    playerName = playerName.replace(/\s*\([^)]*\)/, '').trim();

                                    const projection = {
                                        proj_id: market.id,
                                        league: currentLeague,
                                        player_name: playerName,
                                        stat_type: statType,
                                        line: parseFloat(overOutcome.price.handicap),
                                        over_odds_decimal: parseFloat(overOutcome.price.decimal),
                                        under_odds_decimal: parseFloat(underOutcome.price.decimal),
                                        over_odds_american: convertEvenOdds(overOutcome.price.american),
                                        under_odds_american: convertEvenOdds(underOutcome.price.american),
                                        matchup: event.description,
                                        start_time: new Date(startTime),
                                        source: 'Bovada'
                                    };
                                    projections.push(projection);
                                }
                            }
                        });
                    }
                });
            });
        });

        return projections;
    } catch (error) {
        console.error(`Error fetching event details for ${eventLink}:`, error);
        return [];
    }
};

const fetchEventsForLeague = async (league, url) => {
    try {
        const proxy = await getProxy('Bovada');
        const proxyAgent = new Agent({
            connect: {
                host: proxy.host,
                port: proxy.port,
                username: proxy.username,
                password: proxy.password,
            },
        });

        const response = await fetch(url, { dispatcher: proxyAgent });
        const data = await response.json();

        if (data.length === 0) return [];

        const eventPromises = data.flatMap(item => 
            item.events.map(event => fetchEventDetails(event.link, league, event.startTime))
        );

        const results = await Promise.allSettled(eventPromises);
        return results.reduce((acc, result) => {
            if (result.status === 'fulfilled') {
                acc.push(...result.value);
            } else {
                console.error(`Error fetching event details:`, result.reason);
            }
            return acc;
        }, []);
    } catch (error) {
        console.error(`Error fetching events for league ${league}:`, error);
        return [];
    }
};

const fetchBovada = async () => {
    const leaguePromises = Object.entries(leagueUrls).map(([league, url]) => {
        return fetchEventsForLeague(league, url).then(leagueProjections => ({
            league,
            leagueProjections
        }));
    });

    const results = await Promise.allSettled(leaguePromises);
    let projections = [];

    results.forEach(result => {
        if (result.status === 'fulfilled') {
            projections = projections.concat(result.value.leagueProjections);
        } else {
            console.error(`Error with league ${result.reason.league}:`, result.reason);
        }
    });

    return projections;
};

module.exports = { fetchBovada };
