import { fetchBetOnline } from "../providers/BetOnlineProvider";
import { Projection } from "../utils/types";
import { fetchBetMGM } from "./BetMGMProvider";
import { fetchBetRivers } from "./BetRiversProvider";
//import { fetchBetSaracen } from "./BetSaracenProvider";
import { fetchBetr } from "./BetrProvider";
//import { fetchBoom } from "./BoomProvider";
import { fetchBovada } from "./BovadaProvider";
import { fetchCaesars } from "./Caesars";
import { fetchDabble } from "./DabbleProvider";
// import { fetchESPNBet } from "./ESPNBet";
import { fetchFanatics } from "./Fanatics";
import { fetchFanduel } from "./Fanduel";
import { fetchFliff } from "./Fliff";
import { fetchPrizePicks } from "./PrizePicksProvider.cjs";
import { fetchSleeper } from "./Sleeper";
import { fetchUnderdog } from "./UnderdogProvider";
import { fetchVivid } from "./VividPicksProvider";
import { fetchRebet } from "./RebetProvider";
import { fetchNovig } from "./Novig";
//import { fetchOaklawnSports } from "./OaklawnSportsProvider";
import { fetchHotstreak } from "./HotstreakProvider";
import HardRockScraper from "./HardRock";
import { fetchPick6 } from "./Pick6";
import { fetchDraftKings } from "./DraftKings";
import { fetchOwnersBox } from "./OwnersBox";
import { fetchEpick } from "./EpickProvider"; // Import the new Epick provider
const { fetchProphetX } = require("./ProphetX.js");

// Define the fetchHardRock function
const fetchHardRockData = async (): Promise<Projection[]> => {
  const scraper = new HardRockScraper();
  const projections = await scraper.fetchHardRock();
  return projections;
};

export const SPORTSBOOKS = ["BetMGM", "PropBuilder", "BetRivers", "Bovada", "Caesars", "ESPNBet", "Fanatics", "Fanduel", "Fliff", "HardRock", "Rebet", "Novig", "OwnersBox", "ProphetX"]

export type Provider = {
  name: string;
  fetchFunction: (sharpProjections?: Projection[]) => Promise<Projection[]>;
  collectionName: string;
};

export const providers: Provider[] = [
  /*
  {
    name: "BetMGM",
    fetchFunction: fetchBetMGM,
    collectionName: "BetMGM",
  },
  */
  {
    name: "PropBuilder",
    fetchFunction: fetchBetOnline,
    collectionName: "PropBuilder",
  },
  /* 
  {
    name: "BetSaracen",
    fetchFunction: fetchBetSaracen,
    collectionName: "BetSaracen",
  },
  {
    name: "Boom",
    fetchFunction: fetchBoom,
    collectionName: "Boom",
  },
  */
  {
    name: "Betr",
    fetchFunction: fetchBetr,
    collectionName: "Betr",
  },
  {
    name: "BetRivers",
    fetchFunction: fetchBetRivers,
    collectionName: "BetRivers",
  },
  /*
  {
    name: "Bovada",
    fetchFunction: fetchBovada,
    collectionName: "Bovada",
  },
  */
  {
    name: "Caesars",
    fetchFunction: fetchCaesars,
    collectionName: "Caesars",
  },
  {
    name: "Dabble",
    fetchFunction: fetchDabble,
    collectionName: "Dabble",
  },
  {
    name: "Epick",
    fetchFunction: fetchEpick,
    collectionName: "Epick",
  },
  /*
  {
    name: "ESPNBet",
    fetchFunction: fetchESPNBet,
    collectionName: "ESPNBet",
  },
  */
  {
    name: "Fanatics",
    fetchFunction: fetchFanatics,
    collectionName: "Fanatics",
  },
  /*
  {
    name: "JockMKT",
    fetchFunction: fetchJockMKT,
    collectionName: "JockMKT",
  },
  */
  {
    name: "Fanduel",
    fetchFunction: fetchFanduel,
    collectionName: "Fanduel",
  },
  {
    name: "Fliff",
    fetchFunction: fetchFliff,
    collectionName: "Fliff",
  },
  {
    name: "HardRock",
    fetchFunction: fetchHardRockData,
    collectionName: "HardRock",
  },
  {
    name: "Hotstreak",
    fetchFunction: fetchHotstreak,
    collectionName: "Hotstreak",
  },
  {
    name: "Novig",
    fetchFunction: fetchNovig,
    collectionName: "Novig",
  },
  /*
  {
    name: "OaklawnSports",
    fetchFunction: fetchOaklawnSports,
    collectionName: "OaklawnSports",
  },
  */
  {
    name: "Pick6",
    fetchFunction: fetchPick6,
    collectionName: "Pick6",
  },
  {
    name: "PrizePicks",
    fetchFunction: fetchPrizePicks,
    collectionName: "PrizePicks",
  },
  {
    name: "Rebet",
    fetchFunction: fetchRebet,
    collectionName: "Rebet",
  },
  /*
  {
    name: "Sleeper",
    fetchFunction: fetchSleeper,
    collectionName: "Sleeper",
  },
  */
  {
    name: "Underdog",
    fetchFunction: fetchUnderdog,
    collectionName: "Underdog",
  },
  {
    name: "VividPicks",
    fetchFunction: fetchVivid,
    collectionName: "VividPicks",
  },
  {
    name: "DraftKings",
    fetchFunction: fetchDraftKings,
    collectionName: "DraftKings",
  },
  {
    name: "OwnersBox",
    fetchFunction: fetchOwnersBox,
    collectionName: "OwnersBox",
  },
  {
    name: "ProphetX",
    fetchFunction: fetchProphetX,
    collectionName: "ProphetX",
  },
];