const fs = require('fs').promises;
const path = require('path');
const { fetch, ProxyAgent } = require('undici');
const { getProxy } = require('../utils/proxy');

const sportsUrls = {
    CFB: 'https://api.sleeper.app/lines/available?sports%5B%5D=cfb&dynamic=true&include_preseason=true&eg=15.control',
    //MLB: 'https://api.sleeper.app/lines/available?sports%5B%5D=mlb&dynamic=true&eg=13.control',
    NFL: 'https://api.sleeper.app/lines/available?sports%5B%5D=nfl&dynamic=true&include_preseason=true&eg=15.control',
    //WNBA: 'https://api.sleeper.app/lines/available?sports%5B%5D=wnba&dynamic=true&include_preseason=true&eg=15.control',
    NBA: 'https://api.sleeper.app/lines/available?sports%5B%5D=nba&dynamic=true&eg=13.control',
    NHL: 'https://api.sleeper.app/lines/available?sports%5B%5D=nhl&dynamic=true&include_preseason=true&eg=16.control',
    CBB: 'https://api.sleeper.app/lines/available?sports%5B%5D=cbb&dynamic=true&eg=13.control',
    //CS2: 'https://api.sleeper.app/lines/available?sports%5B%5D=cs&dynamic=true&include_preseason=true&eg=16.control'

};

const playerEndpoints = {
    CFB: 'https://api.sleeper.app/players/cfb?exclude_injury=false',
    MLB: 'https://api.sleeper.app/players/mlb?exclude_injury=false',
    NFL: 'https://api.sleeper.app/players/nfl?exclude_injury=false',
    WNBA: 'https://api.sleeper.app/players/wnba?exclude_injury=false',
    NBA: 'https://api.sleeper.app/players/nba?exclude_injury=false',
    NHL: 'https://api.sleeper.app/players/nhl?exclude_injury=false',
    CBB: 'https://api.sleeper.app/players/cbb?exclude_injury=false',
    //CS2: 'https://api.sleeper.app/players/cs?exclude_injury=false'
};

const scoresEndpoints = {
    CFB: 'https://api.sleeper.app/scores/cfb/regular/2024/1',
    MLB: 'https://api.sleeper.app/scores/mlb/regular/2024/13',
    NFL: 'https://api.sleeper.app/scores/nfl/regular/2024/6',
    WNBA: 'https://api.sleeper.app/scores/wnba/date/', // Base URL for WNBA
    //NFL_PRE: 'https://api.sleeper.app/scores/nfl/pre/2024/3',
    NBA: 'https://api.sleeper.app/scores/nba/regular/2024/1',
    CBB: 'https://api.sleeper.app/scores/cbb/regular/2024/1',
    NHL: 'https://api.sleeper.app/scores/nhl/regular/2024/1',
    //CS2: 'https://api.sleeper.app/scores/cs/regular/2024/41'
};

const headers = {
    accept: 'application/json',
    'x-api-client': 'api.cached',
    'accept-language': 'en-US,en;q=0.9',
    'x-device-id': '924C15C9-C8AF-4130-B998-EB08B79DD9B6',
    'x-platform': 'ios',
    'x-build': '88.3.v4144',
    'x-bundle': 'com.blitzstudios.sleeperbot',
};

const playerCache = {};
const eventCache = {};

async function fetchWithProxy(url, options = {}, retries = 2) {
    let lastError;
    for (let i = 0; i < retries + 1; i++) {
        try {
            const proxyUrl = await getProxy('Sleeper'); // Use Sleeper-specific proxies
            const proxyAgent = new ProxyAgent(proxyUrl);
            
            const controller = new AbortController();
            const timeout = setTimeout(() => controller.abort(), 30000);
            
            const response = await fetch(url, {
                ...options,
                dispatcher: proxyAgent,
                signal: controller.signal
            });
            
            clearTimeout(timeout);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return response;
        } catch (error) {
            lastError = error;
            if (i < retries) {
                console.log(`[SLEEPER] Retry ${i + 1}/${retries} for ${url} due to: ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, 2000 * (i + 1))); // Exponential backoff
            }
        }
    }
    throw lastError;
}

async function getPlayerName(league, subjectId) {
    if (!playerCache[league]) {
        const response = await fetchWithProxy(playerEndpoints[league], { headers });
        playerCache[league] = await response.json();
    }

    const player = playerCache[league][subjectId];
    if (player) {
        return `${player.first_name} ${player.last_name}`;
    }
    return subjectId;
}

async function getEventData(league, gameId) {
    //console.log(`Fetching event data for league: ${league}, gameId: ${gameId}`);
    
    async function fetchAndFindEvent(endpoint) {
        //console.log(`Fetching from endpoint: ${endpoint}`);
        if (!endpoint) {
            console.error(`[SLEEPER] Endpoint is undefined for league: ${league}`);
            return null;
        }
        if (!eventCache[endpoint]) {
            try {
                const response = await fetchWithProxy(endpoint, { headers });
                eventCache[endpoint] = await response.json();
            } catch (error) {
                console.error(`[SLEEPER] Error fetching data from ${endpoint}:`, error);
                return null;
            }
        }

        return eventCache[endpoint].find((event) => event.game_id === gameId);
    }

    if (league === 'WNBA') {
        const [dateStr] = gameId.split('_');
        const formattedDate = `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`;
        const endpoint = `${scoresEndpoints.WNBA}${formattedDate}`;

        const event = await fetchAndFindEvent(endpoint);

        if (event) {
            const { metadata } = event;
            return {
                start_time: new Date(parseInt(event.start_time)),
                matchup: `${metadata.away_team.team} vs ${metadata.home_team.team}`,
                game_id: event.game_id,
                // Add any other relevant fields you want to extract
            };
        }
    } else if (league === 'NFL') {
        //console.log('Entering NFL logic');
        const currentWeek = scoresEndpoints[league] ? parseInt(scoresEndpoints[league].split('/').pop()) : 1;
        //console.log(`Current week for NFL: ${currentWeek}`);
        let week = currentWeek;

        while (week <= currentWeek + 10) {
            const endpoint = scoresEndpoints[league] ? scoresEndpoints[league].replace(/\/\d+$/, `/${week}`) : null;
            //console.log(`Trying endpoint: ${endpoint}`);
            let event = await fetchAndFindEvent(endpoint);

            if (event) {
                const { metadata } = event;
                return {
                    start_time: new Date(parseInt(event.start_time)),
                    matchup: `${metadata.away_team} vs ${metadata.home_team}`,
                    game_id: metadata.game_key,
                };
            }

            week++;
        }
    } else {
        // Existing logic for other leagues
        const currentWeek = parseInt(scoresEndpoints[league].split('/').pop());
        let week = currentWeek;

        while (true) {
            const endpoint = scoresEndpoints[league].replace(/\/\d+$/, `/${week}`);
            const event = await fetchAndFindEvent(endpoint);

            if (event) {
                return {
                    start_time: new Date(event.start_time),
                    // Add any other fields for non-NFL leagues if needed
                };
            }

            if (eventCache[endpoint].length === 0 || week > currentWeek + 10) break;
            week++;
        }
    }

    return null;
}

function decimalToAmericanOdds(decimalOdds) {
    if (decimalOdds >= 2) {
        return `+${Math.round((decimalOdds - 1) * 100)}`;
    } else {
        return `-${Math.round(100 / (decimalOdds - 1))}`;
    }
}

async function extractProjections(data) {
    const projections = [];

    for (const item of data) {
        if (
            item.game_status === 'pre_game' &&
            item.line_type === 'normal' &&
            item.outcome_type === 'over_under' &&
            item.subject_type === 'player' &&
            item.status === 'active'
        ) {
            const overOption = item.options.find((option) => option.outcome === 'over');
            const underOption = item.options.find((option) => option.outcome === 'under');

            if (overOption && underOption) {
                const playerName = await getPlayerName(item.sport.toUpperCase(), item.subject_id);
                const eventData = await getEventData(item.sport.toUpperCase(), item.game_id);
                const source = 'Sleeper';
                let league = item.sport.toUpperCase();
                if (league === 'CBB') {
                    league = 'NCAAB';
                }
                const matchup = eventData?.matchup || 'TBD vs. TBD';

                // Check if any value is 'Unknown' or null
                if (playerName === 'Unknown' || !eventData || !eventData.start_time) {
                    continue; // Skip this projection
                }

                let stat_type = item.wager_type;
                const raw_start_time = eventData ? eventData.start_time : null;
                const start_time = new Date(raw_start_time);

                // Modify league based on quarter or half information
                const periodModifiers = {
                    'first_qtr': '1Q',
                    'second_qtr': '2Q',
                    'third_qtr': '3Q',
                    'fourth_qtr': '4Q',
                    'first_half': '1H',
                    'second_half': '2H',
                    'first_period': '1P',
                    'second_period': '2P',
                    'third_period': '3P',
                };

                for (const [period, modifier] of Object.entries(periodModifiers)) {
                    if (item.wager_type.includes(period)) {
                        league = `${league}${modifier}`;
                        break;  // Exit the loop after finding a match
                    }
                }

                const projection = {
                    proj_id: item.market_type,
                    league: league,
                    player_name: playerName,
                    stat_type: stat_type,
                    line: overOption.outcome_value,
                    over_odds_decimal: parseFloat(overOption.payout_multiplier),
                    under_odds_decimal: parseFloat(underOption.payout_multiplier),
                    over_odds_american: decimalToAmericanOdds(parseFloat(overOption.payout_multiplier)),
                    under_odds_american: decimalToAmericanOdds(parseFloat(underOption.payout_multiplier)),
                    matchup: matchup,
                    start_time: start_time,
                    source: source,
                };

                projections.push(projection);
            }
        }
    }

    return projections;
}

async function fetchProjections(sport, url) {
    try {
        if (!url) {
            console.error(`URL for ${sport} is undefined`);
            return [];
        }
        const response = await fetchWithProxy(url, { headers });
        const data = await response.json();
        const projections = await extractProjections(data);
        return projections;
    } catch (error) {
        console.error(`Error fetching ${sport} data:`, error);
        return [];
    }
}

async function fetchSleeper() {
    const fetchPromises = Object.entries(sportsUrls).map(([sport, url]) => fetchProjections(sport, url));
    const allProjections = await Promise.all(fetchPromises);
    const mergedProjections = allProjections.flat();
    return mergedProjections;
}

// async function main() {
//     try {
//         const projections = await fetchSleeper();
//         await fs.writeFile('sleeper_projections.json', JSON.stringify(projections, null, 2));
//         console.log('Projections saved to sleeper_projections.json');
//     } catch (error) {
//         console.error('An error occurred:', error);
//     }
// }

// main();

module.exports = {
    fetchSleeper,
};
