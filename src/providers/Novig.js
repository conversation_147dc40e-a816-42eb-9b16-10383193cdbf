const { got } = require('got-cjs');
const { HttpsProxyAgent } = require('https-proxy-agent');
const { getProxy } = require('../utils/proxy');
const fs = require('fs');
const path = require('path');

const DEFAULT_PROXY_PATH = path.join(__dirname, 'proxies', 'proxies.txt');

const EVENTS_QUERY = `query Home_Query($order_by_event: [event_order_by!], $where_event: event_bool_exp, $limit_count: Int!) {
  event(where: $where_event, order_by: $order_by_event, limit: $limit_count) {
    id
    # We only need the ID from this initial query now
    # status
    # metabet_points
    # game {
    #   id
    #   status
    #   league
    #   scheduled_start
    #   tournament {
    #     id
    #     name
    #     __typename
    #   }
    #   __typename
    # }
    # ...EventCard_Frag # This fragment is no longer needed here
    __typename
  }
}
`;
// Removed the large EventCard_Frag and its sub-fragments as they are no longer used by EVENTS_QUERY


const SIMPLIFIED_EVENT_DETAILS_QUERY = `query SimplifiedEventDetails_Query($eventId: uuid) {
  event(where: {id: {_eq: $eventId}}) {
    id
    status # Needed for OPEN_PREGAME check
    ...SimplifiedMarketSelector_Frag
    __typename
  }
}

fragment SimplifiedOutcome_Frag on outcome {
  id
  description # Needed to find Over/Under
  available   # Needed for odds calculation
  orders(where: {status: {_eq: "OPEN"}, currency: {_eq: "CASH"}}) { # Needed for limits
    qty
    price
    __typename
  }
  market { # Need path back to game details
    event {
      game {
        scheduled_start # Needed for start_time
        awayTeam {
          symbol # Needed for matchup
          __typename
        }
        homeTeam {
          symbol # Needed for matchup
          __typename
        }
        __typename
      }
      __typename
    }
    __typename
  }
  __typename
}

fragment SimplifiedMarketSelector_Frag on event {
  id
  # Fetch markets relevant for player props, excluding spread/total/moneyline
  # Added status filter here as well to potentially help backend
  markets(where: {
    _and: [
      { type: { _nin: ["SPREAD", "TOTAL", "MONEY"] } },
      { status: { _eq: "OPEN" } },
      { outcomes: { available: { _is_null: false } } } # Ensure there's an available price
      # { player_id: { _is_null: false } } # Removed: This filter was invalid
    ]
  }) {
    id # Needed for proj_id
    strike # Needed for line
    type # Needed for stat_type & filtering
    status # Needed for filtering OPEN markets
    outcomes(where: { description: { _regex: "^(Over|Under)" }}) { # Only fetch Over/Under outcomes
      id
      ...SimplifiedOutcome_Frag
      __typename
    }
    player { # Needed for player_name
      id
      full_name
      __typename
    }
    __typename
  }
  __typename
}`; 
// Removed the old EVENT_DETAILS_QUERY and its complex fragments


function decimalToAmerican(decimalOdds) {
    if (!isFinite(decimalOdds) || decimalOdds <= 1) {
        // Handle invalid inputs like Infinity, NaN, <= 1
        return null; // Return null for invalid/unavailable odds
    }
    if (decimalOdds >= 2) {
        // Positive odds: +100 * (Decimal - 1)
        return `+${Math.round((decimalOdds - 1) * 100)}`;
    } else {
        // Negative odds: -100 / (Decimal - 1)
        // Calculate the value, which should already be negative
        const americanOddsValue = Math.round(-100 / (decimalOdds - 1));
        // Return the calculated value as a string
        return `${americanOddsValue}`;
    }
}

function extractProjections(eventResponse, league) {
    const projections = [];
    // Check if event data exists and handle cases where it might be null/undefined
    const event = eventResponse?.data?.event?.[0];
    
    // If no event found in response, or status is not OPEN_PREGAME, return empty
    if (!event || event.status !== 'OPEN_PREGAME') return [];

    // No longer need EXCLUDED_TYPES here, query filters them
    // const EXCLUDED_TYPES = ['SPREAD', 'TOTAL', 'MONEY']; 

    for (const market of event.markets) {
        // Filtering by type/status is done in the GraphQL query
        // Re-introduce the check for player data, as the server-side filter was invalid
        if (!market.player) continue;

        const overOutcome = market.outcomes.find(o => o.description.startsWith('Over'));
        const underOutcome = market.outcomes.find(o => o.description.startsWith('Under'));
        
        // If query filtering works, this check might become redundant, but keep for safety
        if (!overOutcome || !underOutcome) continue; 

        const overOddsDecimal = 1 / (overOutcome.available ?? Infinity);
        const underOddsDecimal = 1 / (underOutcome.available ?? Infinity);

        // Calculate limits based ONLY on orders matching the displayed price
        const tolerance = 1e-6; // Tolerance for float comparison
        let novig_over_limit = 0;
        const targetUnderBidPrice = 1 - (1 / overOddsDecimal);

        // Check if orders exist before iterating
        if (underOutcome.orders && isFinite(targetUnderBidPrice)) { // Orders for UNDER provide liquidity for OVER
            for (const order of underOutcome.orders) {
                if (Math.abs(order.price - targetUnderBidPrice) < tolerance) {
                    novig_over_limit += order.qty * (1 - order.price);
                }
            }
        }

        let novig_under_limit = 0;
        const targetOverBidPrice = 1 - (1 / underOddsDecimal);

        // Check if orders exist before iterating
        if (overOutcome.orders && isFinite(targetOverBidPrice)) { // Orders for OVER provide liquidity for UNDER
            for (const order of overOutcome.orders) {
                 if (Math.abs(order.price - targetOverBidPrice) < tolerance) {
                    novig_under_limit += order.qty * (1 - order.price);
                }
            }
        }

        const overOddsAmerican = decimalToAmerican(overOddsDecimal);
        const underOddsAmerican = decimalToAmerican(underOddsDecimal);

        // Access game details via the path defined in the simplified outcome fragment
        const gameDetails = overOutcome.market?.event?.game; // Use optional chaining
        if (!gameDetails) continue; // Skip if game details path is broken

        const { 
            awayTeam: { symbol: awaySymbol }, 
            homeTeam: { symbol: homeSymbol },
            scheduled_start
        } = gameDetails;

        projections.push({
            proj_id: market.id,
            league,
            player_name: market.player.full_name,
            stat_type: market.type,
            line: market.strike,
            over_odds_american: overOddsAmerican,
            under_odds_american: underOddsAmerican,
            over_odds_decimal: isFinite(overOddsDecimal) ? overOddsDecimal : undefined,
            under_odds_decimal: isFinite(underOddsDecimal) ? underOddsDecimal : undefined,
            novig_over_limit: Math.round(novig_over_limit / 100), // Convert cents to dollars and round
            novig_under_limit: Math.round(novig_under_limit / 100), // Convert cents to dollars and round
            matchup: `${awaySymbol} vs. ${homeSymbol}`,
            start_time: new Date(scheduled_start),
            source: 'Novig'
        });
    }

    return projections;
}


async function makeRequest(query, proxyString) {
    // Parse the proxy string (format: *****************************:port)
    const proxyUrl = new URL(proxyString);
    const [username, password] = proxyUrl.username ? [proxyUrl.username, proxyUrl.password] : proxyUrl.auth.split(':');
    const proxy = {
        host: proxyUrl.hostname,
        port: proxyUrl.port,
        username: decodeURIComponent(username),
        password: decodeURIComponent(password)
    };

    const agent = new HttpsProxyAgent(`http://${proxy.username}:${proxy.password}@${proxy.host}:${proxy.port}`);

    const makeAttempt = async () => {
        return got.post('https://gql.novig.us/v1/graphql', {
            json: query,
            agent: { https: agent },
            headers: {
                'host': 'gql.novig.us',
                'accept': '*/*',
                'content-type': 'application/json',
                'accept-encoding': 'gzip, deflate, br',
            //'sentry-trace': '1ec38ff8af9c4df2804d2df3b12f8bf0-04ee65f001894872-0',
            //'baggage': 'sentry-environment=production,sentry-public_key=40d0e64c8e4e43a9b4e1a717c56171cf,sentry-release=us.novig.app%401.0.31%2B217,sentry-trace_id=1ec38ff8af9c4df2804d2df3b12f8bf0',
                'user-agent': 'Novig/230 CFNetwork/3826.400.120 Darwin/24.3.0',
                'accept-language': 'en-US,en;q=0.9'
            },
            responseType: 'json',
            http2: false,
            https: { rejectUnauthorized: false },
            retry: { limit: 0 }, // Disable got's built-in retry since we're handling it
            timeout: { request: 30000 }
        });
    };

    try {
        return await makeAttempt();
    } catch (error) {
        console.log(`First attempt failed with error: ${error.message}. Retrying once...`);
        // Wait 1 second before retry
        await new Promise(resolve => setTimeout(resolve, 1000));
        // We wrap the retry attempt in a try...catch as well
        try {
            return await makeAttempt();
        } catch (retryError) {
            // Log the error from the second attempt specifically
            console.error(`[NOVIG] Retry attempt failed: ${retryError.message || retryError}`);
            throw retryError; // Re-throw the error from the retry attempt
        }
    }
}

async function fetchLeagueProjections(league, proxy) {
    const initialQuery = {
        operationName: "Home_Query",
        variables: {
            where_event: {
                _and: [
                    {
                        _or: [
                            {
                                _and: [
                                    {
                                        _or: [
                                            // { status: { _eq: "CLOSED_PREGAME" } },
                                            { status: { _eq: "OPEN_PREGAME" } } // Only fetch OPEN_PREGAME initially
                                        ]
                                    },
                                    { is_visible_pregame: { _eq: true } }
                                ]
                            },
                            // {
                            //     _and: [
                            //         {
                            //             _or: [
                            //                 { status: { _eq: "OPEN_INGAME" } },
                            //                 { status: { _eq: "DELAYED" } }
                            //             ]
                            //         },
                            //         { is_visible_live: { _eq: true } }
                            //     ]
                            // } // Commenting out live game fetching for now, as extractProjections only handles OPEN_PREGAME
                        ]
                    },
                    { game: { league: { _eq: league } } }
                ]
            },
            order_by_event: [
                { game: { scheduled_start: "asc" } },
                { game: { id: "asc" } }
            ],
            limit_count: 10000 // Assuming this is sufficient, adjust if needed
        },
        query: EVENTS_QUERY
    };

    try {
        // 1. Fetch all relevant event IDs first
        const eventsResponse = await makeRequest(initialQuery, proxy);

        // Check for fundamental request issues or GraphQL errors in the initial fetch
        if (!eventsResponse || !eventsResponse.body) {
            console.error(`[NOVIG] Invalid or empty response received for ${league} event list.`);
            return [];
        }
        if (eventsResponse.body.errors) {
            console.error(`[NOVIG] GraphQL errors fetching ${league} event list:`, JSON.stringify(eventsResponse.body.errors));
            return [];
        }
        if (!eventsResponse.body.data || !eventsResponse.body.data.event) {
            console.error(`[NOVIG] Invalid data structure received for ${league} event list (missing data.event). Response body:`, JSON.stringify(eventsResponse.body));
            return [];
        }

        const eventIds = eventsResponse.body.data.event.map(e => e.id);

        if (eventIds.length === 0) {
            // console.log(`[NOVIG] No active OPEN_PREGAME events found for ${league}.`);
            return [];
        }

        let allLeagueProjections = [];
        const batchSize = 20; // Increased batch size slightly, as we removed retry delays

        // Helper function to process a single result 
        const processResult = (detailResponse, eventId, league) => {
            // Check for empty/invalid response body first
            if (!detailResponse || !detailResponse.body) {
                console.warn(`[NOVIG][${eventId}] Received empty or invalid detail response body.`);
                return; 
            }

            // Check for GraphQL errors
            if (detailResponse.body.errors) {
                 // Find timeout errors specifically for logging
                const timeoutError = detailResponse.body.errors.find(err => 
                    err.extensions?.code === 'time-limit-exceeded' || 
                    err.message?.includes('time limit')
                );
                if (timeoutError) {
                    console.warn(`[NOVIG][${eventId}] GraphQL timeout error: ${timeoutError.message}`);
                    // Logged the timeout, but proceed to check structure / potentially extract partial data
                } else {
                    // Log other GraphQL errors
                    console.warn(`[NOVIG][${eventId}] Non-timeout GraphQL errors:`, JSON.stringify(detailResponse.body.errors));
                }
            }

            // Check core data structure AFTER checking for errors
            if (!detailResponse.body.data || !detailResponse.body.data.event || detailResponse.body.data.event.length === 0) {
                 if (!detailResponse.body.errors) {
                     console.warn(`[NOVIG][${eventId}] Invalid data structure or no event data found. Body:`, JSON.stringify(detailResponse.body));
                 }
                return; // Cannot extract
            }

            // Attempt to extract projections if data structure looks ok
            try {
                const eventProjections = extractProjections(detailResponse.body, league);
                allLeagueProjections = allLeagueProjections.concat(eventProjections);
            } catch (extractionError) {
                console.error(`[NOVIG][${eventId}] Error during projection extraction: ${extractionError.message}`, { responseBody: detailResponse.body });
            }
        };

        // 2. Process event IDs in batches
        for (let i = 0; i < eventIds.length; i += batchSize) {
            const batchEventIds = eventIds.slice(i, i + batchSize);
            // console.log(`[NOVIG] Processing batch ${Math.floor(i / batchSize) + 1} for ${league} (Events ${i + 1}-${Math.min(i + batchSize, eventIds.length)} of ${eventIds.length})`);

            // 3. Create promises for fetching details for the current batch
            const batchDetailPromises = batchEventIds.map(eventId => {
                const detailQuery = {
                    operationName: "SimplifiedEventDetails_Query",
                    variables: { eventId },
                    query: SIMPLIFIED_EVENT_DETAILS_QUERY
                };
                return makeRequest(detailQuery, proxy).catch(error => {
                    error.eventId = eventId; // Attach eventId for context
                    throw error;
                });
            });

            // 4. Execute the batch requests concurrently
            const batchResults = await Promise.allSettled(batchDetailPromises);

            // 5. Process the results (no retry logic)
            batchResults.forEach((result, index) => {
                const eventId = batchEventIds[index];
                if (result.status === 'fulfilled') {
                    processResult(result.value, eventId, league);
                } else {
                    // Initial request promise was rejected (Network error, etc.)
                    console.error(`[NOVIG][${eventId}] Network/request error: ${result.reason.message || String(result.reason)}`);
                }
            });
            
            // Optional delay between batches if necessary
            // await new Promise(resolve => setTimeout(resolve, 200)); 
        }

        return allLeagueProjections;

    } catch (error) {
        // Catch errors from the initial event ID fetch
        console.error(`[NOVIG] Top-level error fetching ${league} projections: ${error.message}`);
        return []; // Return empty array on fundamental errors
    }
}

async function fetchNovig() {
    const SUPPORTED_LEAGUES = ['NBA', 'WNBA', 'NFL', 'MLB'];

    try {
        const proxy = await getProxy();
        let allProjections = [];

        for (const league of SUPPORTED_LEAGUES) {
            const leagueProjections = await fetchLeagueProjections(league, proxy);
            allProjections = allProjections.concat(leagueProjections);
        }

        return allProjections;
    } catch (error) {
        console.error(`[NOVIG] ${error.message}`);
        throw error;
    }
}

module.exports = { fetchNovig };