const { fetch, ProxyAgent } = require('undici');
const { getProxy, reportProxySuccess, reportProxyFailure } = require('../utils/proxy');

// Performance optimization constants
const PERFORMANCE_CONFIG = {
    MAX_EXECUTION_TIME: 6000, // 6 seconds max (target: 5s with buffer)
    REQUEST_TIMEOUT: 8000, // Reduced from 30s to 8s
    MAX_RETRIES: 2, // Reduced from 3 to 2
    RETRY_DELAY: 300, // Reduced from 1000ms to 300ms
    EXPONENTIAL_BACKOFF: false // Disable exponential backoff for faster retries
};

async function fetchWithRetry(endpoint, startTime, maxRetries = PERFORMANCE_CONFIG.MAX_RETRIES) {
  let lastError = null;
  let proxyUrl = null;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // Check if we're approaching time limit
      const elapsed = Date.now() - startTime;
      if (elapsed > PERFORMANCE_CONFIG.MAX_EXECUTION_TIME * 0.8) { // 80% of max time
        console.warn(`[Underdog] Approaching time limit (${elapsed}ms), stopping retries`);
        break;
      }

      proxyUrl = await getProxy('Underdog');
      console.log(`[Underdog] Attempt ${attempt + 1}/${maxRetries} using proxy: ${proxyUrl.replace(/:[^:@]+@/, ':****@')}`);

      const proxyAgent = new ProxyAgent(proxyUrl);
      const controller = new AbortController();
      const timeout = setTimeout(() => controller.abort(), PERFORMANCE_CONFIG.REQUEST_TIMEOUT);

      const response = await fetch(endpoint, {
        dispatcher: proxyAgent,
        signal: controller.signal,
        headers: {
          'accept': 'application/json',
          'accept-language': 'en-US,en;q=0.9',
          'user-agent': 'UnderdogFantasy/1.0.0 (iPhone; iOS 15.0; Scale/3.00)',
        }
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
        reportProxyFailure(proxyUrl, error);
        throw error;
      }

      // Success - report proxy success and return data
      reportProxySuccess(proxyUrl);
      return await response.json();

    } catch (error) {
      console.log(`[Underdog] Attempt ${attempt + 1} failed: ${error.message}`);
      lastError = error;

      // Report proxy failure if we have a proxy URL
      if (proxyUrl) {
        reportProxyFailure(proxyUrl, error);
      }

      // If we're not on the last attempt, wait before retrying (optimized delay)
      if (attempt < maxRetries - 1) {
        const delay = PERFORMANCE_CONFIG.EXPONENTIAL_BACKOFF
          ? PERFORMANCE_CONFIG.RETRY_DELAY * (attempt + 1)
          : PERFORMANCE_CONFIG.RETRY_DELAY;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

async function fetchUnderdog() {
  const endpoint = 'https://api.underdogfantasy.com/beta/v6/over_under_lines';
  const startTime = Date.now();

  try {
    console.log('[Underdog] Starting optimized fetch...');
    const data = await fetchWithRetry(endpoint, startTime);
    const { over_under_lines, appearances, games, players, solo_games } = data;

    return over_under_lines.map(line => {
      if (!line.options) {
        return null;
      }

      const appearance = appearances.find(ap => ap.id === line.over_under.appearance_stat.appearance_id);
      if (!appearance) return null;

      let game;
      if (appearance.match_type === "SoloGame") {
        // Search for the game in solo_games if match_type is SoloGame
        game = solo_games.find(g => g.id === appearance.match_id);
      } else {
        game = games.find(g => g.id === appearance.match_id);
      }

      if (!game || !players) return null;
      const player = players.find(p => p.id === appearance.player_id);
      if (!player) return null;

      // Validate against the sport_id in the game object to ensure correct data set usage
      if (!['CBB', 'PGA', 'GOLF', 'MMA', 'NHL', 'FIFA', 'MLB', 'BASKETBALL', 'SOCCER', 'WNBA', 'LAX', 'TENNIS', 'CRICKET', 'HR', 'OLYMPIC_BASKETBALL', 'O_SOCCER', 'OLYMPIC_GOLF', 'CFB', 'NFL', 'NBA'].includes(game.sport_id)) return null;

      let player_name = `${player.first_name} ${player.last_name}`;
      let league = game.sport_id;
      // Define a mapping of prefixes
      const prefixMapping = {
        '1Q': '1Q',
        '1H': '1H',
        '2H': '2H',
        // Add more prefixes here if needed
      };
      let matchup = game.title.replace(' @ ', ' vs. ');

      let over_odds_decimal = null;
      let under_odds_decimal = null;
      let over_odds_american = null;
      let under_odds_american = null;

      line.options.forEach(option => {
        const payout_multiplier = parseFloat(option.payout_multiplier);
        let decimal_odds = payout_multiplier === 1.0 ? 1.86 : payout_multiplier * 1.86;
        const american_odds = decimalToAmerican(decimal_odds);

        if (option.choice_display === "Higher") {
          over_odds_decimal = decimal_odds;
          over_odds_american = american_odds;
        } else if (option.choice_display === "Lower") {
          under_odds_decimal = decimal_odds;
          under_odds_american = american_odds;
        }
      });

      let stat_type = line.over_under.appearance_stat.display_stat;

      if (game.sport_id === 'ESPORTS') {
        player_name = player.last_name;
        if (player_name.includes('+')) {
          return null;
        }
        league = player.first_name.replace(/:$/, '');
        if (league === 'Dota') {
          league = 'DOTA2';
        }
        if (league === 'CS') {
          league = 'CS2';
        }
        matchup = game.title.replace(/^.*?:\s*/, '');
      } else {
        player_name = `${player.first_name} ${player.last_name}`;
        if (player_name.includes('+')) {
          return null;
        }
        league = game.sport_id;

        // Iterate over the mapping to apply prefixes
        Object.keys(prefixMapping).forEach(prefix => {
          if (stat_type.includes(prefix)) {
            if (!league.endsWith(prefixMapping[prefix])) {
              league += prefixMapping[prefix];
            }
            stat_type = stat_type.replace(prefix, '').trim();
          }
        });

        switch (league) {
          case 'FIFA':
          case 'WSOCCER':
          case 'O_SOCCER':
            league = 'SOCCER';
            break;
          case 'BASKETBALL':
            league = 'EURO';
            break;
          case 'CBB':
            league = 'NCAAB';
            break;
          case 'PGA':
          case 'OLYMPIC_GOLF':
            league = 'GOLF';
            break;
          case 'HR':
            league = 'HRDERBY';
            break;
          case 'OLYMPIC_BASKETBALL':
            league = 'OBBALL';
            break;
        }
        matchup = game.title.replace(' @ ', ' vs. ');
      }

      const startTime = new Date(game.scheduled_at);

      return {
        proj_id: line.id,
        league: league.toUpperCase(),
        player_name: player_name,
        stat_type: stat_type,
        line: parseFloat(line.stat_value),
        over_odds_american: over_odds_american,
        under_odds_american: under_odds_american,
        over_odds_decimal: over_odds_decimal,
        under_odds_decimal: under_odds_decimal,
        matchup: matchup,
        start_time: startTime,
        is_alt: (over_odds_decimal !== 1.86 && under_odds_decimal !== 1.86),
        source: 'Underdog',
      };
    })
      .filter(projection => projection !== null)
      .filter(projection => projection.over_odds_american !== null && projection.under_odds_american !== null);

    const duration = Date.now() - startTime;
    console.log(`[Underdog] Completed fetch in ${duration}ms with ${projections.length} projections`);

    // Log performance warning if over target
    if (duration > PERFORMANCE_CONFIG.MAX_EXECUTION_TIME) {
      console.warn(`[Underdog] Performance warning: Execution took ${duration}ms (target: ${PERFORMANCE_CONFIG.MAX_EXECUTION_TIME}ms)`);
    }

    return projections;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Underdog] Failed after ${duration}ms: ${error.message}`);
    return [];
  }
}

function decimalToAmerican(decimal) {
  if (decimal >= 2) {
    return `+${Math.round((decimal - 1) * 100)}`;
  } else {
    return `-${Math.round(100 / (decimal - 1))}`;
  }
}

module.exports = { fetchUnderdog };
