const fetch = (...args) =>
  import('node-fetch').then(({ default: fetch }) => fetch(...args));
const fs = require('fs');
const { getProxy } = require('../utils/proxy');
const { ProxyAgent } = require('undici');

// Constants for Kambi API (Updated based on manual URL)
const KAMBI_BASE_URL = 'https://eu1.offering-api.kambicdn.com/offering/v2018/pivusinrl-law'; // Changed provider key
const MARKET = 'US'; // Changed market to US based on manual URL
const LANG = 'en_US';
// Add other potentially necessary query params from the manual URL
const CLIENT_ID = '2';
const CHANNEL_ID = '7';

// Function to format American odds
const formatAmericanOdds = (odds) => odds.startsWith('-') ? odds : `+${odds}`;

// Map of leagues to categories and URLs
const leagues = {
    NBA: {
        POINTS: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=**********&pageNr=1&pageSize=10',
        REBOUNDS: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=**********&marketCategory=TOTAL_REBOUNDS&pageNr=1&pageSize=10',
        ASSISTS: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=**********&marketCategory=TOTAL_ASSISTS&pageNr=1&pageSize=10',
        PRA: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=**********&marketCategory=TOTAL_PTS_AST_REB&pageNr=1&pageSize=10',
        THREE_PT: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=**********&marketCategory=3PT_FG_MADE&pageNr=1&pageSize=10',
    },
    CBB: {
        POINTS: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=1000093654&pageNr=1&pageSize=10',
        REBOUNDS: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=1000093654&marketCategory=TOTAL_REBOUNDS&pageNr=1&pageSize=10',
        ASSISTS: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=1000093654&marketCategory=TOTAL_ASSISTS&pageNr=1&pageSize=10',
        PRA: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=1000093654&marketCategory=TOTAL_PTS_AST_REB&pageNr=1&pageSize=10',
        THREE_PT: 'https://nj.betrivers.com/apinj/service/sportsbook/offering/playerprops?t=20241211920&cageCode=2&groupId=1000093654&marketCategory=3PT_FG_MADE&pageNr=1&pageSize=10',
    },
    MLB: {
        // We won't use these URLs directly anymore
    },
};

// Function to format player name (from Kambi format)
function formatKambiPlayerName(name) {
    if (!name || typeof name !== 'string') return 'Unknown Player';
    const parts = name.split(',').map(n => n.trim());
    // Normalize and remove accents if necessary, similar to BetRivers.ts
    const normalizedParts = parts.map(n => n.normalize('NFD').replace(/[\u0300-\u036f]/g, ''));
    return normalizedParts.length > 1 ? `${normalizedParts[1]} ${normalizedParts[0]}` : normalizedParts[0];
}

// Function to get stat type from Kambi criterion label for MLB
function getMlbStatType(criterionLabel) {
    const labelLower = criterionLabel.toLowerCase();
    if (labelLower.includes('total hits by batter')) return 'HITS';
    if (labelLower.includes('total bases')) return 'BASES'; // Assuming 'total bases' is used
    if (labelLower.includes('total runs scored')) return 'RUNS';
    if (labelLower.includes('strikeouts thrown')) return 'K'; // For pitchers
    // Add more mappings if needed based on actual API data
    return null; // Return null if no relevant stat type is found
}

// Function to fetch and process data for a given URL (OLD METHOD - keep for NBA/CBB for now)
const fetchDataAndProcess = async (url, leagueName, statType) => {
    try {
        const proxy = getProxy('BetRivers');
        const proxyAgent = new ProxyAgent(proxy);

        const response = await fetch(url, {
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            },
            dispatcher: proxyAgent
        });
        const data = await response.json();
        // Check if the items array is not empty
        if (data.items && data.items.length > 0) {
            const projections = data.items.flatMap(game => {
                // Process each game's betOffers if available
                return game.betOffers.map(offer => {
                    // Find the OVER and UNDER outcomes
                    const overOutcome = offer.outcomes.find(outcome => outcome.type === "OVER");
                    const underOutcome = offer.outcomes.find(outcome => outcome.type === "UNDER");

                    // Check if both outcomes exist before accessing their properties
                    if (!overOutcome || !underOutcome) {
                        // console.warn(`Missing OVER or UNDER outcome for ${leagueName} - ${statType}`);
                        return null;
                    }

                    // Formatting the start time
                    const eventStartTime = new Date(game.start);

                    return {
                        proj_id: offer.id.toString(),
                        league: leagueName,
                        // Use the old player name format for this data structure
                        player_name: offer.participant.split(', ').reverse().join(' '),
                        stat_type: statType,
                        line: overOutcome.line,
                        over_odds_american: formatAmericanOdds(overOutcome.oddsAmerican),
                        over_odds_decimal: overOutcome.odds,
                        under_odds_american: formatAmericanOdds(underOutcome.oddsAmerican),
                        under_odds_decimal: underOutcome.odds,
                        matchup: game.name,
                        start_time: eventStartTime,
                        source: 'BetRivers'
                    };
                });
            }).filter(Boolean); // Remove any null entries

            return projections;
        } else {
            // Log or handle the case where items array is empty
            console.log(`[BetRivers Legacy] No items to process for ${leagueName} - ${statType}`);
            return [];
        }
    } catch (error) {
        console.error('[BetRivers Legacy] Error fetching data for', leagueName, statType, ':', error);
        return []; // Return an empty array instead of throwing
    }
};

// Function to format player name (from Kambi format: "Last, First")
function formatKambiPlayerName(name) {
    if (!name || typeof name !== 'string') return 'Unknown Player';
    const parts = name.split(',').map(n => n.trim());
    // Normalize and remove accents
    const normalizedParts = parts.map(n => n.normalize('NFD').replace(/[\u0300-\u036f]/g, ''));
    return normalizedParts.length > 1 ? `${normalizedParts[1]} ${normalizedParts[0]}` : normalizedParts[0];
}

/**
 * Parses the Kambi criterion label to extract a potential player name and a simplified stat label.
 * This is a best-effort parsing and might need refinement based on more label variations.
 * @param {string} label - The criterion label (e.g., "Total Hits by the Player - ... (McMahon, Ryan)")
 * @param {object} firstOutcome - The first outcome object, often containing the participant name.
 * @returns {object|null} - An object { playerName: string, statLabel: string } or null if parsing fails.
 */
function parseMlbPlayerPropInfo(label, firstOutcome) {
    if (!label || typeof label !== 'string' || !firstOutcome || !firstOutcome.participant) {
        return null;
    }

    const playerName = formatKambiPlayerName(firstOutcome.participant);
    if (playerName === 'Unknown Player') return null;

    let statLabel = label;

    // Attempt to clean up the label
    statLabel = statLabel.replace(`(${firstOutcome.participant})`, '').trim(); // Remove participant in brackets
    statLabel = statLabel.replace(/ - Including Extra Innings/i, '').trim();
    statLabel = statLabel.replace(/\(Listed player must be in starting lineup for bets to stand\)/i, '').trim();
    statLabel = statLabel.replace(/ by the Player/i, '').trim(); // "Total Hits by the Player" -> "Total Hits"
    statLabel = statLabel.replace(/Player to /i, '').trim(); // "Player to Hit a Home Run" -> "Hit a Home Run"
    statLabel = statLabel.replace(/thrown by the Player/i, '').trim(); // "Strikeouts thrown by the Player" -> "Strikeouts"

    // Specific keyword replacements for consistency
    if (statLabel.toLowerCase().includes('hit a home run')) statLabel = 'Home Runs';
    if (statLabel.toLowerCase().includes('total hits')) statLabel = 'Hits';
    if (statLabel.toLowerCase().includes('total bases')) statLabel = 'Total Bases';
    if (statLabel.toLowerCase().includes('strikeouts thrown')) statLabel = 'Strikeouts';
    if (statLabel.toLowerCase().includes('total rbis')) statLabel = 'RBIs';
    if (statLabel.toLowerCase().includes('total runs scored')) statLabel = 'Runs Scored';
    if (statLabel.toLowerCase().includes('total doubles')) statLabel = 'Doubles';
    if (statLabel.toLowerCase().includes('total stolen bases')) statLabel = 'Stolen Bases';
     // Add more specific mappings if needed

    // Further cleanup for labels like "Player to hit 2 or more Home Runs"
     const multiHrMatch = statLabel.match(/hit (\d+) or more Home Runs/i);
     if (multiHrMatch) {
         statLabel = `Home Runs (${multiHrMatch[1]}+)`; // e.g., "Home Runs (2+)"
     }


    // Remove potential trailing numbers or hyphens if they seem like part of Kambi's formatting
    statLabel = statLabel.replace(/ - \d+$/, '').trim();

    return { playerName, statLabel };
}

// Function to fetch detailed projection data for a single MLB event
async function fetchMlbProjectionData(eventId, eventData) {
    const url = `${KAMBI_BASE_URL}/betoffer/event/${eventId}.json?lang=${LANG}&market=${MARKET}&ncid=${Date.now()}`; // Added ncid cache buster
    const projections = [];
    const uniqueProjections = new Map(); // Key: playerName_statLabel_line

    try {
        const proxy = getProxy('BetRivers');
        const proxyAgent = new ProxyAgent(proxy);
        const response = await fetch(url, { dispatcher: proxyAgent });

        if (!response.ok) {
            console.warn(`[BetRivers Kambi MLB] Network response error for event ${eventId}: ${response.status}`);
            return [];
        }
        const data = await response.json();

        if (!data.betOffers || !Array.isArray(data.betOffers)) {
            console.warn(`[BetRivers Kambi MLB] Invalid or missing betOffers for event ${eventId}`);
            return [];
        }

        data.betOffers.forEach(offer => {
            // Basic validation
            if (!offer || typeof offer.id !== 'number' || !offer.criterion || typeof offer.criterion.label !== 'string' || !Array.isArray(offer.outcomes) || offer.outcomes.length < 2 || !offer.betOfferType) {
                return; // Skip malformed offer
            }

            const betOfferTypeName = offer.betOfferType.name;
            const firstOutcome = offer.outcomes[0]; // Use first outcome to get participant

             // Check if it's a potential player prop (has participant info in outcomes)
            if (!firstOutcome || !firstOutcome.participant) {
                return; // Skip if no participant found (likely not a player prop)
            }

             // --- Try Parsing Player Name and Stat Label ---
             const propInfo = parseMlbPlayerPropInfo(offer.criterion.label, firstOutcome);
             if (!propInfo) {
                 // console.log(`Could not parse prop info for label: ${offer.criterion.label}`); // Optional logging
                 return; // Skip if we couldn't parse player/stat
             }
             const { playerName, statLabel } = propInfo;


            // --- Handle Over/Under style markets ---
            if (betOfferTypeName === 'Over/Under' || (betOfferTypeName === 'Player Occurrence Line' && offer.outcomes.some(o => o.type === 'OT_OVER'))) {
                const overOutcome = offer.outcomes.find(o => o.type === 'OT_OVER' && o.participant === firstOutcome.participant && o.status === 'OPEN');
                const underOutcome = offer.outcomes.find(o => o.type === 'OT_UNDER' && o.participant === firstOutcome.participant && o.status === 'OPEN');

                if (overOutcome && underOutcome && overOutcome.line !== undefined && overOutcome.line === underOutcome.line && overOutcome.odds && underOutcome.odds) {
                    const line = overOutcome.line / 1000;
                    const projectionKey = `${playerName}_${statLabel}_${line}`;

                    if (!uniqueProjections.has(projectionKey)) {
                        const projection = {
                            proj_id: `${offer.id}_${overOutcome.id}`,
                            league: 'MLB',
                            player_name: playerName,
                            stat_type: statLabel, // Use the parsed label
                            line: line,
                            over_odds_american: formatAmericanOdds(overOutcome.oddsAmerican),
                            over_odds_decimal: overOutcome.odds / 1000,
                            under_odds_american: formatAmericanOdds(underOutcome.oddsAmerican),
                            under_odds_decimal: underOutcome.odds / 1000,
                            matchup: eventData.name,
                            start_time: new Date(eventData.start),
                            source: 'BetRivers'
                        };
                        projections.push(projection);
                        uniqueProjections.set(projectionKey, true);
                    }
                }
            }
            // --- Handle Yes/No style markets (Treat Yes as Over, No as Under) ---
            else if (betOfferTypeName === 'Yes/No' || (betOfferTypeName === 'Player Occurrence Line' && offer.outcomes.some(o => o.type === 'OT_YES'))) {
                 const yesOutcome = offer.outcomes.find(o => o.type === 'OT_YES' && o.participant === firstOutcome.participant && o.status === 'OPEN');
                 const noOutcome = offer.outcomes.find(o => o.type === 'OT_NO' && o.participant === firstOutcome.participant && o.status === 'OPEN'); // Require NO to be OPEN too

                 // Ensure both exist and are open. Use line from 'yes' outcome, default to 0.5 if missing/invalid
                 if (yesOutcome && noOutcome && yesOutcome.odds && noOutcome.odds) {
                     let line;
                     // Kambi uses line for thresholds like "2+ HRs" (line: 2000) or "1+ HR" (line: 1000 or 500)
                     if (yesOutcome.line !== undefined && yesOutcome.line > 0) {
                          // If line is 1000 for "1+ HR", effective line is 0.5. If 2000 for "2+ HR", effective line is 1.5.
                         // A line of 500 seems to mean 0.5 directly sometimes. Let's assume line/1000 is the threshold value for Over X.
                         // Example: "2+ HR" (line 2000) means Over 1.5 HRs.
                         // Example: "1+ HR" (line 1000) means Over 0.5 HRs.
                         // Example: "1+ HR" (line 500) means Over 0.5 HRs? Seems redundant but possible.
                         // Safest bet: use line/1000 - 0.5 IF line >= 1000, otherwise use line/1000 directly IF line = 500?
                         // Let's simplify: use `(line / 1000) - 0.5` seems most consistent for "X or more" props.
                         // Let's test with `line/1000` first as the direct threshold for "Over X". Home Run 1.0 == Over 0.5. Home Run 2.0 == Over 1.5? No, Kambi line usually IS the threshold.
                         // Line 500 = 0.5, Line 1500 = 1.5 etc. Seems correct.
                         line = yesOutcome.line / 1000;
                     } else {
                         // Default for simple Yes/No props like "To Record a Hit" or "To Hit a Home Run"
                         line = 0.5;
                     }

                     const projectionKey = `${playerName}_${statLabel}_${line}`;

                     if (!uniqueProjections.has(projectionKey)) {
                         const projection = {
                             proj_id: `${offer.id}_${yesOutcome.id}`,
                             league: 'MLB',
                             player_name: playerName,
                             stat_type: statLabel, // Use the parsed label
                             line: line,
                             over_odds_american: formatAmericanOdds(yesOutcome.oddsAmerican), // YES -> OVER
                             over_odds_decimal: yesOutcome.odds / 1000,
                             under_odds_american: formatAmericanOdds(noOutcome.oddsAmerican), // NO -> UNDER
                             under_odds_decimal: noOutcome.odds / 1000,
                             matchup: eventData.name,
                             start_time: new Date(eventData.start),
                             source: 'BetRivers'
                         };
                         projections.push(projection);
                         uniqueProjections.set(projectionKey, true);
                     }
                 }
             }
        });

        return projections;
    } catch (error) {
        console.error(`[BetRivers Kambi MLB] Failed to fetch or process projections for event ${eventId}:`, error);
        return [];
    }
}


// Function to fetch all MLB events and their projection data
async function fetchAllMlbData() {
    const queryParams = new URLSearchParams({
        lang: LANG,
        market: MARKET,
        client_id: CLIENT_ID,
        channel_id: CHANNEL_ID,
        ncid: Date.now().toString(), // Cache buster
        useCombined: 'true',
        // Add other params if necessary, e.g., competition=... for specific leagues if needed
    });
     // Use the specific MLB group ID (found via network inspector or prior knowledge)
     const mlbGroupId = 1000093616;
     const url = `${KAMBI_BASE_URL}/listView/baseball/mlb.json?${queryParams.toString()}`;
     // Alternative: const url = `${KAMBI_BASE_URL}/listView/all/all/all/all/competitions/${mlbGroupId}.json?${queryParams.toString()}`;
     // Alternative 2: const url = `${KAMBI_BASE_URL}/listView/baseball/united_states/mlb.json?${queryParams.toString()}`; // This seems most likely based on common patterns

    console.log(`[BetRivers Kambi MLB] Fetching MLB events from: ${url}`);
    try {
        const proxy = getProxy('BetRivers');
        const proxyAgent = new ProxyAgent(proxy);
        const response = await fetch(url, { dispatcher: proxyAgent });

        if (!response.ok) {
            throw new Error(`[BetRivers Kambi MLB] Failed to fetch MLB events list: ${response.status} ${response.statusText} from ${url}`);
        }
        const data = await response.json();

        // Adjusting based on potential `listView` response structure
        const eventsToProcess = data.events || (data.liveEvents || []).concat(data.events || []); // Combine live and pre-match if structure varies

        if (!eventsToProcess || !Array.isArray(eventsToProcess) || eventsToProcess.length === 0) {
            console.warn('[BetRivers Kambi MLB] No events found in MLB response using URL:', url);
            return [];
        }

        console.log(`[BetRivers Kambi MLB] Found ${eventsToProcess.length} MLB events.`);

        const projectionPromises = eventsToProcess.map(eventContainer => {
            // The actual event data might be nested under 'event'
            const event = eventContainer.event || eventContainer;
            if (!event || typeof event.id !== 'number' || typeof event.name !== 'string' || typeof event.start !== 'string') {
                console.warn('[BetRivers Kambi MLB] Skipping malformed event container:', eventContainer);
                return Promise.resolve([]);
            }
            const { id, name, start } = event;
            // Filter out events that have already started if needed, though Kambi might handle this
             if (new Date(start) < new Date()) {
                 // console.log(`Skipping already started event: ${name}`);
                 return Promise.resolve([]);
             }
            return fetchMlbProjectionData(id.toString(), { id, name, start });
        });


        const results = await Promise.allSettled(projectionPromises);
        const allMlbProjections = results
            .filter(result => result.status === 'fulfilled' && Array.isArray(result.value))
            .flatMap(result => result.value);

        console.log(`[BetRivers Kambi MLB] Successfully processed ${allMlbProjections.length} MLB projections.`);
        return allMlbProjections;

    } catch (error) {
        console.error('[BetRivers Kambi MLB] Error fetching or processing MLB events:', error);
        return [];
    }
}

// Main function to process all leagues
const fetchBetRivers = async () => {
    console.log('[BetRivers] Starting fetch...');
    const fetchPromises = [];

    // --- Add Kambi MLB Fetch ---
    fetchPromises.push(fetchAllMlbData());

    // --- Keep Legacy Fetch for NBA & CBB ---
    Object.entries(leagues).forEach(([leagueName, categories]) => {
        if (leagueName !== 'MLB') {
             Object.entries(categories).forEach(([statType, url]) => {
                 fetchPromises.push(fetchDataAndProcess(url, leagueName, statType));
             });
        }
     });

    const results = await Promise.allSettled(fetchPromises);

    const allProjections = results
      .filter(result => result.status === 'fulfilled' && Array.isArray(result.value)) // Check isArray
      .flatMap(result => result.value);

    console.log(`[BetRivers] Completed fetch. Total projections: ${allProjections.length}`);
    // fs.writeFileSync('betrivers_debug_output.json', JSON.stringify(allProjections, null, 2)); // Optional: Write output for debugging
    return allProjections;
};

module.exports = { fetchBetRivers };