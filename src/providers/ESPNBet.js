const { ESPNBetAPI } = require('../api/espnbet');

async function fetchESPNBet() {
    try {
        const api = new ESPNBetAPI();
        //console.log('Fetching ESPNBet projections...');
        const projections = await api.fetchESPNBet();
        //console.log(`Fetched ${projections.length} projections.`);
        return projections;
    } catch (error) {
        console.error('[ESPNBET] Error fetching ESPNBet projections:', error);
        throw error; // Re-throw the error to be handled by the caller
    }
}

module.exports = { fetchESPNBet };