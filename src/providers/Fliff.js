const FliffAPI = require('../api/fliff-api');
//const fs = require('fs');
//const path = require('path');

async function fetchFliff() {
    const fliffApi = new FliffAPI();

    try {
        const allProjections = await fliffApi.fetchFliff();
        //console.log('Finished fetching game data for all leagues.');

        // Define the path where the JSON file will be saved
        //const filePath = path.join(__dirname, 'fliffProjections.json');

        // Write the projections to a JSON file
        //fs.writeFileSync(filePath, JSON.stringify(allProjections, null, 2));

        //console.log(`Projections saved to ${filePath}`);
        return allProjections;
    } catch (error) {
        console.error('[FLIFF] An error occurred while fetching game data:', error);
        throw error;  // Re-throw the error so it can be handled by the caller
    }
}

module.exports = { fetchFliff };
