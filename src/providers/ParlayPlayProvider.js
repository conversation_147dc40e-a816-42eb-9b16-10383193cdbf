const fetch = require("node-fetch");
const HttpsProxyAgent = require('https-proxy-agent');
const { getProxy } = require('../utils/proxy');
const { decimalToAmerican } = require("../utils/ProjectionUtility");

// Function to extract projections from the response
function extractProjections(response, league) {
    // Check if league is one of UEL, SERIEA, or MLS and set to SOCCER if true
    const adjustedLeague = ['UEL', 'SERIEA', 'MLS', 'EPL', 'LALIGA', 'LIGUE1', 'MX', 'BUNDES', 'COPA', 'EURO'].includes(league) ? 'SOCCER' : (league === 'UFC' ? 'MMA' : league);

    const projections = [];
    response.players.forEach(player => {
        const playerName = player.player.fullName;
        const matchup = `${player.match.awayTeam.teamAbbreviation} vs. ${player.match.homeTeam.teamAbbreviation}`;

        // formatted start time
        const startTime = new Date(player.match.matchDate);

        player.stats.forEach(stat => {
            // Find the altLine that matches the statValue
            const matchingAltLine = stat.altLines.values.find(projection => projection.selectionPoints === stat.statValue);

            // Proceed only if a matching altLine is found
            if (matchingAltLine && matchingAltLine.decimalPriceOver !== 0 && matchingAltLine.decimalPriceUnder !== 0) {
                const projId = `${playerName}_${stat.challengeName}_${matchingAltLine.selectionPoints}_${adjustedLeague}`;
                const stat_type = stat.challengeName;
                const source = 'ParlayPlay';
                let leagueName = adjustedLeague;
                if (leagueName === 'LIV') {
                    leagueName = 'GOLF';
                }
                if (leagueName === 'PGA') {
                    leagueName = 'GOLF';
                }
                if (leagueName === 'UFC') {
                    leagueName = 'MMA';
                }
                projections.push({
                    proj_id: projId,
                    league: adjustedLeague, // Use adjusted league
                    player_name: playerName,
                    stat_type: stat_type,
                    line: matchingAltLine.selectionPoints,
                    over_odds_decimal: matchingAltLine.decimalPriceOver,
                    under_odds_decimal: matchingAltLine.decimalPriceUnder,
                    over_odds_american: decimalToAmerican(matchingAltLine.decimalPriceOver),
                    under_odds_american: decimalToAmerican(matchingAltLine.decimalPriceUnder),
                    matchup: matchup,
                    start_time: startTime,
                    source: source
                });
            }
        });
    });
    return projections;
}



// Function to asynchronously fetch and process data for all leagues
export async function fetchParlayPlay() {
    const proxy = await getProxy();
    const agent = new HttpsProxyAgent(`http://${proxy.username}:${proxy.password}@${proxy.host}:${proxy.port}`);

    const options = {
        method: 'GET',
        headers: {
            'content-type': 'application/json',
            'x-parlay-request': '1',
            'x-requested-with': 'XMLHttpRequest',
            'user-agent': 'ParlayPlay/7 CFNetwork/1474 Darwin/23.0.0',
            'X-Csrftoken': 'CMNfbGhBGl11Aj6im1t0YtcOpGwiuZbLP1HsquPLZ5PwmP8lMDdkH9PtFN5q8SKY'
        },
        agent
    };

    // URLs for SPORTS
    const urls = {
        //CBB: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Basketball&league=CBB&includeAlt=true',
        MMA: 'https://parlayplay.io/api/v1/crossgame/search/?sport=MMA&league=UFC&includeAlt=true',
        PGA: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Golf&league=PGA&includeAlt=true',
        //NBA: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Basketball&league=NBA&includeAlt=true',
        WNBA: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Basketball&league=WNBA&includeAlt=true',
        MLB: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Baseball&league=MLB&includeAlt=true',
        NHL: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Hockey&league=NHL&includeAlt=true',
        UEL: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=UEL&includeAlt=true',
        SERIEA: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=SerieA&includeAlt=true',
        MLS: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=MLS&includeAlt=true',
        EPL: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=EPL&includeAlt=true',
        LALIGA: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=LaLiga&includeAlt=true',
        LIGUE1: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=Ligue1&includeAlt=true',
        MX: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=MX&includeAlt=true',
        BUNDES: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=Bundes&includeAlt=true',
        EURO: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=EURO&includeAlt=true',
        COPA: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Soccer&league=COPA&includeAlt=true',
        HRDERBY: 'https://parlayplay.io/api/v1/crossgame/search/?sport=Baseball&league=HR&includeAlt=true'
    };

    const fetchPromises = Object.entries(urls).map(([league, url]) =>
        fetch(url, options)
            .then(res => res.json())
            .then(json => extractProjections(json, league))
    );

    return Promise.all(fetchPromises).then(results => {
        // Flatten the array of arrays into a single array of projections
        const allProjections = [].concat(...results);
        return allProjections;
    });
}
