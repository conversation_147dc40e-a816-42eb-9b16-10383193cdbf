// DabbleProvider.js

const axios = require('axios');
const HttpsProxyAgent = require('https-proxy-agent');
const { getProxy } = require('../utils/proxy');

async function fetchActiveCompetitions() {
  try {
    const proxy = getProxy('Dabble');
    if (!proxy) {
      return [];
    }

    const httpsAgent = new HttpsProxyAgent(proxy);

    const response = await axios({
      method: 'get',
      url: 'https://api.dabble.com/competitions/active/',
      headers: {
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.5',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      proxy: false,
      httpsAgent,
      timeout: 30000,
      maxRedirects: 5,
      validateStatus: null
    });

    if (response.status !== 200) {
      return [];
    }

    const data = response.data;

    if (!data || !data.data || !Array.isArray(data.data.activeCompetitions)) {
      return [];
    }

    const competitions = data.data.activeCompetitions.map(comp => comp.id);
    return competitions;
  } catch (error) {
    return [];
  }
}

async function fetchSportFixtures(competitionId) {
  try {
    const proxy = getProxy('Dabble');
    if (!proxy) {
      return null;
    }

    const httpsAgent = new HttpsProxyAgent(proxy);

    const response = await axios({
      method: 'get',
      url: `https://api.dabble.com/competitions/${competitionId}/sport-fixtures`,
      headers: {
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.5',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      proxy: false,
      httpsAgent,
      timeout: 30000,
      maxRedirects: 5,
      validateStatus: null
    });

    if (response.status !== 200) {
      return null;
    }

    return response.data;
  } catch (error) {
    return null;
  }
}

function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

function formatStatType(statType) {
  return statType
    .split('_') // Split by underscore
    .map(part =>
      part.split('-') // Split each part by dash
        .map(capitalizeFirstLetter) // Capitalize each word
        .join(' ') // Join words with space
    )
    .join(' '); // Join parts with space
}

function cleanPlayerName(playerName) {
  const assistsIndex = playerName.toLowerCase().indexOf(' assists');
  const pointsIndex = playerName.toLowerCase().indexOf(' points');
  
  if (assistsIndex !== -1) {
    playerName = playerName.substring(0, assistsIndex);
  }
  if (pointsIndex !== -1) {
    playerName = playerName.substring(0, pointsIndex);
  }
  
  return playerName.trim();
}

function extractProjectionData(fixture) {
  const projections = [];
  const { markets, selections } = fixture;

  if (!markets || !selections) {
    return projections;
  }

  const selectionMap = new Map();
  for (const selection of selections) {
    const selectionParts = selection.name.split(' ');
    const baseName = selectionParts.slice(0, -1).join(' ');
    if (!selectionMap.has(baseName)) {
      selectionMap.set(baseName, { over: null, under: null });
    }
    const group = selectionMap.get(baseName);
    if (selectionParts[selectionParts.length - 1] === 'over') {
      group.over = selection;
    } else if (selectionParts[selectionParts.length - 1] === 'under') {
      group.under = selection;
    }
  }

  let totalMarkets = 0;
  let filteredDisplayed = 0;
  let filteredLine = 0;
  let filteredSelections = 0;
  let filteredStatType = 0;

  for (const market of markets) {
    totalMarkets++;

    if (!market.isDisplayed || market.status !== 'open') {
      filteredDisplayed++;
      continue;
    }

    const marketParts = market.name.split(' ');
    const line = parseFloat(marketParts[marketParts.length - 1]);
    if (isNaN(line)) {
      filteredLine++;
      continue;
    }

    const overSelectionName = `${market.name} over`;
    const underSelectionName = `${market.name} under`;
    const selections = selectionMap.get(market.name);
    
    if (!selections || !selections.over || !selections.under) {
      filteredSelections++;
      continue;
    }

    let statTypeStartIndex = -1;
    const statTypeKeywords = [
      'passes', 'points', 'rebounds', 'assists', 'steals', 'blocks',
      'turnovers', 'threes', '3pt', 'goals', 'shots', 'saves',
      'strikeouts', 'hits', 'runs', 'yards', 'touchdowns', 'receptions',
      'tackles', 'sacks', 'interceptions', 'completions', 'attempts'
    ];

    for (let i = 0; i < marketParts.length - 1; i++) {
      if (marketParts[i].includes('-')) {
        statTypeStartIndex = i;
        break;
      }
    }

    if (statTypeStartIndex === -1) {
      for (let i = 0; i < marketParts.length - 1; i++) {
        const part = marketParts[i].toLowerCase();
        if (statTypeKeywords.some(keyword => part.includes(keyword))) {
          statTypeStartIndex = i;
          break;
        }
      }
    }

    if (statTypeStartIndex === -1) {
      filteredStatType++;
      continue;
    }

    let playerName = marketParts.slice(0, statTypeStartIndex).join(' ');
    playerName = cleanPlayerName(playerName);

    let roundNumber = '';
    const roundIndex = playerName.indexOf(' Round');
    if (roundIndex !== -1) {
      const roundPart = playerName.substring(roundIndex + 7);
      const match = roundPart.match(/^\d+/);
      if (match) {
        roundNumber = match[0];
      }
      playerName = playerName.substring(0, roundIndex).trim();
    }

    let statType = market.resultingType || marketParts.slice(statTypeStartIndex, -1).join(' ');
    let statTypeDisplay = formatStatType(statType);

    const competitionDisplayName = fixture.competition.displayName;
    let league = competitionDisplayName;
    if (statType.includes('first-quarter')) {
      league = `${league}1Q`;
    } else if (league === 'Euros' || league === 'MLS' || league === 'Copa' || league === 'EPL' || league === 'LaLiga' || league === 'Premier League' || league === 'Champions League') {
      league = 'SOCCER';
    } else if (league === 'Tennis M' || league === 'Tennis W') {
      league = 'TENNIS';
    } else if (league === 'UFC') {
      league = 'MMA';
    } else if (league === 'CBB' || league === 'College Basketball') {
      league = 'NCAAB';
    } else if (league === 'Home Run Derby') {
      league = 'HRDERBY';
      if (roundNumber) {
        statType += `-r${roundNumber}`;
        statTypeDisplay += ` R${roundNumber}`;
      }
    } else if (league === 'NBA' || league === 'National Basketball Association') {
      league = 'NBA';
    } else if (league === 'WNBA' || league === "Women's National Basketball Association") {
      league = 'WNBA';
    } else if (league === 'NFL' || league === 'National Football League') {
      league = 'NFL';
    } else if (league === 'MLB' || league === 'Major League Baseball') {
      league = 'MLB';
    } else if (league === 'NHL' || league === 'National Hockey League') {
      league = 'NHL';
    } else if (league === 'CRICKET' || league === 'Cricket') {
      league = 'CRICKET';
    } else if (league === 'CFB' || league === 'College Football') {
      league = 'CFB';
    }

    const projection = {
      proj_id: market.id,
      league: league,
      player_name: playerName,
      stat_type: statType,
      stat_type_display: statTypeDisplay,
      line: line,
      over_odds_american: '-122',
      under_odds_american: '-122',
      over_odds_decimal: 1.82,
      under_odds_decimal: 1.82,
      matchup: fixture.name,
      start_time: fixture.advertisedStart,
      source: 'Dabble'
    };

    projections.push(projection);
  }

  return projections;
}

async function fetchDabble() {
  const startTime = Date.now();
  
  const competitionIds = await fetchActiveCompetitions();
  
  let projections = [];

  const fixturePromises = competitionIds.map(id => fetchSportFixtures(id));

  const allFixturesData = await Promise.all(fixturePromises);

  allFixturesData.forEach((fixturesData, index) => {
    if (!fixturesData || !fixturesData.data || fixturesData.data.length === 0) {
      return;
    }

    const fixtures = fixturesData.data;

    fixtures.forEach(fixture => {
      if (!fixture.markets || fixture.markets.length === 0) {
        return;
      }
      
      const newProjections = extractProjectionData(fixture);
      projections = [...projections, ...newProjections];
    });
  });

  const endTime = Date.now();
  const duration = endTime - startTime;

  return projections;
}

module.exports = { fetchDabble };
