class PrizePicksAPI {
    constructor(proxyKey = 'PrizePicks') {
        this.proxyKey = proxyKey;
    }

    async fetchPrizePicks() {
        let client;
        try {
            // Presuming createClient/getProxy exist in scope or are imported
            client = await createClient();
            let proxyUrl = getProxy(this.proxyKey);

            while (true) {
                const response = await this.makeRequest(client, proxyUrl, headers);

                if (response && response.body.substring(0, 30) === '{"data":[{"type":"projection",') {
                    return this.transformProjections(JSON.parse(response.body));
                } else {
                    await client.terminate();
                    return [];
                }
            }
        } catch (error) {
            console.error('Error fetching PrizePicks projections:', error);
            throw error;
        } finally {
            if (client) {
                await client.terminate();
            }
        }
    }

    async makeRequest(client, proxyUrl, headers) {
        try {
            const proxy = getProxy(this.proxyKey);
            const response = await client.get(
                "https://api.prizepicks.com/projections?per_page=9999",
                {
                    headers,
                    proxyUrl: this.proxy || proxy
                }
            );
            return response;
        } catch (error) {
            console.error(`[PRIZEPICKS] Request error: ${error}`);
            return null;
        }
    }

    transformProjections(responseBody) {
        if (
            !responseBody ||
            !Array.isArray(responseBody.data) ||
            !Array.isArray(responseBody.included)
        ) {
            console.log('Invalid response body structure');
            return [];
        }

        // Add 'NHL SERIES' to allowedLeagues so that it does not get filtered out.
        const allowedLeagues = [
            'PGA', 'CRICKET', 'NHL', 'NHL SERIES', 'DARTS', 'SOCCER', 'EURO',
            'MLB', 'WNBA', 'LAX', 'TENNIS', 'MMA', 'OBBALL', 'OGOLF',
            'NFL', 'CFB', 'NBA', 'NBA1Q', 'CBB'
        ];

        const filteredProjections = responseBody.data.filter(proj => {
            if (proj.attributes.odds_type !== 'standard') {
                return false;
            }
            const playerInfo = this.getPlayerInfo(
                responseBody.included,
                proj.relationships.new_player.data.id
            );
            if (!playerInfo || !playerInfo.league) {
                return false;
            }
            if (proj.attributes.is_promo !== false) {
                return false;
            }
            // Keep 'NHL SERIES' if in allowedLeagues
            if (!allowedLeagues.includes(playerInfo.league)) {
                return false;
            }
            if (playerInfo.combo !== false) {
                return false;
            }
            return true;
        });

        return filteredProjections
            .map(proj => {
                const playerInfo = this.getPlayerInfo(
                    responseBody.included,
                    proj.relationships.new_player.data.id
                );
                if (!playerInfo) {
                    return null;
                }
                const startTime = new Date(proj.attributes.start_time);

                let stat_type = proj.attributes.stat_type;
                let league = playerInfo.league;
                const originalLeague = league;

                // Special re-map only if league is not NHL SERIES
                // so that we do NOT transform 'NHL SERIES' => 'NHL' or any other.
                if (originalLeague !== 'NHL SERIES') {
                    if (league === 'DARTS' && stat_type === "180's Thrown") {
                        stat_type = 'Player 180s';
                    }
                    if (['PGA', 'LIVGOLF', 'LPGA', 'EUROGOLF', 'OGOLF'].includes(league)) {
                        league = 'GOLF';
                    }
                    if (
                        league === 'HRDERBY' &&
                        ['Round 1', 'Round 2', 'Round 3'].includes(
                            proj.attributes.description
                        )
                    ) {
                        stat_type = `${stat_type} ${proj.attributes.description}`;
                    }
                    if (league === 'NFL1Q') {
                        league = 'NFL';
                        stat_type = `1Q ${stat_type}`;
                    }
                    if (league === 'CBB') {
                        league = 'NCAAB';
                    }
                } else {
                    // Force keep 'NHL SERIES' if originally that
                    league = 'NHL SERIES';
                }

                return {
                    proj_id: proj.id,
                    league: league,
                    player_name: playerInfo.name.trim(),
                    team: playerInfo.team,
                    stat_type: stat_type,
                    line: proj.attributes.line_score,
                    over_odds_american: '-119',
                    under_odds_american: '-119',
                    over_odds_decimal: 1.84,
                    under_odds_decimal: 1.84,
                    matchup: `${playerInfo.team} vs. ${proj.attributes.description}`,
                    start_time: startTime,
                    source: 'PrizePicks'
                };
            })
            .filter(Boolean);
    }

    getPlayerInfo(included, playerId) {
        if (!Array.isArray(included)) {
            console.error(
                "[PRIZEPICKS GETPLAYERINFO] 'included' is not an array."
            );
            return { league: '', name: '', team: '', combo: true };
        }
        const player = included.find(
            item => item.type === 'new_player' && item.id === playerId
        );
        if (!player || !player.attributes) { 
            console.warn(
                `[PRIZEPICKS GETPLAYERINFO] Player or attributes not found for id: ${playerId}`
            );
            return { league: '', name: '', team: '', combo: true };
        }
        return {
            league: player.attributes.league,
            name: player.attributes.display_name || player.attributes.name,
            team: player.attributes.team,
            combo: player.attributes.combo
        };
    }
}

module.exports = {
    PrizePicksAPI
};