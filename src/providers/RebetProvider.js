// Rebet.js
const fs = require('fs');
const { got } = require('got-cjs');
const { HttpsProxyAgent } = require('https-proxy-agent');
const path = require('path');
const { getProxy, reportProxySuccess, reportProxyFailure } = require('../utils/proxy');

const DEFAULT_PROXY_PATH = path.join(__dirname, 'proxies', 'proxies.txt');

// Performance optimization constants
const PERFORMANCE_CONFIG = {
    MAX_EXECUTION_TIME: 6000, // 6 seconds max (target: 5s with buffer)
    REQUEST_TIMEOUT: 8000, // Reduced from 30s to 8s
    MAX_RETRIES: 2, // Reduced from 3 to 2
    RETRY_DELAY: 300, // 300ms delay between retries
    MAX_CONCURRENT_LEAGUES: 4 // Process all leagues in parallel
};

function decimalToAmerican(decimalOdds) {
    if (decimalOdds >= 2) {
        return `+${Math.round((decimalOdds - 1) * 100)}`;
    } else {
        return `-${Math.round(100 / (decimalOdds - 1))}`;
    }
}

function extractProjections(events) {
    const projections = [];

    for (const event of events) {
        if (event.status !== "not_started") continue;

        const competitors = event.competitors.competitor;
        const homeTeam = competitors.find(c => c.qualifier === "home")?.abbreviation;
        const awayTeam = competitors.find(c => c.qualifier === "away")?.abbreviation;
        const matchup = `${awayTeam} vs. ${homeTeam}`;

        if (event.odds?.market) {
            for (const market of event.odds.market) {
                if (!market.specifiers?.includes("player=sr:player")) continue;

                const overOutcome = market.outcome?.find(o => o.name.startsWith("over"));
                const underOutcome = market.outcome?.find(o => o.name.startsWith("under"));
                if (!overOutcome || !underOutcome) continue;

                const line = parseFloat(overOutcome.name.split(" ")[1]);
                if (isNaN(line)) continue;

                const [lastName, firstName] = market.player_name.split(", ");
                const playerName = `${firstName} ${lastName}`;
                const overOdds = parseFloat(overOutcome.odds);
                const underOdds = parseFloat(underOutcome.odds);
                const statType = market.name.split(' ').slice(1).join(' ').split(' (')[0];
                const startTime = new Date(event.start_time);

                projections.push({
                    proj_id: market.m_id,
                    league: event.league_name,
                    player_name: playerName,
                    stat_type: statType,
                    line: line,
                    over_odds_american: decimalToAmerican(overOdds),
                    under_odds_american: decimalToAmerican(underOdds),
                    over_odds_decimal: overOdds,
                    under_odds_decimal: underOdds,
                    matchup: matchup,
                    start_time: startTime,
                    source: 'Rebet'
                });
            }
        }
    }

    return projections;
}

async function fetchGamesData(leagueName, startTime) {
    const tournament_id = leagues[leagueName];
    if (!tournament_id) {
        throw new Error(`Unknown league: ${leagueName}`);
    }

    const requestBody = {
        tournament_id,
        game_type: 1,
        custom_filter: false
    };

    let proxyUrl = null;
    try {
        // Check if we're approaching time limit
        const elapsed = Date.now() - startTime;
        if (elapsed > PERFORMANCE_CONFIG.MAX_EXECUTION_TIME * 0.8) { // 80% of max time
            console.warn(`[REBET] ${leagueName} approaching time limit (${elapsed}ms), skipping`);
            return [];
        }

        proxyUrl = await getProxy('Rebet');
        const agent = new HttpsProxyAgent(proxyUrl);

        const response = await got.post('https://api.rebet.app/prod/sportsbook-v3/load-sportsbook-data-v3', {
            json: requestBody,
            agent: { https: agent },
            headers: {
                'host': 'api.rebet.app',
                'accept': 'application/json, text/plain, */*',
                'content-type': 'application/json',
                'accept-encoding': 'gzip, deflate, br',
                'user-agent': 'rebetMobileApp/1034 CFNetwork/1568.300.101 Darwin/24.2.0',
                'accept-language': 'en-US,en;q=0.9'
            },
            responseType: 'json',
            http2: false,
            https: {
                rejectUnauthorized: false,
                checkServerIdentity: () => undefined
            },
            retry: { limit: 0 }, // Disable got's built-in retry since we're handling it
            timeout: { request: PERFORMANCE_CONFIG.REQUEST_TIMEOUT }
        });

        if (response.statusCode !== 200) {
            const error = new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`);
            reportProxyFailure(proxyUrl, error);
            throw error;
        }

        // Success - report proxy success
        reportProxySuccess(proxyUrl);

        let projections = [];
        projections = extractProjections(response.body.data.events);

        return projections;
    } catch (error) {
        if (proxyUrl) {
            reportProxyFailure(proxyUrl, error);
        }
        throw new Error(`[REBET] ${leagueName}: ${error.message}`);
    }
}

async function fetchLeagueWithRetry(leagueName, startTime) {
    for (let attempt = 0; attempt < PERFORMANCE_CONFIG.MAX_RETRIES; attempt++) {
        try {
            // Check if we're approaching time limit
            const elapsed = Date.now() - startTime;
            if (elapsed > PERFORMANCE_CONFIG.MAX_EXECUTION_TIME * 0.8) { // 80% of max time
                console.warn(`[REBET] ${leagueName} approaching time limit (${elapsed}ms), stopping retries`);
                return [];
            }

            const projections = await fetchGamesData(leagueName, startTime);
            console.log(`[REBET] ${leagueName} completed successfully with ${projections.length} projections`);
            return projections;
        } catch (error) {
            console.error(`[REBET] Failed attempt ${attempt + 1}/${PERFORMANCE_CONFIG.MAX_RETRIES} for ${leagueName} - ${error.message}`);

            if (attempt === PERFORMANCE_CONFIG.MAX_RETRIES - 1) {
                console.error(`[REBET] All attempts failed for ${leagueName}`);
                return [];
            }

            // Add delay before retry
            if (attempt < PERFORMANCE_CONFIG.MAX_RETRIES - 1) {
                await new Promise(resolve => setTimeout(resolve, PERFORMANCE_CONFIG.RETRY_DELAY));
            }
        }
    }
    return [];
}

async function fetchRebet() {
    const startTime = Date.now();

    try {
        console.log('[REBET] Starting optimized fetch for all leagues...');

        // Process all leagues in parallel for better performance
        const leagueNames = Object.keys(leagues);
        const leaguePromises = leagueNames.map(leagueName =>
            fetchLeagueWithRetry(leagueName, startTime)
        );

        // Wait for all leagues to complete
        const leagueResults = await Promise.all(leaguePromises);

        // Flatten all projections
        const allProjections = leagueResults.flat();

        const duration = Date.now() - startTime;
        console.log(`[REBET] Completed fetch in ${duration}ms with ${allProjections.length} projections`);

        // Log performance warning if over target
        if (duration > PERFORMANCE_CONFIG.MAX_EXECUTION_TIME) {
            console.warn(`[REBET] Performance warning: Execution took ${duration}ms (target: ${PERFORMANCE_CONFIG.MAX_EXECUTION_TIME}ms)`);
        }

        return allProjections;
    } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`[REBET] Failed after ${duration}ms: ${error.message}`);
        throw error;
    }
}

const leagues = {
    // 'NFL': 'sr:tournament:31',
    'NBA': 'sr:tournament:132',
    // CFB': 'sr:tournament:27653',
    'NCAAB': 'sr:tournament:648',
    'NHL': 'sr:tournament:234',
    'MLB': 'sr:tournament:109'
};

module.exports = { fetchRebet };