const fs = require('fs');

const LEAGUE_MAP = {
  'Sport:NrEf9A': 'LOL',
  'Sport:A0mfMN': 'CS2',
  'Sport:VRLfwA': 'GOLF',
  'League:MmDCpL': 'MLB',
  'League:xq1CKZ': 'WNBA',
  'League:ENzCaE': 'NFL',
  'League:dKC2w0': 'CFB',
  'League:MRGCpm': 'NHL',
  'League:xVBCgx': 'NBA',
  'League:LzmCdx': 'NCAAB',
};

function mapLeague(sportId, leagueId) {
  return LEAGUE_MAP[sportId] || LEAGUE_MAP[leagueId] || null;
}

function modifyStatType(statType, minGames) {
  if (minGames > 1) {
    return `MAPS 1-${minGames} ${statType}`;
  } else if (minGames === 1) {
    return `MAP 1 ${statType}`;
  }
  return statType;
}

function findGame(games, opponentId) {
  return Object.entries(games).find(([, game]) => 
    game.opponents && opponentId in game.opponents
  );
}

function decimalToAmericanOdds(decimalOdds) {
    if (decimalOdds >= 2) {
      return '+' + Math.round((decimalOdds - 1) * 100);
    } else {
      return Math.round(-100 / (decimalOdds - 1)).toString();
    }
  }

function extractProjections(data) {
  const projections = [];

  for (const [participantId, participant] of Object.entries(data.board)) {
    const opponentId = participant.opponent;
    const gameEntry = findGame(data.games, opponentId);

    if (!gameEntry) {
      console.warn(`No game found for participant: ${participant.name}`);
      continue;
    }

    const [gameId, game] = gameEntry;

    const leagueId = game.league;
    const league = mapLeague(data.leagues[leagueId].sport, leagueId);
    if (!league) {
      console.warn(`Unsupported league for game: ${leagueId}`);
      continue;
    }

    const awayTeam = Object.values(game.opponents).find(opp => opp.designation === 'away');
    const homeTeam = Object.values(game.opponents).find(opp => opp.designation === 'home');
    
    if (!awayTeam || !homeTeam) {
      console.warn(`Missing team information for game: ${gameId}`);
      continue;
    }

    const matchup = `${awayTeam.alias} vs. ${homeTeam.alias}`;

    for (const [marketId, market] of Object.entries(participant.markets)) {
      const [, statType, period] = marketId.split(',');
      let leagueName = league;

      if (period === 'quarter') {
        leagueName += '1Q';
      } else if (period === 'half') {
        leagueName += '1H';
      }

      const modifiedStatType = modifyStatType(market.category, game.minimum_number_of_games);

      projections.push({
        proj_id: marketId,
        league: leagueName,
        player_name: participant.name,
        stat_type: modifiedStatType,
        line: market.line,
        over_odds_decimal: market.odds[1],
        under_odds_decimal: market.odds[0],
        over_odds_american: decimalToAmericanOdds(market.odds[1]),
        under_odds_american: decimalToAmericanOdds(market.odds[0]),
        matchup: matchup,
        start_time: new Date(game.scheduled_at),
        source: 'Hotstreak'
      });
    }
  }

  return projections;
}

async function fetchHotstreak() {
  const url = 'https://api.hotstreak.gg/board.json?api_key=********************************';

  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return extractProjections(data);
  } catch (error) {
    console.error('Error fetching data:', error);
    return [];
  }
}

module.exports = { fetchHotstreak };
/*
// For testing purposes
if (require.main === module) {
  fetchHotstreak().then(projections => {
    //console.log(JSON.stringify(projections, null, 2));
    fs.writeFileSync('projections.json', JSON.stringify(projections, null, 2));
  });
}*/