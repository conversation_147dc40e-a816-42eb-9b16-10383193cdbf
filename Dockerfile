# Build stage
FROM node:20-bullseye AS builder

# Install Python 3 and pip
RUN apt-get update && apt-get install -y \
    python3 \
    python3-venv \
    python3-pip \
    make \
    g++ \
    wget \
    libnss3 \
    libnspr4 \
    libdbus-1-3 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libasound2 \
    xvfb \
    fonts-liberation \
    fonts-noto-color-emoji \
    chromium \
    chromium-driver \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy package files and install Node dependencies
COPY package*.json ./
COPY tsconfig.json ./
COPY src ./src
COPY .env ./
COPY src/api/prizepicks-api/package*.json ./src/api/prizepicks-api/
RUN npm install

# Set up Python virtual environment and install dependencies
COPY requirements.txt ./
RUN python3 -m venv /opt/venv && \
    . /opt/venv/bin/activate && \
    pip install -r requirements.txt && \
    mkdir -p /tmp/.chromium && \
    chmod -R 777 /tmp/.chromium && \
    python3 -m camoufox fetch

# Build TypeScript
RUN npm run build

# Production stage
FROM node:20-bullseye AS runner

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-venv \
    python3-pip \
    wget \
    libnss3 \
    libnspr4 \
    libdbus-1-3 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libasound2 \
    xvfb \
    fonts-liberation \
    fonts-noto-color-emoji \
    chromium \
    chromium-driver \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy node modules and built files
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/src ./src
COPY --from=builder /app/.env ./.env
COPY package*.json ./
COPY tsconfig.json ./

# Copy Python virtual environment
COPY --from=builder /opt/venv /opt/venv
COPY --from=builder /tmp/.chromium /tmp/.chromium

# Set up environment variables
ENV PATH="/opt/venv/bin:$PATH"
ENV PYTHONPATH="/app"
ENV DISPLAY=:99
ENV CHROMIUM_PATH=/usr/bin/chromium
ENV CHROMIUM_USER_DATA_DIR=/tmp/chromium-data

# Create necessary directories and set permissions
RUN mkdir -p /tmp/chromium-data && \
    chmod 777 /tmp/chromium-data && \
    chmod -R 777 /tmp/.chromium

# Create a non-root user
RUN useradd -m -u 2000 scraper && \
    chown -R scraper:scraper /app /tmp/.chromium /tmp/chromium-data /opt/venv

# Switch to non-root user
USER scraper

# Command to run the application with Xvfb
CMD ["sh","-c","rm -f /tmp/.X99-lock && \
              mkdir -p /tmp/.X11-unix && chmod 1777 /tmp/.X11-unix && \
              Xvfb :99 -screen 0 1024x768x24 & \
              npm run prod"]