{"name": "prizepicks", "version": "1.0.0", "description": "", "main": "main.js", "scripts": {"start": "npm run dev", "prod": "node ./dist/main.js", "tsc": "tsc -p tsconfig.json", "dev": "ts-node-dev --respawn --pretty src/main.ts", "build": "npm run clean && npm run tsc && npm run copy-files", "clean": "rimraf ./dist", "copy-files": "copyfiles -u 1 \"./src/**/*.txt\" \"./src/**/*.json\" \"./src/**/*.py\" dist/", "postinstall": "if [ -d \"src/api/prizepicks-api\" ]; then cd src/api/prizepicks-api && npm install; else echo \"prizepicks-api directory not found\"; fi", "setup-python": "python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt && playwright install", "prizepicks-fetch": "./venv/bin/python3 src/providers/PrizePicks.py"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": "20.x"}, "dependencies": {"axios": "^1.7.9", "cycletls": "^1.0.27", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "form-data": "^4.0.1", "got-cjs": "^12.5.4", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^5.0.1", "jstat": "^1.9.6", "lodash": "^4.17.21", "luxon": "^3.5.0", "mathjs": "^14.1.0", "moment-timezone": "^0.5.46", "mongodb": "^6.12.0", "node-fetch": "^2.7.0", "p-limit": "^3.1.0", "rimraf": "^6.0.1", "string-similarity": "^4.0.4", "tiny-async-pool": "^2.1.0", "tlsclientwrapper": "^3.0.3", "toad-scheduler": "^3.0.1", "ts-node-dev": "^2.0.0", "undici": "^7.3.0", "uuid": "^11.0.5", "ws": "^8.18.0"}, "devDependencies": {"@eslint/config-array": "^0.19.1", "@eslint/object-schema": "^2.1.5", "@types/node": "^22.10.10", "@types/node-fetch": "^2.6.12", "@types/ws": "^8.5.14", "@typescript-eslint/parser": "^8.21.0", "chai": "^5.1.2", "copyfiles": "^2.4.1", "eslint": "^9.19.0", "mocha": "^11.1.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}