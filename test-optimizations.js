#!/usr/bin/env node

/**
 * Test script to validate the implemented optimizations
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Testing Optimization Implementations...\n');

// Test 1: Verify HardRock.js no longer uses Python processes
function testHardRockOptimization() {
    console.log('1. Testing HardRock.js Python Process Elimination...');
    
    const hardRockPath = path.join(__dirname, 'src/providers/HardRock.js');
    const content = fs.readFileSync(hardRockPath, 'utf8');
    
    const hasPythonSpawn = content.includes('spawn(') && content.includes('python');
    const hasCycleTLS = content.includes('initCycleTLS') || content.includes('cycletls');
    
    if (!hasPythonSpawn && hasCycleTLS) {
        console.log('   ✅ HardRock.js successfully refactored to use cycletls directly');
    } else {
        console.log('   ❌ HardRock.js still contains Python process spawning');
    }
    
    return !hasPythonSpawn && hasCycleTLS;
}

// Test 2: Verify Thrillzz.js uses parallel processing
function testThrillzzOptimization() {
    console.log('2. Testing Thrillzz.js Parallel Processing...');
    
    const thrillzzPath = path.join(__dirname, 'src/api/thrillzz/index.js');
    const content = fs.readFileSync(thrillzzPath, 'utf8');
    
    const hasParallelProcessing = content.includes('processEventsInParallel') || content.includes('Promise.allSettled');
    const hasPLimit = content.includes('pLimit') || content.includes('p-limit');
    const hasSequentialProcessing = content.includes('currentEventIndex') && content.includes('while');
    
    if (hasParallelProcessing && hasPLimit && !hasSequentialProcessing) {
        console.log('   ✅ Thrillzz.js successfully refactored for parallel processing');
    } else {
        console.log('   ❌ Thrillzz.js still uses sequential processing');
    }
    
    return hasParallelProcessing && hasPLimit && !hasSequentialProcessing;
}

// Test 3: Verify HTTP client standardization
function testHttpClientStandardization() {
    console.log('3. Testing HTTP Client Standardization...');
    
    const filesToCheck = [
        'src/providers/Pick6.js',
        'src/providers/EpickProvider.ts',
        'src/api/fliff-api/index.js'
    ];
    
    let allUseUndici = true;
    let foundAxios = false;
    
    filesToCheck.forEach(filePath => {
        const fullPath = path.join(__dirname, filePath);
        if (fs.existsSync(fullPath)) {
            const content = fs.readFileSync(fullPath, 'utf8');
            
            if (content.includes('require(\'axios\')') || content.includes('import axios')) {
                foundAxios = true;
                allUseUndici = false;
                console.log(`   ❌ ${filePath} still uses axios`);
            } else if (content.includes('undici') || content.includes('fetch')) {
                console.log(`   ✅ ${filePath} uses undici/fetch`);
            }
        }
    });
    
    if (allUseUndici && !foundAxios) {
        console.log('   ✅ HTTP clients successfully standardized to undici');
    }
    
    return allUseUndici && !foundAxios;
}

// Test 4: Verify concurrency limit increase
function testConcurrencyIncrease() {
    console.log('4. Testing Main Concurrency Limit Increase...');
    
    const mainPath = path.join(__dirname, 'src/main.ts');
    const content = fs.readFileSync(mainPath, 'utf8');
    
    const hasHighConcurrency = content.includes('pLimit(50)') || content.includes('pLimit(40)') || content.includes('pLimit(30)');
    const hasLowConcurrency = content.includes('pLimit(15)') || content.includes('pLimit(10)');
    
    if (hasHighConcurrency && !hasLowConcurrency) {
        console.log('   ✅ Main concurrency limit successfully increased');
    } else {
        console.log('   ❌ Main concurrency limit not increased');
    }
    
    return hasHighConcurrency && !hasLowConcurrency;
}

// Test 5: Verify Fanduel pagination optimization
function testFanduelPaginationOptimization() {
    console.log('5. Testing Fanduel Pagination Optimization...');
    
    const fanduelPath = path.join(__dirname, 'src/api/fanduel-sharp-api/index.js');
    const content = fs.readFileSync(fanduelPath, 'utf8');
    
    const hasParallelPagination = content.includes('Promise.allSettled') && content.includes('remainingPagePromises');
    const hasSequentialPagination = content.includes('while (hasMoreResults') && content.includes('pageCount < maxPages');
    
    if (hasParallelPagination && !hasSequentialPagination) {
        console.log('   ✅ Fanduel pagination successfully parallelized');
    } else {
        console.log('   ❌ Fanduel still uses sequential pagination');
    }
    
    return hasParallelPagination && !hasSequentialPagination;
}

// Test 6: Verify Proxy Manager implementation
function testProxyManagerImplementation() {
    console.log('6. Testing Smart Proxy Manager Implementation...');
    
    const proxyManagerPath = path.join(__dirname, 'src/utils/proxy/ProxyManager.js');
    const proxyIndexPath = path.join(__dirname, 'src/utils/proxy/index.ts');
    
    const proxyManagerExists = fs.existsSync(proxyManagerPath);
    
    if (proxyManagerExists) {
        const managerContent = fs.readFileSync(proxyManagerPath, 'utf8');
        const indexContent = fs.readFileSync(proxyIndexPath, 'utf8');
        
        const hasHealthChecks = managerContent.includes('healthCheck') && managerContent.includes('isHealthy');
        const hasFailureTracking = managerContent.includes('reportFailure') && managerContent.includes('failures');
        const hasProxyManager = indexContent.includes('proxyManager');
        
        if (hasHealthChecks && hasFailureTracking && hasProxyManager) {
            console.log('   ✅ Smart Proxy Manager successfully implemented');
        } else {
            console.log('   ❌ Proxy Manager missing key features');
        }
        
        return hasHealthChecks && hasFailureTracking && hasProxyManager;
    } else {
        console.log('   ❌ Proxy Manager file not found');
        return false;
    }
}

// Test 7: Verify build success
function testBuildSuccess() {
    console.log('7. Testing Build Success...');
    
    const distPath = path.join(__dirname, 'dist');
    const buildExists = fs.existsSync(distPath);
    
    if (buildExists) {
        const files = fs.readdirSync(distPath);
        const hasMainFile = files.some(file => file.includes('main'));
        
        if (hasMainFile) {
            console.log('   ✅ Build successful - dist directory contains compiled files');
            return true;
        }
    }
    
    console.log('   ❌ Build failed or dist directory missing');
    return false;
}

// Run all tests
async function runAllTests() {
    const results = [];
    
    results.push(testHardRockOptimization());
    results.push(testThrillzzOptimization());
    results.push(testHttpClientStandardization());
    results.push(testConcurrencyIncrease());
    results.push(testFanduelPaginationOptimization());
    results.push(testProxyManagerImplementation());
    results.push(testBuildSuccess());
    
    const passedTests = results.filter(result => result).length;
    const totalTests = results.length;
    
    console.log('\n📊 Test Results Summary:');
    console.log(`   Passed: ${passedTests}/${totalTests} tests`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('\n🎉 All optimizations successfully implemented!');
        console.log('   Expected performance improvement: 70-85% reduction in execution time');
        console.log('   Target: Sub-5-second scraper execution');
    } else {
        console.log('\n⚠️  Some optimizations need attention');
        console.log('   Review failed tests above');
    }
    
    return passedTests === totalTests;
}

// Execute tests
if (require.main === module) {
    runAllTests().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { runAllTests };
